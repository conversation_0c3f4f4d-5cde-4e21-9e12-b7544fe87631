import React, { useState } from 'react';
import { 
  Shield, 
  User, 
  Clock, 
  Search, 
  Filter, 
  Download, 
  Eye,
  Edit,
  Trash2,
  Plus,
  Settings,
  LogIn,
  LogOut,
  UserPlus,
  UserMinus,
  FileText,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Calendar,
  MapPin,
  Smartphone,
  Monitor,
  Globe
} from 'lucide-react';

interface AuditLogEntry {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  userRole: string;
  action: string;
  actionType: 'create' | 'update' | 'delete' | 'login' | 'logout' | 'view' | 'export' | 'system';
  resource: string;
  resourceId?: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  location?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'success' | 'failed' | 'warning';
}

const AuditLog = () => {
  const [logs, setLogs] = useState<AuditLogEntry[]>([
    {
      id: '1',
      timestamp: '2024-01-20T14:30:00Z',
      userId: 'admin001',
      userName: 'أحمد المدير',
      userRole: 'مدير النظام',
      action: 'تسجيل دخول',
      actionType: 'login',
      resource: 'لوحة التحكم',
      details: 'تسجيل دخول ناجح إلى لوحة التحكم الإدارية',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'الدار البيضاء، المغرب',
      severity: 'low',
      status: 'success'
    },
    {
      id: '2',
      timestamp: '2024-01-20T14:25:00Z',
      userId: 'manager002',
      userName: 'فاطمة الإدارية',
      userRole: 'مدير المطاعم',
      action: 'إضافة مطعم جديد',
      actionType: 'create',
      resource: 'المطاعم',
      resourceId: 'rest_123',
      details: 'تم إضافة مطعم "الأصالة" بنجاح مع جميع التفاصيل والوثائق',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      location: 'الرباط، المغرب',
      severity: 'medium',
      status: 'success'
    },
    {
      id: '3',
      timestamp: '2024-01-20T14:20:00Z',
      userId: 'support003',
      userName: 'محمد الدعم',
      userRole: 'دعم فني',
      action: 'حذف شكوى عميل',
      actionType: 'delete',
      resource: 'الشكاوى',
      resourceId: 'comp_456',
      details: 'تم حذف شكوى العميل رقم #456 بعد حلها',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
      location: 'فاس، المغرب',
      severity: 'medium',
      status: 'success'
    },
    {
      id: '4',
      timestamp: '2024-01-20T14:15:00Z',
      userId: 'hacker001',
      userName: 'مستخدم مجهول',
      userRole: 'غير محدد',
      action: 'محاولة دخول غير مصرح',
      actionType: 'login',
      resource: 'لوحة التحكم',
      details: 'محاولة دخول فاشلة بكلمة مرور خاطئة - تم حظر IP',
      ipAddress: '45.123.456.789',
      userAgent: 'curl/7.68.0',
      location: 'غير محدد',
      severity: 'critical',
      status: 'failed'
    },
    {
      id: '5',
      timestamp: '2024-01-20T14:10:00Z',
      userId: 'admin001',
      userName: 'أحمد المدير',
      userRole: 'مدير النظام',
      action: 'تصدير تقرير مالي',
      actionType: 'export',
      resource: 'التقارير المالية',
      resourceId: 'report_789',
      details: 'تم تصدير التقرير المالي لشهر يناير 2024 بصيغة PDF',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'الدار البيضاء، المغرب',
      severity: 'medium',
      status: 'success'
    }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [filterAction, setFilterAction] = useState('all');
  const [filterSeverity, setFilterSeverity] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedDateRange, setSelectedDateRange] = useState('today');

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'create': return <Plus className="w-4 h-4" />;
      case 'update': return <Edit className="w-4 h-4" />;
      case 'delete': return <Trash2 className="w-4 h-4" />;
      case 'login': return <LogIn className="w-4 h-4" />;
      case 'logout': return <LogOut className="w-4 h-4" />;
      case 'view': return <Eye className="w-4 h-4" />;
      case 'export': return <Download className="w-4 h-4" />;
      case 'system': return <Settings className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityLabel = (severity: string) => {
    switch (severity) {
      case 'low': return 'منخفض';
      case 'medium': return 'متوسط';
      case 'high': return 'عالي';
      case 'critical': return 'حرج';
      default: return severity;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default: return <CheckCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'success': return 'نجح';
      case 'failed': return 'فشل';
      case 'warning': return 'تحذير';
      default: return status;
    }
  };

  const getDeviceIcon = (userAgent: string) => {
    if (userAgent.includes('iPhone') || userAgent.includes('Android')) {
      return <Smartphone className="w-4 h-4" />;
    } else if (userAgent.includes('Windows') || userAgent.includes('Mac')) {
      return <Monitor className="w-4 h-4" />;
    } else {
      return <Globe className="w-4 h-4" />;
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         log.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         log.resource.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         log.details.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesAction = filterAction === 'all' || log.actionType === filterAction;
    const matchesSeverity = filterSeverity === 'all' || log.severity === filterSeverity;
    const matchesStatus = filterStatus === 'all' || log.status === filterStatus;
    
    return matchesSearch && matchesAction && matchesSeverity && matchesStatus;
  });

  const exportLogs = (format: 'csv' | 'pdf') => {
    alert(`تصدير سجل العمليات بصيغة ${format.toUpperCase()}`);
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString('ar-MA'),
      time: date.toLocaleTimeString('ar-MA', { hour12: false })
    };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">سجل العمليات</h2>
          <p className="text-gray-600 rtl-font">تتبع جميع العمليات والأنشطة في النظام</p>
        </div>
        
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button
            onClick={() => exportLogs('csv')}
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Download className="w-4 h-4" />
            <span className="rtl-font">CSV</span>
          </button>
          <button
            onClick={() => exportLogs('pdf')}
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <Download className="w-4 h-4" />
            <span className="rtl-font">PDF</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">العمليات اليوم</p>
              <p className="text-2xl font-bold text-blue-600">
                {logs.filter(log => {
                  const today = new Date().toDateString();
                  const logDate = new Date(log.timestamp).toDateString();
                  return today === logDate;
                }).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">العمليات الناجحة</p>
              <p className="text-2xl font-bold text-green-600">
                {logs.filter(log => log.status === 'success').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">العمليات الفاشلة</p>
              <p className="text-2xl font-bold text-red-600">
                {logs.filter(log => log.status === 'failed').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
              <XCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">تنبيهات حرجة</p>
              <p className="text-2xl font-bold text-orange-600">
                {logs.filter(log => log.severity === 'critical').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="البحث في السجل..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
            />
          </div>
          
          <select
            value={filterAction}
            onChange={(e) => setFilterAction(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="all">جميع العمليات</option>
            <option value="create">إنشاء</option>
            <option value="update">تحديث</option>
            <option value="delete">حذف</option>
            <option value="login">تسجيل دخول</option>
            <option value="logout">تسجيل خروج</option>
            <option value="view">عرض</option>
            <option value="export">تصدير</option>
          </select>
          
          <select
            value={filterSeverity}
            onChange={(e) => setFilterSeverity(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="all">جميع المستويات</option>
            <option value="low">منخفض</option>
            <option value="medium">متوسط</option>
            <option value="high">عالي</option>
            <option value="critical">حرج</option>
          </select>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="all">جميع الحالات</option>
            <option value="success">نجح</option>
            <option value="failed">فشل</option>
            <option value="warning">تحذير</option>
          </select>
          
          <select
            value={selectedDateRange}
            onChange={(e) => setSelectedDateRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="all">جميع الفترات</option>
          </select>
        </div>
        
        <div className="mt-4 text-sm text-gray-600 rtl-font">
          عرض {filteredLogs.length} من {logs.length} عملية
        </div>
      </div>

      {/* Logs Table */}
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الوقت</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">المستخدم</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">العملية</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">المورد</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">التفاصيل</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">المستوى</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الحالة</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الموقع</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredLogs.map((log) => {
                const { date, time } = formatTimestamp(log.timestamp);
                return (
                  <tr key={log.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        <div className="font-medium text-gray-900">{time}</div>
                        <div className="text-gray-500">{date}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                          <User className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 rtl-font">{log.userName}</div>
                          <div className="text-sm text-gray-500 rtl-font">{log.userRole}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        {getActionIcon(log.actionType)}
                        <span className="text-sm text-gray-900 rtl-font">{log.action}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 rtl-font">{log.resource}</div>
                      {log.resourceId && (
                        <div className="text-xs text-gray-500">#{log.resourceId}</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 rtl-font max-w-xs truncate" title={log.details}>
                        {log.details}
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                        {getDeviceIcon(log.userAgent)}
                        <span className="text-xs text-gray-500">{log.ipAddress}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(log.severity)}`}>
                        {getSeverityLabel(log.severity)}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        {getStatusIcon(log.status)}
                        <span className="text-sm text-gray-900 rtl-font">{getStatusLabel(log.status)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-500 rtl-font">{log.location || 'غير محدد'}</span>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AuditLog;
