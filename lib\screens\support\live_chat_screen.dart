import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';

/// Live Chat screen for real-time support
class LiveChatScreen extends StatefulWidget {
  const LiveChatScreen({super.key});

  @override
  State<LiveChatScreen> createState() => _LiveChatScreenState();
}

class _LiveChatScreenState extends State<LiveChatScreen> {
  final _messageController = TextEditingController();
  final _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  bool _isTyping = false;
  bool _isConnected = false;

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeChat() {
    // Simulate connection
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() => _isConnected = true);
        _addWelcomeMessage();
      }
    });
  }

  void _addWelcomeMessage() {
    final languageProvider = context.read<LanguageProvider>();
    final welcomeMessage = ChatMessage(
      text: languageProvider.getText(
        'Hello! I\'m Sarah from Wasslti support. How can I help you today?',
        'مرحباً! أنا سارة من دعم وصلتي. كيف يمكنني مساعدتك اليوم؟'
      ),
      isUser: false,
      timestamp: DateTime.now(),
      agentName: languageProvider.getText('Sarah', 'سارة'),
      agentAvatar: '👩‍💼',
    );
    
    setState(() {
      _messages.add(welcomeMessage);
    });
    _scrollToBottom();
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    final message = ChatMessage(
      text: _messageController.text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(message);
      _isTyping = true;
    });

    _messageController.clear();
    _scrollToBottom();

    // Simulate agent response
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _addAgentResponse(message.text);
      }
    });
  }

  void _addAgentResponse(String userMessage) {
    final languageProvider = context.read<LanguageProvider>();
    String response;

    // Simple response logic based on keywords
    if (userMessage.toLowerCase().contains('payment') || 
        userMessage.toLowerCase().contains('money') ||
        userMessage.toLowerCase().contains('دفع') ||
        userMessage.toLowerCase().contains('مال')) {
      response = languageProvider.getText(
        'I can help you with payment issues. Payments are processed weekly on Tuesdays. You can check your earnings in the app. Is there a specific payment issue you\'re experiencing?',
        'يمكنني مساعدتك في مشاكل الدفع. تتم معالجة المدفوعات أسبوعياً يوم الثلاثاء. يمكنك التحقق من أرباحك في التطبيق. هل هناك مشكلة دفع محددة تواجهها؟'
      );
    } else if (userMessage.toLowerCase().contains('trip') || 
               userMessage.toLowerCase().contains('ride') ||
               userMessage.toLowerCase().contains('رحلة') ||
               userMessage.toLowerCase().contains('سفر')) {
      response = languageProvider.getText(
        'I\'m here to help with trip-related questions. Are you having trouble with accepting trips, navigation, or something else? Please provide more details.',
        'أنا هنا لمساعدتك في الأسئلة المتعلقة بالرحلات. هل تواجه مشكلة في قبول الرحلات أو التنقل أو شيء آخر؟ يرجى تقديم المزيد من التفاصيل.'
      );
    } else if (userMessage.toLowerCase().contains('account') || 
               userMessage.toLowerCase().contains('profile') ||
               userMessage.toLowerCase().contains('حساب') ||
               userMessage.toLowerCase().contains('ملف')) {
      response = languageProvider.getText(
        'For account and profile issues, you can update most information in Settings > Account. If you need help with verification or have other account concerns, I\'m here to assist.',
        'لمشاكل الحساب والملف الشخصي، يمكنك تحديث معظم المعلومات في الإعدادات > الحساب. إذا كنت تحتاج مساعدة في التحقق أو لديك مخاوف أخرى بشأن الحساب، أنا هنا للمساعدة.'
      );
    } else {
      response = languageProvider.getText(
        'Thank you for your message. I understand your concern and I\'m here to help. Could you please provide more specific details about the issue you\'re experiencing?',
        'شكراً لك على رسالتك. أفهم قلقك وأنا هنا للمساعدة. هل يمكنك تقديم تفاصيل أكثر تحديداً حول المشكلة التي تواجهها؟'
      );
    }

    final agentMessage = ChatMessage(
      text: response,
      isUser: false,
      timestamp: DateTime.now(),
      agentName: languageProvider.getText('Sarah', 'سارة'),
      agentAvatar: '👩‍💼',
    );

    setState(() {
      _isTyping = false;
      _messages.add(agentMessage);
    });
    _scrollToBottom();
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: const Color(0xFF11B96F),
          foregroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Row(
            children: [
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: const Center(
                  child: Text('👩‍💼', style: TextStyle(fontSize: 18)),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageProvider.getText('Live Support', 'الدعم المباشر'),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      _isConnected
                          ? languageProvider.getText('Online', 'متصل')
                          : languageProvider.getText('Connecting...', 'جاري الاتصال...'),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () => _showChatOptions(),
            ),
          ],
        ),
        body: Column(
          children: [
            // Connection Status
            if (!_isConnected)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                color: Colors.orange.withValues(alpha: 0.1),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      languageProvider.getText(
                        'Connecting to support agent...',
                        'جاري الاتصال بوكيل الدعم...'
                      ),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

            // Messages List
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: _messages.length + (_isTyping ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == _messages.length && _isTyping) {
                    return _buildTypingIndicator(isDark, languageProvider);
                  }
                  return _buildMessageBubble(_messages[index], isDark, languageProvider);
                },
              ),
            ),

            // Message Input
            _buildMessageInput(isDark, languageProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message, bool isDark, LanguageProvider languageProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!message.isUser) ...[
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: const Color(0xFF11B96F),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Text(
                  message.agentAvatar ?? '🤖',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: message.isUser
                    ? const Color(0xFF11B96F)
                    : (isDark ? const Color(0xFF1B3B2E) : Colors.white),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(message.isUser ? 16 : 4),
                  bottomRight: Radius.circular(message.isUser ? 4 : 16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!message.isUser && message.agentName != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        message.agentName!,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF11B96F),
                        ),
                      ),
                    ),
                  Text(
                    message.text,
                    style: TextStyle(
                      fontSize: 15,
                      color: message.isUser
                          ? Colors.white
                          : (isDark ? Colors.white : Colors.black87),
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      fontSize: 11,
                      color: message.isUser
                          ? Colors.white70
                          : (isDark ? Colors.grey[400] : Colors.grey[600]),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Center(
                child: Text('👤', style: TextStyle(fontSize: 16)),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator(bool isDark, LanguageProvider languageProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFF11B96F),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(
              child: Text('👩‍💼', style: TextStyle(fontSize: 16)),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
                bottomLeft: Radius.circular(4),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  languageProvider.getText('Sarah is typing', 'سارة تكتب'),
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isDark ? Colors.grey[400]! : Colors.grey[600]!,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                ),
              ),
              child: TextField(
                controller: _messageController,
                style: TextStyle(
                  fontSize: 16,
                  color: isDark ? Colors.white : Colors.black87,
                ),
                decoration: InputDecoration(
                  hintText: languageProvider.getText(
                    'Type your message...',
                    'اكتب رسالتك...'
                  ),
                  hintStyle: TextStyle(
                    color: isDark ? Colors.grey[400] : Colors.grey[500],
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              width: 48,
              height: 48,
              decoration: const BoxDecoration(
                color: Color(0xFF11B96F),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.send,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return context.read<LanguageProvider>().getText('Just now', 'الآن');
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
    } else {
      return '${time.day}/${time.month}';
    }
  }

  void _showChatOptions() {
    final languageProvider = context.read<LanguageProvider>();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF1B3B2E)
              : Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.email_outlined, color: Color(0xFF11B96F)),
              title: Text(languageProvider.getText('Email Transcript', 'إرسال النسخة بالبريد')),
              onTap: () {
                Navigator.pop(context);
                _emailTranscript();
              },
            ),
            ListTile(
              leading: const Icon(Icons.star_outline, color: Colors.orange),
              title: Text(languageProvider.getText('Rate Support', 'تقييم الدعم')),
              onTap: () {
                Navigator.pop(context);
                _rateSupport();
              },
            ),
            ListTile(
              leading: const Icon(Icons.close, color: Colors.red),
              title: Text(languageProvider.getText('End Chat', 'إنهاء المحادثة')),
              onTap: () {
                Navigator.pop(context);
                _endChat();
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _emailTranscript() {
    final languageProvider = context.read<LanguageProvider>();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.getText('Chat transcript sent to your email', 'تم إرسال نسخة المحادثة لبريدك')
        ),
        backgroundColor: const Color(0xFF11B96F),
      ),
    );
  }

  void _rateSupport() {
    final languageProvider = context.read<LanguageProvider>();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageProvider.getText('Rate Support', 'تقييم الدعم')),
        content: Text(
          languageProvider.getText(
            'How would you rate your support experience?',
            'كيف تقيم تجربة الدعم؟'
          )
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageProvider.getText('Cancel', 'إلغاء')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    languageProvider.getText('Thank you for your feedback!', 'شكراً لك على ملاحظاتك!')
                  ),
                  backgroundColor: const Color(0xFF11B96F),
                ),
              );
            },
            child: Text(languageProvider.getText('Submit', 'إرسال')),
          ),
        ],
      ),
    );
  }

  void _endChat() {
    final languageProvider = context.read<LanguageProvider>();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageProvider.getText('End Chat', 'إنهاء المحادثة')),
        content: Text(
          languageProvider.getText(
            'Are you sure you want to end this chat session?',
            'هل أنت متأكد من إنهاء جلسة المحادثة؟'
          )
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageProvider.getText('Cancel', 'إلغاء')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(languageProvider.getText('End Chat', 'إنهاء المحادثة')),
          ),
        ],
      ),
    );
  }
}

/// Chat message model
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? agentName;
  final String? agentAvatar;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.agentName,
    this.agentAvatar,
  });
}
