import 'package:flutter/material.dart';
import '../constants/driver_themes.dart';

/// اختبار القائمة المنسدلة
void main() {
  runApp(const MenuTestApp());
}

class MenuTestApp extends StatelessWidget {
  const MenuTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Menu Test',
      debugShowCheckedModeBanner: false,
      theme: DriverThemes.lightTheme,
      darkTheme: DriverThemes.darkTheme,
      themeMode: ThemeMode.system,
      home: const MenuTestScreen(),
    );
  }
}

class MenuTestScreen extends StatefulWidget {
  const MenuTestScreen({super.key});

  @override
  State<MenuTestScreen> createState() => _MenuTestScreenState();
}

class _MenuTestScreenState extends State<MenuTestScreen> {
  bool _isMenuExpanded = false;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text('Menu Test'),
        backgroundColor: const Color(0xFF11B96F),
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          // محتوى الشاشة
          const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.menu,
                  size: 80,
                  color: Color(0xFF11B96F),
                ),
                SizedBox(height: 20),
                Text(
                  'اختبار القائمة المنسدلة',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'انقر على أيقونة القائمة في الأعلى',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),

          // الشريط العلوي مع القائمة
          Positioned(
            top: 50,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Row(
                    children: [
                      // زر القائمة
                      GestureDetector(
                        onTap: () {
                          print('🔧 Menu button tapped');
                          setState(() {
                            _isMenuExpanded = !_isMenuExpanded;
                          });
                        },
                        child: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                            borderRadius: BorderRadius.circular(24),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            _isMenuExpanded ? Icons.close : Icons.menu,
                            color: const Color(0xFF11B96F),
                            size: 20,
                          ),
                        ),
                      ),

                      const Spacer(),

                      // عنوان وسط
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          'اختبار القائمة',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: isDark ? Colors.white : Colors.black87,
                          ),
                        ),
                      ),

                      const Spacer(),

                      // زر الدعم
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.headset_mic,
                          color: Color(0xFF11B96F),
                          size: 20,
                        ),
                      ),
                    ],
                  ),

                  // القائمة المنسدلة
                  if (_isMenuExpanded)
                    Positioned(
                      left: 16,
                      top: 60,
                      child: Material(
                        color: Colors.transparent,
                        elevation: 8,
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.2),
                                blurRadius: 12,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildMenuOption(
                                icon: Icons.account_balance_wallet,
                                label: 'Wallet',
                                onTap: () {
                                  print('🔧 Wallet tapped');
                                  _closeMenu();
                                  _showMessage('Wallet clicked!');
                                },
                                isDark: isDark,
                              ),

                              const SizedBox(height: 12),

                              _buildMenuOption(
                                icon: Icons.local_shipping,
                                label: 'Deliveries',
                                onTap: () {
                                  print('🔧 Deliveries tapped');
                                  _closeMenu();
                                  _showMessage('Deliveries clicked!');
                                },
                                isDark: isDark,
                              ),

                              const SizedBox(height: 12),

                              _buildMenuOption(
                                icon: Icons.settings,
                                label: 'Settings',
                                onTap: () {
                                  print('🔧 Settings onTap called');
                                  _closeMenu();
                                  _showMessage('Settings clicked!');
                                },
                                isDark: isDark,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    print('🔧 Building menu option: $label');
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          print('🔧 InkWell tapped for: $label');
          print('🔧 About to call onTap for: $label');
          onTap();
          print('🔧 onTap completed for: $label');
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // التسمية
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF0F231A) : Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // الأيقونة
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF0F231A) : Colors.grey[100],
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  icon,
                  color: const Color(0xFF11B96F),
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _closeMenu() {
    setState(() {
      _isMenuExpanded = false;
    });
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
