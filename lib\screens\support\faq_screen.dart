import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// FAQ Screen with expandable questions and answers
class FAQScreen extends StatefulWidget {
  const FAQScreen({super.key});

  @override
  State<FAQScreen> createState() => _FAQScreenState();
}

class _FAQScreenState extends State<FAQScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _searchQuery = '';
  String _selectedCategory = 'all';

  final List<FAQCategory> _categories = [
    FAQCategory(
      id: 'all',
      nameEn: 'All',
      nameAr: 'الكل',
      icon: Icons.all_inclusive,
    ),
    FAQCategory(
      id: 'account',
      nameEn: 'Account',
      nameAr: 'الحساب',
      icon: Icons.account_circle,
    ),
    FAQCategory(
      id: 'trips',
      nameEn: 'Trips',
      nameAr: 'الرحلات',
      icon: Icons.directions_car,
    ),
    FAQCategory(
      id: 'payments',
      nameEn: 'Payments',
      nameAr: 'المدفوعات',
      icon: Icons.payment,
    ),
    FAQCategory(
      id: 'technical',
      nameEn: 'Technical',
      nameAr: 'تقني',
      icon: Icons.settings,
    ),
  ];

  final List<FAQItem> _faqItems = [
    FAQItem(
      category: 'account',
      questionEn: 'How do I update my profile information?',
      questionAr: 'كيف يمكنني تحديث معلومات ملفي الشخصي؟',
      answerEn: 'Go to Settings > Account > Edit Profile to update your personal information, contact details, and profile picture.',
      answerAr: 'انتقل إلى الإعدادات > الحساب > تعديل الملف الشخصي لتحديث معلوماتك الشخصية وتفاصيل الاتصال وصورة الملف الشخصي.',
    ),
    FAQItem(
      category: 'account',
      questionEn: 'How do I change my password?',
      questionAr: 'كيف يمكنني تغيير كلمة المرور؟',
      answerEn: 'Go to Settings > Security > Change Password. Enter your current password and then your new password twice.',
      answerAr: 'انتقل إلى الإعدادات > الأمان > تغيير كلمة المرور. أدخل كلمة المرور الحالية ثم كلمة المرور الجديدة مرتين.',
    ),
    FAQItem(
      category: 'trips',
      questionEn: 'How do I start a trip?',
      questionAr: 'كيف أبدأ رحلة؟',
      answerEn: 'When you receive a trip request, tap "Accept" to confirm. Follow the GPS directions to pick up the passenger, then tap "Start Trip" when they are in your vehicle.',
      answerAr: 'عندما تتلقى طلب رحلة، انقر على "قبول" للتأكيد. اتبع توجيهات GPS لاستلام الراكب، ثم انقر على "بدء الرحلة" عندما يكون في سيارتك.',
    ),
    FAQItem(
      category: 'trips',
      questionEn: 'What if the passenger cancels the trip?',
      questionAr: 'ماذا لو ألغى الراكب الرحلة؟',
      answerEn: 'If a passenger cancels after you\'ve started driving to the pickup location, you may be eligible for a cancellation fee. This will be automatically added to your earnings.',
      answerAr: 'إذا ألغى الراكب بعد أن بدأت القيادة إلى موقع الاستلام، فقد تكون مؤهلاً لرسوم الإلغاء. سيتم إضافة هذا تلقائياً إلى أرباحك.',
    ),
    FAQItem(
      category: 'payments',
      questionEn: 'When do I get paid?',
      questionAr: 'متى أحصل على الدفع؟',
      answerEn: 'Payments are processed weekly on Tuesdays. You can view your earnings and payment history in the Earnings section of the app.',
      answerAr: 'تتم معالجة المدفوعات أسبوعياً يوم الثلاثاء. يمكنك عرض أرباحك وتاريخ المدفوعات في قسم الأرباح في التطبيق.',
    ),
    FAQItem(
      category: 'payments',
      questionEn: 'How do I add my bank account?',
      questionAr: 'كيف أضيف حسابي المصرفي؟',
      answerEn: 'Go to Settings > Payment > Bank Account. Enter your bank details including account number and routing number. We use secure encryption to protect your information.',
      answerAr: 'انتقل إلى الإعدادات > الدفع > الحساب المصرفي. أدخل تفاصيل البنك بما في ذلك رقم الحساب ورقم التوجيه. نستخدم التشفير الآمن لحماية معلوماتك.',
    ),
    FAQItem(
      category: 'technical',
      questionEn: 'The app is running slowly. What should I do?',
      questionAr: 'التطبيق يعمل ببطء. ماذا يجب أن أفعل؟',
      answerEn: 'Try closing and reopening the app. If the problem persists, restart your phone or check for app updates in your device\'s app store.',
      answerAr: 'حاول إغلاق التطبيق وإعادة فتحه. إذا استمرت المشكلة، أعد تشغيل هاتفك أو تحقق من تحديثات التطبيق في متجر التطبيقات.',
    ),
    FAQItem(
      category: 'technical',
      questionEn: 'GPS is not working properly. How do I fix it?',
      questionAr: 'GPS لا يعمل بشكل صحيح. كيف أصلحه؟',
      answerEn: 'Make sure location services are enabled for the app. Go to your phone settings and ensure GPS/Location is turned on. Also check that the app has permission to access your location.',
      answerAr: 'تأكد من تمكين خدمات الموقع للتطبيق. انتقل إلى إعدادات هاتفك وتأكد من تشغيل GPS/الموقع. تحقق أيضاً من أن التطبيق لديه إذن للوصول إلى موقعك.',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Frequently Asked Questions', 'الأسئلة الشائعة'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              // Search Bar
              _buildSearchBar(isDark, languageProvider),
              
              // Categories
              _buildCategories(isDark, languageProvider),
              
              // FAQ List
              Expanded(
                child: _buildFAQList(isDark, languageProvider),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar(bool isDark, LanguageProvider languageProvider) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: (value) => setState(() => _searchQuery = value),
        decoration: InputDecoration(
          hintText: languageProvider.getText('Search FAQ...', 'البحث في الأسئلة الشائعة...'),
          border: InputBorder.none,
          prefixIcon: const Icon(Icons.search, color: Color(0xFF11B96F)),
          hintStyle: TextStyle(
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
        style: TextStyle(
          color: isDark ? Colors.white : Colors.black87,
        ),
      ),
    );
  }

  Widget _buildCategories(bool isDark, LanguageProvider languageProvider) {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = _selectedCategory == category.id;
          
          return GestureDetector(
            onTap: () => setState(() => _selectedCategory = category.id),
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected 
                    ? const Color(0xFF11B96F) 
                    : (isDark ? const Color(0xFF1B3B2E) : Colors.white),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected 
                      ? const Color(0xFF11B96F) 
                      : (isDark ? Colors.grey[700]! : Colors.grey[300]!),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    category.icon,
                    size: 16,
                    color: isSelected 
                        ? Colors.white 
                        : (isDark ? Colors.grey[400] : Colors.grey[600]),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    languageProvider.isArabic ? category.nameAr : category.nameEn,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: isSelected 
                          ? Colors.white 
                          : (isDark ? Colors.grey[400] : Colors.grey[600]),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFAQList(bool isDark, LanguageProvider languageProvider) {
    final filteredItems = _faqItems.where((item) {
      final matchesCategory = _selectedCategory == 'all' || item.category == _selectedCategory;
      final matchesSearch = _searchQuery.isEmpty || 
          (languageProvider.isArabic 
              ? item.questionAr.toLowerCase().contains(_searchQuery.toLowerCase())
              : item.questionEn.toLowerCase().contains(_searchQuery.toLowerCase()));
      return matchesCategory && matchesSearch;
    }).toList();

    if (filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: isDark ? Colors.grey[600] : Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              languageProvider.getText('No results found', 'لم يتم العثور على نتائج'),
              style: TextStyle(
                fontSize: 18,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        final item = filteredItems[index];
        return _buildFAQItem(item, isDark, languageProvider);
      },
    );
  }

  Widget _buildFAQItem(FAQItem item, bool isDark, LanguageProvider languageProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        title: Text(
          languageProvider.isArabic ? item.questionAr : item.questionEn,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        iconColor: const Color(0xFF11B96F),
        collapsedIconColor: isDark ? Colors.grey[400] : Colors.grey[600],
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              languageProvider.isArabic ? item.answerAr : item.answerEn,
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
                color: isDark ? Colors.grey[300] : Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class FAQCategory {
  final String id;
  final String nameEn;
  final String nameAr;
  final IconData icon;

  FAQCategory({
    required this.id,
    required this.nameEn,
    required this.nameAr,
    required this.icon,
  });
}

class FAQItem {
  final String category;
  final String questionEn;
  final String questionAr;
  final String answerEn;
  final String answerAr;

  FAQItem({
    required this.category,
    required this.questionEn,
    required this.questionAr,
    required this.answerEn,
    required this.answerAr,
  });
}
