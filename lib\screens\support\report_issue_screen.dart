import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Report Issue screen for bug reports and problems
class ReportIssueScreen extends StatefulWidget {
  const ReportIssueScreen({super.key});

  @override
  State<ReportIssueScreen> createState() => _ReportIssueScreenState();
}

class _ReportIssueScreenState extends State<ReportIssueScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _stepsController = TextEditingController();
  String _selectedCategory = 'app_bug';
  String _selectedPriority = 'medium';
  bool _isSubmitting = false;
  final List<File> _attachedImages = [];
  final ImagePicker _picker = ImagePicker();

  final List<IssueCategory> _categories = [
    IssueCategory(
      id: 'app_bug',
      nameEn: 'App Bug',
      nameAr: 'خطأ في التطبيق',
      icon: Icons.bug_report,
    ),
    IssueCategory(
      id: 'payment_issue',
      nameEn: 'Payment Issue',
      nameAr: 'مشكلة في الدفع',
      icon: Icons.payment,
    ),
    IssueCategory(
      id: 'gps_problem',
      nameEn: 'GPS Problem',
      nameAr: 'مشكلة في GPS',
      icon: Icons.gps_off,
    ),
    IssueCategory(
      id: 'trip_issue',
      nameEn: 'Trip Issue',
      nameAr: 'مشكلة في الرحلة',
      icon: Icons.directions_car,
    ),
    IssueCategory(
      id: 'account_problem',
      nameEn: 'Account Problem',
      nameAr: 'مشكلة في الحساب',
      icon: Icons.account_circle,
    ),
    IssueCategory(
      id: 'other',
      nameEn: 'Other',
      nameAr: 'أخرى',
      icon: Icons.help,
    ),
  ];

  final List<IssuePriority> _priorities = [
    IssuePriority(
      id: 'low',
      nameEn: 'Low',
      nameAr: 'منخفض',
      color: Colors.green,
    ),
    IssuePriority(
      id: 'medium',
      nameEn: 'Medium',
      nameAr: 'متوسط',
      color: Colors.orange,
    ),
    IssuePriority(
      id: 'high',
      nameEn: 'High',
      nameAr: 'عالي',
      color: Colors.red,
    ),
    IssuePriority(
      id: 'critical',
      nameEn: 'Critical',
      nameAr: 'حرج',
      color: Colors.red[900]!,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _stepsController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Report Issue', 'الإبلاغ عن مشكلة'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Header
                _buildHeader(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Report Form
                _buildReportForm(isDark, languageProvider),
                
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red,
            Colors.red.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.bug_report,
            size: 60,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('Report a Problem', 'الإبلاغ عن مشكلة'),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Help us improve by reporting bugs and issues',
              'ساعدنا في التحسين من خلال الإبلاغ عن الأخطاء والمشاكل'
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.security,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  languageProvider.getText('Confidential & Secure', 'سري وآمن'),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportForm(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              languageProvider.getText('Issue Details', 'تفاصيل المشكلة'),
              style: TextStyle(
                fontSize: DriverTypography.titleMedium,
                fontWeight: FontWeight.bold,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 20),
            
            // Category Selection
            Text(
              languageProvider.getText('Category', 'الفئة'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            _buildCategorySelection(isDark, languageProvider),
            
            const SizedBox(height: 20),
            
            // Priority Selection
            Text(
              languageProvider.getText('Priority', 'الأولوية'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            _buildPrioritySelection(isDark, languageProvider),
            
            const SizedBox(height: 20),
            
            // Title Field
            TextFormField(
              controller: _titleController,
              decoration: InputDecoration(
                labelText: languageProvider.getText('Issue Title', 'عنوان المشكلة'),
                prefixIcon: const Icon(Icons.title, color: Color(0xFF11B96F)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return languageProvider.getText('Please enter issue title', 'يرجى إدخال عنوان المشكلة');
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // Description Field
            TextFormField(
              controller: _descriptionController,
              maxLines: 4,
              decoration: InputDecoration(
                labelText: languageProvider.getText('Description', 'الوصف'),
                prefixIcon: const Icon(Icons.description, color: Color(0xFF11B96F)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
                ),
                alignLabelWithHint: true,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return languageProvider.getText('Please describe the issue', 'يرجى وصف المشكلة');
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // Steps to Reproduce
            TextFormField(
              controller: _stepsController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: languageProvider.getText('Steps to Reproduce (Optional)', 'خطوات إعادة الإنتاج (اختياري)'),
                prefixIcon: const Icon(Icons.list, color: Color(0xFF11B96F)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
                ),
                alignLabelWithHint: true,
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Attachments Section
            _buildAttachmentsSection(isDark, languageProvider),
            
            const SizedBox(height: 20),
            
            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitReport,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        languageProvider.getText('Submit Report', 'إرسال التقرير'),
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySelection(bool isDark, LanguageProvider languageProvider) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _categories.map((category) {
        final isSelected = _selectedCategory == category.id;
        return GestureDetector(
          onTap: () => setState(() => _selectedCategory = category.id),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color(0xFF11B96F)
                  : (isDark ? Colors.grey[800] : Colors.grey[200]),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected
                    ? const Color(0xFF11B96F)
                    : (isDark ? Colors.grey[600]! : Colors.grey[400]!),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  category.icon,
                  size: 16,
                  color: isSelected
                      ? Colors.white
                      : (isDark ? Colors.grey[400] : Colors.grey[600]),
                ),
                const SizedBox(width: 6),
                Text(
                  languageProvider.isArabic ? category.nameAr : category.nameEn,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isSelected
                        ? Colors.white
                        : (isDark ? Colors.grey[400] : Colors.grey[600]),
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPrioritySelection(bool isDark, LanguageProvider languageProvider) {
    return Row(
      children: _priorities.map((priority) {
        final isSelected = _selectedPriority == priority.id;
        return Expanded(
          child: GestureDetector(
            onTap: () => setState(() => _selectedPriority = priority.id),
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: isSelected
                    ? priority.color.withValues(alpha: 0.2)
                    : (isDark ? Colors.grey[800] : Colors.grey[100]),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected
                      ? priority.color
                      : (isDark ? Colors.grey[600]! : Colors.grey[300]!),
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Column(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: priority.color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    languageProvider.isArabic ? priority.nameAr : priority.nameEn,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected
                          ? priority.color
                          : (isDark ? Colors.grey[400] : Colors.grey[600]),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAttachmentsSection(bool isDark, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              languageProvider.getText('Attachments (Optional)', 'المرفقات (اختياري)'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
            TextButton.icon(
              onPressed: _pickImage,
              icon: const Icon(Icons.add_photo_alternate, color: Color(0xFF11B96F)),
              label: Text(
                languageProvider.getText('Add Image', 'إضافة صورة'),
                style: const TextStyle(color: Color(0xFF11B96F)),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (_attachedImages.isNotEmpty)
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _attachedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  width: 100,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
                    ),
                  ),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _attachedImages[index],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () => _removeImage(index),
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          )
        else
          Container(
            width: double.infinity,
            height: 80,
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[800] : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
                style: BorderStyle.solid,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.cloud_upload,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                  size: 32,
                ),
                const SizedBox(height: 4),
                Text(
                  languageProvider.getText('Tap to add screenshots', 'انقر لإضافة لقطات الشاشة'),
                  style: TextStyle(
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Future<void> _pickImage() async {
    try {
      final image = await _picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        setState(() {
          _attachedImages.add(File(image.path));
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.read<LanguageProvider>().getText(
                'Failed to pick image',
                'فشل في اختيار الصورة'
              )
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _attachedImages.removeAt(index);
    });
  }

  Future<void> _submitReport() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.read<LanguageProvider>().getText(
                'Issue reported successfully! We will investigate and get back to you.',
                'تم الإبلاغ عن المشكلة بنجاح! سنقوم بالتحقيق والعودة إليك.'
              )
            ),
            backgroundColor: const Color(0xFF11B96F),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );

        // Clear form
        _titleController.clear();
        _descriptionController.clear();
        _stepsController.clear();
        setState(() {
          _selectedCategory = 'app_bug';
          _selectedPriority = 'medium';
          _attachedImages.clear();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.read<LanguageProvider>().getText(
                'Failed to submit report. Please try again.',
                'فشل في إرسال التقرير. يرجى المحاولة مرة أخرى.'
              )
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}

class IssueCategory {
  final String id;
  final String nameEn;
  final String nameAr;
  final IconData icon;

  IssueCategory({
    required this.id,
    required this.nameEn,
    required this.nameAr,
    required this.icon,
  });
}

class IssuePriority {
  final String id;
  final String nameEn;
  final String nameAr;
  final Color color;

  IssuePriority({
    required this.id,
    required this.nameEn,
    required this.nameAr,
    required this.color,
  });
}
