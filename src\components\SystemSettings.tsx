import React, { useState } from 'react';
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Mail, 
  Globe, 
  CreditCard, 
  Server, 
  Shield, 
  Bell, 
  Palette, 
  Database,
  Key,
  Lock,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Upload,
  Download,
  Trash2
} from 'lucide-react';

interface SystemConfig {
  general: {
    siteName: string;
    siteUrl: string;
    adminEmail: string;
    timezone: string;
    language: string;
    currency: string;
    maintenanceMode: boolean;
  };
  email: {
    smtpHost: string;
    smtpPort: number;
    smtpUsername: string;
    smtpPassword: string;
    smtpEncryption: string;
    fromEmail: string;
    fromName: string;
  };
  payment: {
    stripePublicKey: string;
    stripeSecretKey: string;
    paypalClientId: string;
    paypalClientSecret: string;
    enableCashOnDelivery: boolean;
    defaultCommission: number;
    minimumOrderAmount: number;
    deliveryFee: number;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    orderNotifications: boolean;
    marketingEmails: boolean;
  };
  security: {
    twoFactorAuth: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    requirePasswordChange: boolean;
  };
  api: {
    googleMapsKey: string;
    firebaseKey: string;
    twilioSid: string;
    twilioToken: string;
    enableApiLogging: boolean;
    rateLimitPerMinute: number;
  };
}

const SystemSettings = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [showPasswords, setShowPasswords] = useState<{[key: string]: boolean}>({});
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  const [config, setConfig] = useState<SystemConfig>({
    general: {
      siteName: 'وصلتي - Wasslti',
      siteUrl: 'https://wasslti.com',
      adminEmail: '<EMAIL>',
      timezone: 'Africa/Casablanca',
      language: 'ar',
      currency: 'MAD',
      maintenanceMode: false
    },
    email: {
      smtpHost: 'smtp.gmail.com',
      smtpPort: 587,
      smtpUsername: '<EMAIL>',
      smtpPassword: '',
      smtpEncryption: 'tls',
      fromEmail: '<EMAIL>',
      fromName: 'وصلتي - Wasslti'
    },
    payment: {
      stripePublicKey: '',
      stripeSecretKey: '',
      paypalClientId: '',
      paypalClientSecret: '',
      enableCashOnDelivery: true,
      defaultCommission: 15,
      minimumOrderAmount: 50,
      deliveryFee: 15
    },
    notifications: {
      emailNotifications: true,
      smsNotifications: true,
      pushNotifications: true,
      orderNotifications: true,
      marketingEmails: false
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      requirePasswordChange: false
    },
    api: {
      googleMapsKey: '',
      firebaseKey: '',
      twilioSid: '',
      twilioToken: '',
      enableApiLogging: true,
      rateLimitPerMinute: 100
    }
  });

  const tabs = [
    { id: 'general', name: 'عام', icon: <Settings className="w-5 h-5" /> },
    { id: 'email', name: 'البريد الإلكتروني', icon: <Mail className="w-5 h-5" /> },
    { id: 'payment', name: 'الدفع', icon: <CreditCard className="w-5 h-5" /> },
    { id: 'notifications', name: 'الإشعارات', icon: <Bell className="w-5 h-5" /> },
    { id: 'security', name: 'الأمان', icon: <Shield className="w-5 h-5" /> },
    { id: 'api', name: 'API', icon: <Key className="w-5 h-5" /> }
  ];

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSaveMessage('تم حفظ الإعدادات بنجاح!');
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      setSaveMessage('حدث خطأ في حفظ الإعدادات');
    } finally {
      setIsSaving(false);
    }
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const updateConfig = (section: keyof SystemConfig, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleBackup = () => {
    alert('سيتم إنشاء نسخة احتياطية...');
  };

  const handleRestore = () => {
    if (confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع الإعدادات الحالية.')) {
      alert('سيتم استعادة النسخة الاحتياطية...');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white rtl-font">
            إعدادات النظام
          </h1>
          <p className="text-gray-600 dark:text-gray-400 rtl-font">
            إدارة وتكوين إعدادات المنصة العامة
          </p>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button 
            onClick={handleBackup}
            className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Download className="w-4 h-4" />
            <span>نسخ احتياطي</span>
          </button>
          <button 
            onClick={handleRestore}
            className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Upload className="w-4 h-4" />
            <span>استعادة</span>
          </button>
          <button 
            onClick={handleSave}
            disabled={isSaving}
            className="bg-primary text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary/90 transition-colors flex items-center space-x-2 rtl:space-x-reverse disabled:opacity-50"
          >
            {isSaving ? <RefreshCw className="w-5 h-5 animate-spin" /> : <Save className="w-5 h-5" />}
            <span>{isSaving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}</span>
          </button>
        </div>
      </div>

      {/* Save Message */}
      {saveMessage && (
        <div className={`p-4 rounded-xl flex items-center space-x-3 rtl:space-x-reverse ${
          saveMessage.includes('نجاح') 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' 
            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
        }`}>
          {saveMessage.includes('نجاح') ? <CheckCircle className="w-5 h-5" /> : <AlertTriangle className="w-5 h-5" />}
          <span className="rtl-font">{saveMessage}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 rounded-xl transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-primary text-white shadow-lg'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  {tab.icon}
                  <span className="font-medium rtl-font">{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            {/* General Settings */}
            {activeTab === 'general' && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">الإعدادات العامة</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      اسم الموقع
                    </label>
                    <input
                      type="text"
                      value={config.general.siteName}
                      onChange={(e) => updateConfig('general', 'siteName', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      رابط الموقع
                    </label>
                    <input
                      type="url"
                      value={config.general.siteUrl}
                      onChange={(e) => updateConfig('general', 'siteUrl', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      بريد المدير
                    </label>
                    <input
                      type="email"
                      value={config.general.adminEmail}
                      onChange={(e) => updateConfig('general', 'adminEmail', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      المنطقة الزمنية
                    </label>
                    <select
                      value={config.general.timezone}
                      onChange={(e) => updateConfig('general', 'timezone', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                    >
                      <option value="Africa/Casablanca">المغرب (GMT+1)</option>
                      <option value="Europe/Paris">باريس (GMT+1)</option>
                      <option value="UTC">UTC (GMT+0)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      اللغة الافتراضية
                    </label>
                    <select
                      value={config.general.language}
                      onChange={(e) => updateConfig('general', 'language', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                    >
                      <option value="ar">العربية</option>
                      <option value="fr">Français</option>
                      <option value="en">English</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      العملة
                    </label>
                    <select
                      value={config.general.currency}
                      onChange={(e) => updateConfig('general', 'currency', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                    >
                      <option value="MAD">درهم مغربي (MAD)</option>
                      <option value="EUR">يورو (EUR)</option>
                      <option value="USD">دولار أمريكي (USD)</option>
                    </select>
                  </div>
                </div>

                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <input
                    type="checkbox"
                    id="maintenanceMode"
                    checked={config.general.maintenanceMode}
                    onChange={(e) => updateConfig('general', 'maintenanceMode', e.target.checked)}
                    className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                  />
                  <label htmlFor="maintenanceMode" className="text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                    تفعيل وضع الصيانة
                  </label>
                </div>
              </div>
            )}

            {/* Email Settings */}
            {activeTab === 'email' && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">إعدادات البريد الإلكتروني</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      خادم SMTP
                    </label>
                    <input
                      type="text"
                      value={config.email.smtpHost}
                      onChange={(e) => updateConfig('email', 'smtpHost', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      منفذ SMTP
                    </label>
                    <input
                      type="number"
                      value={config.email.smtpPort}
                      onChange={(e) => updateConfig('email', 'smtpPort', parseInt(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      اسم المستخدم
                    </label>
                    <input
                      type="email"
                      value={config.email.smtpUsername}
                      onChange={(e) => updateConfig('email', 'smtpUsername', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      كلمة المرور
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.smtpPassword ? 'text' : 'password'}
                        value={config.email.smtpPassword}
                        onChange={(e) => updateConfig('email', 'smtpPassword', e.target.value)}
                        className="w-full px-4 py-3 pr-12 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('smtpPassword')}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.smtpPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      التشفير
                    </label>
                    <select
                      value={config.email.smtpEncryption}
                      onChange={(e) => updateConfig('email', 'smtpEncryption', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    >
                      <option value="tls">TLS</option>
                      <option value="ssl">SSL</option>
                      <option value="none">بدون تشفير</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      البريد المرسل من
                    </label>
                    <input
                      type="email"
                      value={config.email.fromEmail}
                      onChange={(e) => updateConfig('email', 'fromEmail', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      اسم المرسل
                    </label>
                    <input
                      type="text"
                      value={config.email.fromName}
                      onChange={(e) => updateConfig('email', 'fromName', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Payment Settings */}
            {activeTab === 'payment' && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">إعدادات الدفع</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Stripe Public Key
                    </label>
                    <input
                      type="text"
                      value={config.payment.stripePublicKey}
                      onChange={(e) => updateConfig('payment', 'stripePublicKey', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Stripe Secret Key
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.stripeSecret ? 'text' : 'password'}
                        value={config.payment.stripeSecretKey}
                        onChange={(e) => updateConfig('payment', 'stripeSecretKey', e.target.value)}
                        className="w-full px-4 py-3 pr-12 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('stripeSecret')}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.stripeSecret ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      العمولة الافتراضية (%)
                    </label>
                    <input
                      type="number"
                      value={config.payment.defaultCommission}
                      onChange={(e) => updateConfig('payment', 'defaultCommission', parseFloat(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      الحد الأدنى للطلب (درهم)
                    </label>
                    <input
                      type="number"
                      value={config.payment.minimumOrderAmount}
                      onChange={(e) => updateConfig('payment', 'minimumOrderAmount', parseFloat(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      رسوم التوصيل (درهم)
                    </label>
                    <input
                      type="number"
                      value={config.payment.deliveryFee}
                      onChange={(e) => updateConfig('payment', 'deliveryFee', parseFloat(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <input
                    type="checkbox"
                    id="cashOnDelivery"
                    checked={config.payment.enableCashOnDelivery}
                    onChange={(e) => updateConfig('payment', 'enableCashOnDelivery', e.target.checked)}
                    className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                  />
                  <label htmlFor="cashOnDelivery" className="text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                    تفعيل الدفع عند التسليم
                  </label>
                </div>
              </div>
            )}

            {/* Notifications Settings */}
            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">إعدادات الإشعارات</h2>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <div>
                      <h3 className="font-semibold text-gray-800 dark:text-white rtl-font">إشعارات البريد الإلكتروني</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إرسال إشعارات عبر البريد الإلكتروني</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.notifications.emailNotifications}
                      onChange={(e) => updateConfig('notifications', 'emailNotifications', e.target.checked)}
                      className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <div>
                      <h3 className="font-semibold text-gray-800 dark:text-white rtl-font">إشعارات SMS</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إرسال إشعارات عبر الرسائل النصية</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.notifications.smsNotifications}
                      onChange={(e) => updateConfig('notifications', 'smsNotifications', e.target.checked)}
                      className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <div>
                      <h3 className="font-semibold text-gray-800 dark:text-white rtl-font">الإشعارات الفورية</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إشعارات فورية في التطبيق</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.notifications.pushNotifications}
                      onChange={(e) => updateConfig('notifications', 'pushNotifications', e.target.checked)}
                      className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <div>
                      <h3 className="font-semibold text-gray-800 dark:text-white rtl-font">إشعارات الطلبات</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إشعارات حالة الطلبات</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.notifications.orderNotifications}
                      onChange={(e) => updateConfig('notifications', 'orderNotifications', e.target.checked)}
                      className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <div>
                      <h3 className="font-semibold text-gray-800 dark:text-white rtl-font">رسائل التسويق</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">رسائل ترويجية وعروض خاصة</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.notifications.marketingEmails}
                      onChange={(e) => updateConfig('notifications', 'marketingEmails', e.target.checked)}
                      className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Security Settings */}
            {activeTab === 'security' && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">إعدادات الأمان</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      مهلة الجلسة (دقيقة)
                    </label>
                    <input
                      type="number"
                      value={config.security.sessionTimeout}
                      onChange={(e) => updateConfig('security', 'sessionTimeout', parseInt(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      محاولات تسجيل الدخول القصوى
                    </label>
                    <input
                      type="number"
                      value={config.security.maxLoginAttempts}
                      onChange={(e) => updateConfig('security', 'maxLoginAttempts', parseInt(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                      الحد الأدنى لطول كلمة المرور
                    </label>
                    <input
                      type="number"
                      value={config.security.passwordMinLength}
                      onChange={(e) => updateConfig('security', 'passwordMinLength', parseInt(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <div>
                      <h3 className="font-semibold text-gray-800 dark:text-white rtl-font">المصادقة الثنائية</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">تفعيل المصادقة الثنائية للحسابات</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.security.twoFactorAuth}
                      onChange={(e) => updateConfig('security', 'twoFactorAuth', e.target.checked)}
                      className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    />
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <div>
                      <h3 className="font-semibold text-gray-800 dark:text-white rtl-font">إجبار تغيير كلمة المرور</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إجبار المستخدمين على تغيير كلمة المرور دورياً</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={config.security.requirePasswordChange}
                      onChange={(e) => updateConfig('security', 'requirePasswordChange', e.target.checked)}
                      className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* API Settings */}
            {activeTab === 'api' && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">إعدادات API</h2>
                
                <div className="grid grid-cols-1 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Google Maps API Key
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.googleMaps ? 'text' : 'password'}
                        value={config.api.googleMapsKey}
                        onChange={(e) => updateConfig('api', 'googleMapsKey', e.target.value)}
                        className="w-full px-4 py-3 pr-12 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('googleMaps')}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.googleMaps ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Firebase API Key
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.firebase ? 'text' : 'password'}
                        value={config.api.firebaseKey}
                        onChange={(e) => updateConfig('api', 'firebaseKey', e.target.value)}
                        className="w-full px-4 py-3 pr-12 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('firebase')}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.firebase ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Twilio SID
                      </label>
                      <input
                        type="text"
                        value={config.api.twilioSid}
                        onChange={(e) => updateConfig('api', 'twilioSid', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                        حد الطلبات في الدقيقة
                      </label>
                      <input
                        type="number"
                        value={config.api.rateLimitPerMinute}
                        onChange={(e) => updateConfig('api', 'rateLimitPerMinute', parseInt(e.target.value))}
                        className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <input
                    type="checkbox"
                    id="apiLogging"
                    checked={config.api.enableApiLogging}
                    onChange={(e) => updateConfig('api', 'enableApiLogging', e.target.checked)}
                    className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                  />
                  <label htmlFor="apiLogging" className="text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                    تفعيل تسجيل طلبات API
                  </label>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemSettings;