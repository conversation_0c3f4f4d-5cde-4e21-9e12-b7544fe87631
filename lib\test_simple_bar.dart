import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'constants/driver_themes.dart';
import 'providers/auth_provider.dart';
import 'models/driver_status.dart';
import 'widgets/simple_top_bar.dart';
import 'screens/settings/settings_screen.dart';

/// اختبار الشريط العلوي الجديد
void main() {
  runApp(const TestSimpleBarApp());
}

class TestSimpleBarApp extends StatelessWidget {
  const TestSimpleBarApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'Test Simple Bar',
        debugShowCheckedModeBanner: false,
        theme: DriverThemes.lightTheme,
        darkTheme: DriverThemes.darkTheme,
        themeMode: ThemeMode.system,
        home: const TestSimpleBarScreen(),
      ),
    );
  }
}

class TestSimpleBarScreen extends StatefulWidget {
  const TestSimpleBarScreen({super.key});

  @override
  State<TestSimpleBarScreen> createState() => _TestSimpleBarScreenState();
}

class _TestSimpleBarScreenState extends State<TestSimpleBarScreen> {
  DriverStatus _driverStatus = DriverStatus.working;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
      body: Stack(
        children: [
          // خلفية تشبه الخريطة
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  isDark ? const Color(0xFF0F231A) : const Color(0xFFF0F8FF),
                  isDark ? const Color(0xFF1B3B2E) : const Color(0xFFE8F5E8),
                ],
              ),
            ),
          ),

          // محتوى الشاشة
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.settings,
                  size: 80,
                  color: Color(0xFF11B96F),
                ),
                const SizedBox(height: 20),
                const Text(
                  'اختبار الشريط العلوي الجديد',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                const Text(
                  'انقر على زر الترس في الأعلى للذهاب للإعدادات',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                
                // أزرار تغيير الحالة
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildStatusButton('يعمل', DriverStatus.working, Colors.green),
                    const SizedBox(width: 10),
                    _buildStatusButton('استراحة', DriverStatus.resting, Colors.orange),
                    const SizedBox(width: 10),
                    _buildStatusButton('متوقف', DriverStatus.stopped, Colors.red),
                  ],
                ),
              ],
            ),
          ),

          // الشريط العلوي الجديد
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: FloatingTopBar(
              driverStatus: _driverStatus,
              onStatusTap: _handleStatusTap,
              onSettingsTap: _handleSettingsTap,
              onSupportTap: _handleSupportTap,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusButton(String text, DriverStatus status, Color color) {
    final isSelected = _driverStatus == status;
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _driverStatus = status;
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? color : Colors.grey,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        text,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  void _handleStatusTap() {
    // تدوير الحالة
    setState(() {
      switch (_driverStatus) {
        case DriverStatus.working:
          _driverStatus = DriverStatus.resting;
          break;
        case DriverStatus.resting:
          _driverStatus = DriverStatus.stopped;
          break;
        case DriverStatus.stopped:
          _driverStatus = DriverStatus.working;
          break;
      }
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تغيير الحالة إلى: ${_getStatusText()}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _handleSettingsTap() {
    print('🔧 Settings button pressed - navigating to settings');
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );
  }

  void _handleSupportTap() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم النقر على زر الدعم'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  String _getStatusText() {
    switch (_driverStatus) {
      case DriverStatus.working:
        return 'يعمل';
      case DriverStatus.resting:
        return 'استراحة';
      case DriverStatus.stopped:
        return 'متوقف';
    }
  }
}
