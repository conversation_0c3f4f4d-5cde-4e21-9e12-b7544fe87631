import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/driver_themes.dart';
import '../providers/auth_provider.dart';
import '../utils/settings_helper.dart';

/// Complete settings demo showcasing all features
void main() {
  runApp(const CompleteSettingsDemoApp());
}

class CompleteSettingsDemoApp extends StatelessWidget {
  const CompleteSettingsDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'Complete Settings Demo',
        debugShowCheckedModeBanner: false,
        theme: DriverThemes.lightTheme,
        darkTheme: DriverThemes.darkTheme,
        themeMode: ThemeMode.system,
        home: const CompleteSettingsDemoScreen(),
      ),
    );
  }
}

class CompleteSettingsDemoScreen extends StatefulWidget {
  const CompleteSettingsDemoScreen({super.key});

  @override
  State<CompleteSettingsDemoScreen> createState() => _CompleteSettingsDemoScreenState();
}

class _CompleteSettingsDemoScreenState extends State<CompleteSettingsDemoScreen> {
  ThemeMode _themeMode = ThemeMode.system;
  String _selectedLanguage = 'العربية';

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Complete Settings Demo',
      debugShowCheckedModeBanner: false,
      theme: DriverThemes.lightTheme,
      darkTheme: DriverThemes.darkTheme,
      themeMode: _themeMode,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Complete Settings Demo'),
          backgroundColor: const Color(0xFF11B96F),
          foregroundColor: Colors.white,
          actions: [
            PopupMenuButton<ThemeMode>(
              icon: Icon(_getThemeIcon()),
              onSelected: (ThemeMode mode) {
                setState(() {
                  _themeMode = mode;
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: ThemeMode.light,
                  child: Row(
                    children: [
                      Icon(Icons.light_mode),
                      SizedBox(width: 8),
                      Text('Light Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: ThemeMode.dark,
                  child: Row(
                    children: [
                      Icon(Icons.dark_mode),
                      SizedBox(width: 8),
                      Text('Dark Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: ThemeMode.system,
                  child: Row(
                    children: [
                      Icon(Icons.settings),
                      SizedBox(width: 8),
                      Text('System'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
          ],
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const Icon(
                  Icons.settings,
                  size: 80,
                  color: Color(0xFF11B96F),
                ),
                const SizedBox(height: 20),
                const Text(
                  'Complete Settings Demo',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                const Text(
                  'Test all settings features and languages',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 30),
                
                // Language Selection
                _buildLanguageSelector(),
                
                const SizedBox(height: 30),
                
                // Settings Buttons
                _buildSettingsButtons(),
                
                const SizedBox(height: 30),
                
                // Helper Functions Demo
                _buildHelperDemo(),
                
                const SizedBox(height: 30),
                
                // Features List
                _buildFeaturesList(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1B3B2E)
            : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Language:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('العربية'),
                  value: 'العربية',
                  groupValue: _selectedLanguage,
                  onChanged: (value) {
                    setState(() {
                      _selectedLanguage = value!;
                    });
                  },
                  activeColor: const Color(0xFF11B96F),
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('English'),
                  value: 'English',
                  groupValue: _selectedLanguage,
                  onChanged: (value) {
                    setState(() {
                      _selectedLanguage = value!;
                    });
                  },
                  activeColor: const Color(0xFF11B96F),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsButtons() {
    return Column(
      children: [
        // Open Settings with Selected Language
        SizedBox(
          width: double.infinity,
          height: 60,
          child: ElevatedButton.icon(
            onPressed: () {
              SettingsHelper.navigateToSettings(context, language: _selectedLanguage);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF11B96F),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(Icons.settings, size: 24),
            label: Text(
              'Open Settings ($_selectedLanguage)',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Auto-detect Language
        SizedBox(
          width: double.infinity,
          height: 60,
          child: ElevatedButton.icon(
            onPressed: () {
              context.navigateToSettingsAuto();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(Icons.auto_awesome, size: 24),
            label: const Text(
              'Auto-detect Language',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Arabic Settings
        SizedBox(
          width: double.infinity,
          height: 60,
          child: ElevatedButton.icon(
            onPressed: () {
              context.navigateToSettings(language: 'العربية');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(Icons.language, size: 24),
            label: const Text(
              'Force Arabic Settings',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // English Settings
        SizedBox(
          width: double.infinity,
          height: 60,
          child: ElevatedButton.icon(
            onPressed: () {
              context.navigateToSettings(language: 'English');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(Icons.language, size: 24),
            label: const Text(
              'Force English Settings',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHelperDemo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1B3B2E)
            : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Helper Functions Demo:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          _buildHelperItem('Supported Languages:', SettingsHelper.getSupportedLanguages().join(', ')),
          _buildHelperItem('Current Language Display:', SettingsHelper.getLanguageDisplayName(_selectedLanguage)),
          _buildHelperItem('Is RTL:', SettingsHelper.isRTL(_selectedLanguage).toString()),
          _buildHelperItem('Text Direction:', SettingsHelper.getTextDirection(_selectedLanguage).toString()),
        ],
      ),
    );
  }

  Widget _buildHelperItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[300]
                  : Colors.grey[700],
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: const Color(0xFF11B96F),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1B3B2E)
            : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Available Features:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          _buildFeatureItem('✅ Arabic Settings Screen (RTL)'),
          _buildFeatureItem('✅ English Settings Screen (LTR)'),
          _buildFeatureItem('✅ Automatic language detection'),
          _buildFeatureItem('✅ Manual language selection'),
          _buildFeatureItem('✅ Light/Dark theme support'),
          _buildFeatureItem('✅ All 9 settings sections'),
          _buildFeatureItem('✅ Toggle switches and selections'),
          _buildFeatureItem('✅ Logout with confirmation'),
          _buildFeatureItem('✅ Helper utilities'),
          _buildFeatureItem('✅ Extension methods'),
          _buildFeatureItem('✅ Responsive design'),
          _buildFeatureItem('✅ Modern UI components'),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[300]
              : Colors.grey[700],
        ),
      ),
    );
  }

  IconData _getThemeIcon() {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.settings;
    }
  }
}

/// Function to run the complete settings demo
void runCompleteSettingsDemo() {
  runApp(const CompleteSettingsDemoApp());
}

/// Usage example:
///
/// ```dart
/// // In main.dart or any other file
/// import 'demo/complete_settings_demo.dart';
///
/// void main() {
///   runCompleteSettingsDemo();
/// }
/// ```
