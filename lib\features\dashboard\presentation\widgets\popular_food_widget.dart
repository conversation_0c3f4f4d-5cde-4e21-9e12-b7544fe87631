import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/theme/app_colors.dart';

class PopularFoodWidget extends StatelessWidget {
  const PopularFoodWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Popular Food',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            Icon(
              Icons.more_horiz,
              color: AppColors.textSecondary,
              size: 20,
            ),
          ],
        ),
        
        const SizedBox(height: 20),
        
        // Donut Chart
        Expanded(
          child: Stack(
            children: [
              Pie<PERSON><PERSON>(
                PieChartData(
                  sectionsSpace: 2,
                  centerSpaceRadius: 60,
                  sections: [
                    PieChartSectionData(
                      color: Colors.orange,
                      value: 36,
                      title: '',
                      radius: 30,
                    ),
                    PieChartSectionData(
                      color: AppColors.success,
                      value: 30,
                      title: '',
                      radius: 30,
                    ),
                    PieChartSectionData(
                      color: AppColors.error,
                      value: 34,
                      title: '',
                      radius: 30,
                    ),
                  ],
                ),
              ),
              
              // Center text
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '\$120K',
                      style: GoogleFonts.inter(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      'Total',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 20),
        
        // Legend
        Column(
          children: [
            _buildLegendItem(
              color: Colors.orange,
              label: 'Sushi Foods',
              percentage: '36%',
            ),
            const SizedBox(height: 8),
            _buildLegendItem(
              color: AppColors.success,
              label: 'Salad Foods',
              percentage: '30%',
            ),
            const SizedBox(height: 8),
            _buildLegendItem(
              color: AppColors.error,
              label: 'Vegetarian Foods',
              percentage: '34%',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLegendItem({
    required Color color,
    required String label,
    required String percentage,
  }) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Text(
          percentage,
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }
}
