import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../screens/bill_screen.dart';

class BillItemWidget extends StatelessWidget {
  final BillItem bill;
  final VoidCallback onTap;

  const BillItemWidget({
    super.key,
    required this.bill,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          Checkbox(
            value: false,
            onChanged: (value) {},
            activeColor: AppColors.primary,
          ),
          
          const SizedBox(width: 16),
          
          // Order Number
          Expanded(
            flex: 2,
            child: Text(
              bill.id,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          
          // Status
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor(),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                _getStatusText(),
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          
          // Address
          Expanded(
            flex: 3,
            child: Text(
              bill.address,
              style: GoogleFonts.inter(
                fontSize: 13,
                color: AppColors.textSecondary,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          
          // Date
          Expanded(
            flex: 2,
            child: Text(
              bill.date,
              style: GoogleFonts.inter(
                fontSize: 13,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          // Total
          Expanded(
            flex: 1,
            child: Text(
              bill.total,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          
          // Payment Method
          Expanded(
            flex: 2,
            child: Row(
              children: [
                Icon(
                  _getPaymentIcon(),
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 8),
                Text(
                  bill.paymentMethod,
                  style: GoogleFonts.inter(
                    fontSize: 13,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (bill.status) {
      case BillStatus.completed:
        return AppColors.success;
      case BillStatus.cancelled:
        return AppColors.error;
      case BillStatus.pending:
        return Colors.orange;
    }
  }

  String _getStatusText() {
    switch (bill.status) {
      case BillStatus.completed:
        return 'Completed';
      case BillStatus.cancelled:
        return 'Cancelled';
      case BillStatus.pending:
        return 'Pending';
    }
  }

  IconData _getPaymentIcon() {
    switch (bill.paymentMethod.toLowerCase()) {
      case 'card':
        return Icons.credit_card;
      case 'cash':
        return Icons.money;
      default:
        return Icons.payment;
    }
  }
}
