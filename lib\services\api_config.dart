/// إعدادات API للتطبيق
class ApiConfig {
  // ==================== عناوين الخادم ====================
  
  /// عنوان الخادم الأساسي
  static const String baseUrl = 'https://api.wasslti.com'; // يجب تغييره للخادم الحقيقي
  
  /// عنوان خادم التطوير
  static const String devBaseUrl = 'https://dev-api.wasslti.com';
  
  /// عنوان خادم الاختبار
  static const String testBaseUrl = 'https://test-api.wasslti.com';
  
  /// الحصول على عنوان الخادم حسب البيئة
  static String get currentBaseUrl {
    // يمكن تغيير هذا حسب البيئة
    return baseUrl;
  }
  
  // ==================== نقاط النهاية (Endpoints) ====================
  
  /// نقاط نهاية المصادقة
  static const String loginEndpoint = '/api/v1/driver/login';
  static const String verifyOtpEndpoint = '/api/v1/driver/verify-otp';
  static const String refreshTokenEndpoint = '/api/v1/driver/refresh-token';
  static const String logoutEndpoint = '/api/v1/driver/logout';
  static const String profileEndpoint = '/api/v1/driver/profile';
  
  /// نقاط نهاية الطلبات
  static const String ordersEndpoint = '/api/v1/driver/orders';
  static const String acceptOrderEndpoint = '/api/v1/driver/orders/{id}/accept';
  static const String updateOrderStatusEndpoint = '/api/v1/driver/orders/{id}/status';
  static const String orderHistoryEndpoint = '/api/v1/driver/orders/history';
  
  /// نقاط نهاية الموقع
  static const String updateLocationEndpoint = '/api/v1/driver/location';
  static const String getDirectionsEndpoint = '/api/v1/driver/directions';
  
  /// نقاط نهاية المحفظة
  static const String walletEndpoint = '/api/v1/driver/wallet';
  static const String earningsEndpoint = '/api/v1/driver/earnings';
  static const String withdrawEndpoint = '/api/v1/driver/withdraw';
  
  // ==================== إعدادات الطلبات ====================
  
  /// مهلة الاتصال (بالثواني)
  static const int connectionTimeout = 30;
  
  /// مهلة الاستقبال (بالثواني)
  static const int receiveTimeout = 30;
  
  /// مهلة الإرسال (بالثواني)
  static const int sendTimeout = 30;
  
  /// عدد محاولات إعادة الطلب
  static const int maxRetries = 3;
  
  /// فترة الانتظار بين المحاولات (بالثواني)
  static const int retryDelay = 2;
  
  // ==================== رؤوس HTTP ====================
  
  /// رؤوس HTTP الافتراضية
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'WassltiDriver/1.0.0',
    'Platform': 'mobile',
  };
  
  /// رؤوس HTTP مع التوكن
  static Map<String, String> getAuthHeaders(String token) => {
    ...defaultHeaders,
    'Authorization': 'Bearer $token',
  };
  
  // ==================== رموز الاستجابة ====================
  
  /// رموز الاستجابة الناجحة
  static const List<int> successCodes = [200, 201, 202, 204];
  
  /// رموز الاستجابة التي تتطلب إعادة تسجيل الدخول
  static const List<int> authErrorCodes = [401, 403];
  
  /// رموز الاستجابة التي تتطلب إعادة المحاولة
  static const List<int> retryableCodes = [500, 502, 503, 504];
  
  // ==================== إعدادات OTP ====================
  
  /// طول رمز OTP
  static const int otpLength = 6;
  
  /// مدة صلاحية OTP (بالدقائق)
  static const int otpValidityMinutes = 5;
  
  /// عدد محاولات OTP المسموحة
  static const int maxOtpAttempts = 3;
  
  // ==================== إعدادات الموقع ====================
  
  /// فترة تحديث الموقع (بالثواني)
  static const int locationUpdateInterval = 10;
  
  /// دقة الموقع المطلوبة (بالأمتار)
  static const double locationAccuracy = 10.0;
  
  /// المسافة الدنيا لتحديث الموقع (بالأمتار)
  static const double minLocationDistance = 5.0;
  
  // ==================== إعدادات الطلبات ====================
  
  /// مدة انتظار قبول الطلب (بالثواني)
  static const int orderAcceptanceTimeout = 30;
  
  /// عدد الطلبات المعروضة في الصفحة الواحدة
  static const int ordersPerPage = 20;
  
  /// فترة تحديث قائمة الطلبات (بالثواني)
  static const int ordersRefreshInterval = 30;
  
  // ==================== إعدادات التخزين المؤقت ====================
  
  /// مدة صلاحية التخزين المؤقت (بالساعات)
  static const int cacheValidityHours = 24;
  
  /// حجم التخزين المؤقت الأقصى (بالميجابايت)
  static const int maxCacheSizeMB = 50;
  
  // ==================== إعدادات الإشعارات ====================
  
  /// مفتاح Firebase Cloud Messaging
  static const String fcmServerKey = 'YOUR_FCM_SERVER_KEY';
  
  /// موضوع الإشعارات للسائقين
  static const String driverNotificationTopic = 'driver_notifications';
  
  // ==================== إعدادات التطوير ====================
  
  /// تفعيل وضع التطوير
  static const bool isDevelopment = true;
  
  /// تفعيل طباعة طلبات API
  static const bool enableApiLogging = true;
  
  /// تفعيل البيانات التجريبية
  static const bool useMockData = true;
  
  /// تأخير البيانات التجريبية (بالثواني)
  static const int mockDataDelay = 2;
  
  // ==================== دوال مساعدة ====================
  
  /// بناء URL كامل
  static String buildUrl(String endpoint) {
    return '$currentBaseUrl$endpoint';
  }
  
  /// استبدال معاملات URL
  static String replaceUrlParams(String endpoint, Map<String, String> params) {
    String result = endpoint;
    params.forEach((key, value) {
      result = result.replaceAll('{$key}', value);
    });
    return result;
  }
  
  /// التحقق من نجاح الاستجابة
  static bool isSuccessResponse(int statusCode) {
    return successCodes.contains(statusCode);
  }
  
  /// التحقق من خطأ المصادقة
  static bool isAuthError(int statusCode) {
    return authErrorCodes.contains(statusCode);
  }
  
  /// التحقق من إمكانية إعادة المحاولة
  static bool isRetryableError(int statusCode) {
    return retryableCodes.contains(statusCode);
  }
  
  // ==================== إعدادات البيئة ====================
  
  /// تحديد البيئة الحالية
  static Environment get currentEnvironment {
    if (currentBaseUrl == devBaseUrl) return Environment.development;
    if (currentBaseUrl == testBaseUrl) return Environment.testing;
    return Environment.production;
  }
}

/// بيئات التطبيق
enum Environment {
  development,
  testing,
  production,
}

/// امتداد لبيئات التطبيق
extension EnvironmentExtension on Environment {
  String get name {
    switch (this) {
      case Environment.development:
        return 'Development';
      case Environment.testing:
        return 'Testing';
      case Environment.production:
        return 'Production';
    }
  }
  
  bool get isDevelopment => this == Environment.development;
  bool get isTesting => this == Environment.testing;
  bool get isProduction => this == Environment.production;
}
