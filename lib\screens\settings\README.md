# ⚙️ شاشة الإعدادات (Settings Screen)

## 🎨 التصميم

شاشة إعدادات حديثة وأنيقة مصممة خصيصاً لتطبيق السائق مع دعم كامل للوضعين الفاتح والمظلم.

### 🌟 **الميزات الرئيسية:**
- **تصميم حديث ومتجاوب** مع حواف مدورة وظلال ناعمة
- **دعم RTL كامل** للعربية والإنجليزية
- **وضعين مظلم وفاتح** مع انتقالات سلسة
- **تجميع منطقي** للإعدادات في أقسام واضحة
- **واجهة بديهية** مع أيقونات معبرة

## 📱 الأقسام والإعدادات

### **1. إعدادات الحساب**
- ✏️ **تعديل الملف الشخصي** - تحديث بيانات السائق
- 🔒 **تغيير كلمة السر** - تأمين الحساب
- 📄 **إدارة الوثائق** - رخصة القيادة والهوية

### **2. التوافر**
- ⚡ **متصل/غير متصل** - تبديل حالة السائق
- 📍 **مناطق التوصيل المفضلة** - تحديد المناطق

### **3. الإشعارات**
- 🔔 **الإشعارات الفورية** - تفعيل/إلغاء الإشعارات
- 🔊 **الصوت والاهتزاز** - إعدادات التنبيهات

### **4. الأمان**
- 👆 **المصادقة البيومترية** - بصمة الإصبع/الوجه
- 🛡️ **سجل الجلسات** - تتبع تسجيلات الدخول

### **5. الدفع والأرباح**
- 💰 **نظرة عامة على الأرباح** - إحصائيات مالية
- 🏦 **إضافة حساب بنكي** - طرق السحب

### **6. تفضيلات التطبيق**
- 🌐 **اللغة** - العربية/الإنجليزية
- 🌙 **وضع المظهر** - فاتح/مظلم/تلقائي
- 🗺️ **نوع الخريطة** - Google Maps/Waze

### **7. الدعم**
- ❓ **مركز المساعدة** - الأسئلة الشائعة
- 💬 **التواصل مع الدعم** - دردشة مباشرة

### **8. القانونية**
- 📋 **الشروط والأحكام** - قوانين الاستخدام
- 🔒 **سياسة الخصوصية** - حماية البيانات

### **9. تسجيل الخروج**
- 🚪 **تسجيل الخروج** - إنهاء الجلسة بأمان

## 🎨 نظام الألوان

### **الوضع المظلم:**
```dart
backgroundColor: #0F231A    // خلفية رئيسية
cardColor: #1B3B2E         // خلفية الكروت
accentColor: #11B96F       // اللون المميز
textColor: #FFFFFF         // النص الأساسي
```

### **الوضع الفاتح:**
```dart
backgroundColor: #F5F5F5    // خلفية رئيسية
cardColor: #FFFFFF         // خلفية الكروت
accentColor: #11B96F       // اللون المميز
textColor: #000000         // النص الأساسي
```

## 🔧 المكونات

### **الملفات الرئيسية:**
- `settings_screen.dart` - الشاشة الرئيسية
- `settings_demo.dart` - العرض التجريبي

### **المكونات المخصصة:**
- `_buildSection()` - قسم الإعدادات
- `_buildSettingItem()` - عنصر إعداد عادي
- `_buildToggleItem()` - عنصر تبديل (Switch)
- `_buildSelectionItem()` - عنصر اختيار متعدد
- `_buildLogoutButton()` - زر تسجيل الخروج

## 🎯 الاستخدام

### **التنقل للشاشة:**
```dart
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => const SettingsScreen(),
  ),
);
```

### **من الشاشة الرئيسية:**
- انقر على أيقونة القائمة في الشريط العلوي
- اختر "Settings" من القائمة المنسدلة

## 🧪 العرض التجريبي

### **تشغيل العرض:**
```bash
flutter run lib/demo/settings_demo.dart --debug
```

### **الميزات التجريبية:**
- **تبديل الوضع** بين فاتح ومظلم
- **اختبار جميع الإعدادات** والتفاعلات
- **تجربة تسجيل الخروج** مع تأكيد

## 📱 التفاعلات

### **أنواع العناصر:**
1. **عناصر التنقل** - تفتح شاشات فرعية
2. **مفاتيح التبديل** - تغيير حالة فوري
3. **قوائم الاختيار** - حوار اختيار متعدد
4. **أزرار الإجراء** - تنفيذ عمليات مباشرة

### **التأكيدات:**
- **تسجيل الخروج** - حوار تأكيد
- **تغيير الإعدادات الحساسة** - تنبيهات

## 🔄 إدارة الحالة

### **الإعدادات المحلية:**
```dart
bool _isOnline = true;
bool _pushNotifications = true;
String _selectedLanguage = 'العربية';
String _selectedTheme = 'مظلم';
```

### **التكامل مع AuthProvider:**
- **تسجيل الخروج** يستخدم `AuthProvider.logout()`
- **حفظ الإعدادات** في `StorageService`

## 🚀 الميزات المستقبلية

### **قريباً:**
- [ ] **حفظ الإعدادات** في التخزين المحلي
- [ ] **مزامنة الإعدادات** مع الخادم
- [ ] **إعدادات متقدمة** للإشعارات
- [ ] **تخصيص الواجهة** أكثر

### **متقدم:**
- [ ] **نسخ احتياطي للإعدادات**
- [ ] **استيراد/تصدير الإعدادات**
- [ ] **إعدادات المطورين**
- [ ] **وضع التطوير**

## 🎨 التخصيص

### **إضافة إعداد جديد:**
```dart
_buildToggleItem(
  icon: Icons.new_feature,
  title: 'ميزة جديدة',
  value: _newFeature,
  isDark: isDark,
  onChanged: (value) => setState(() => _newFeature = value),
),
```

### **إضافة قسم جديد:**
```dart
_buildSection(
  title: 'قسم جديد',
  isDark: isDark,
  children: [
    // عناصر القسم
  ],
),
```

## ✨ النتيجة

شاشة إعدادات احترافية ومتكاملة توفر:
- ✅ **تجربة مستخدم ممتازة**
- ✅ **تصميم حديث ومتجاوب**
- ✅ **دعم كامل للوضعين**
- ✅ **تنظيم منطقي للإعدادات**
- ✅ **تفاعلات سلسة وبديهية**

**الشاشة جاهزة للاستخدام والتطوير! 🎉**
