import React from 'react';
import { Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  Home, 
  Info, 
  ChefHat, 
  MapPin, 
  Car, 
  MessageCircle, 
  Star,
  Download,
  Gift,
  HelpCircle,
  Users,
  Clock,
  Shield,
  Award,
  Globe,
  ExternalLink
} from 'lucide-react';

const SitemapPage = () => {
  const siteStructure = [
    {
      title: 'الصفحات الرئيسية',
      icon: <Home className="w-6 h-6" />,
      color: 'from-blue-500 to-blue-600',
      pages: [
        { name: 'الصفحة الرئيسية', path: '/', icon: <Home className="w-4 h-4" /> },
        { name: 'تواصل معنا', path: '/contact', icon: <MessageCircle className="w-4 h-4" /> },
        { name: 'خريطة الموقع', path: '/sitemap', icon: <Globe className="w-4 h-4" /> }
      ]
    },
    {
      title: 'أقسام الموقع الرئيسي',
      icon: <Info className="w-6 h-6" />,
      color: 'from-green-500 to-green-600',
      pages: [
        { name: 'من نحن', path: '/#about', icon: <Info className="w-4 h-4" /> },
        { name: 'كيف يعمل وصلتي', path: '/#how-it-works', icon: <Clock className="w-4 h-4" /> },
        { name: 'المطاعم المميزة', path: '/#restaurants', icon: <ChefHat className="w-4 h-4" /> },
        { name: 'المدن المتاحة', path: '/#cities', icon: <MapPin className="w-4 h-4" /> },
        { name: 'آراء العملاء', path: '/#testimonials', icon: <Star className="w-4 h-4" /> },
        { name: 'تحميل التطبيق', path: '/#app-download', icon: <Download className="w-4 h-4" /> },
        { name: 'انضم كسائق', path: '/#drivers', icon: <Car className="w-4 h-4" /> },
        { name: 'برنامج الولاء', path: '/#loyalty', icon: <Gift className="w-4 h-4" /> },
        { name: 'الأسئلة الشائعة', path: '/#faq', icon: <HelpCircle className="w-4 h-4" /> }
      ]
    },
    {
      title: 'خدمات العملاء',
      icon: <Users className="w-6 h-6" />,
      color: 'from-purple-500 to-purple-600',
      pages: [
        { name: 'مركز المساعدة', path: '/help', icon: <HelpCircle className="w-4 h-4" /> },
        { name: 'تتبع الطلب', path: '/track-order', icon: <MapPin className="w-4 h-4" /> },
        { name: 'حسابي', path: '/account', icon: <Users className="w-4 h-4" /> },
        { name: 'طلباتي', path: '/orders', icon: <Clock className="w-4 h-4" /> },
        { name: 'المفضلة', path: '/favorites', icon: <Star className="w-4 h-4" /> }
      ]
    },
    {
      title: 'للشركاء',
      icon: <Award className="w-6 h-6" />,
      color: 'from-orange-500 to-orange-600',
      pages: [
        { name: 'انضم كمطعم شريك', path: '/restaurant-partner', icon: <ChefHat className="w-4 h-4" /> },
        { name: 'تسجيل السائقين', path: '/driver-signup', icon: <Car className="w-4 h-4" /> },
        { name: 'لوحة تحكم المطعم', path: '/restaurant-dashboard', icon: <Award className="w-4 h-4" /> },
        { name: 'تطبيق السائقين', path: '/driver-app', icon: <Download className="w-4 h-4" /> }
      ]
    },
    {
      title: 'المعلومات القانونية',
      icon: <Shield className="w-6 h-6" />,
      color: 'from-gray-500 to-gray-600',
      pages: [
        { name: 'شروط الخدمة', path: '/terms', icon: <Shield className="w-4 h-4" /> },
        { name: 'سياسة الخصوصية', path: '/privacy', icon: <Shield className="w-4 h-4" /> },
        { name: 'سياسة الاسترداد', path: '/refund-policy', icon: <Shield className="w-4 h-4" /> },
        { name: 'سياسة ملفات تعريف الارتباط', path: '/cookies', icon: <Shield className="w-4 h-4" /> }
      ]
    },
    {
      title: 'روابط خارجية',
      icon: <ExternalLink className="w-6 h-6" />,
      color: 'from-indigo-500 to-indigo-600',
      pages: [
        { name: 'تطبيق iOS', path: 'https://apps.apple.com/app/wasslti', icon: <Download className="w-4 h-4" />, external: true },
        { name: 'تطبيق Android', path: 'https://play.google.com/store/apps/details?id=com.wasslti', icon: <Download className="w-4 h-4" />, external: true },
        { name: 'فيسبوك', path: 'https://facebook.com/wasslti', icon: <ExternalLink className="w-4 h-4" />, external: true },
        { name: 'إنستغرام', path: 'https://instagram.com/wasslti', icon: <ExternalLink className="w-4 h-4" />, external: true },
        { name: 'تويتر', path: 'https://twitter.com/wasslti', icon: <ExternalLink className="w-4 h-4" />, external: true }
      ]
    }
  ];

  const stats = [
    { number: '50+', label: 'صفحة ومحتوى', icon: <Globe className="w-6 h-6" /> },
    { number: '12', label: 'مدينة مغربية', icon: <MapPin className="w-6 h-6" /> },
    { number: '500+', label: 'مطعم شريك', icon: <ChefHat className="w-6 h-6" /> },
    { number: '1M+', label: 'مستخدم نشط', icon: <Users className="w-6 h-6" /> }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-white shadow-lg sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link 
              to="/" 
              className="flex items-center space-x-3 rtl:space-x-reverse text-gray-700 hover:text-primary transition-colors"
            >
              <ArrowLeft className="w-6 h-6" />
              <span className="font-semibold rtl-font">العودة للرئيسية</span>
            </Link>
            
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center">
                <span className="text-white font-bold text-2xl">و</span>
              </div>
              <div>
                <span className="text-2xl font-bold text-accent">وصلتي</span>
                <div className="text-xs text-gray-500">Wasslti</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary via-primary/95 to-primary/90 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-72 h-72 bg-white rounded-full blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <Globe className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-5xl font-bold mb-6 rtl-font">خريطة الموقع</h1>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto rtl-font">
            استكشف جميع صفحات وأقسام موقع وصلتي بسهولة. دليل شامل لكل ما نقدمه من خدمات ومحتوى
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-2 text-white">
                  {stat.icon}
                </div>
                <div className="text-2xl font-bold mb-1">{stat.number}</div>
                <div className="text-sm opacity-80 rtl-font">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Sitemap Content */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-accent mb-6 rtl-font">هيكل الموقع</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto rtl-font">
              تصفح جميع أقسام الموقع والخدمات المتاحة
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {siteStructure.map((section, sectionIndex) => (
              <div key={sectionIndex} className="bg-white rounded-3xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500">
                {/* Section Header */}
                <div className={`bg-gradient-to-r ${section.color} p-6 text-white`}>
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                      {section.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold rtl-font">{section.title}</h3>
                      <p className="text-sm opacity-90">{section.pages.length} عنصر</p>
                    </div>
                  </div>
                </div>

                {/* Section Pages */}
                <div className="p-6">
                  <div className="space-y-3">
                    {section.pages.map((page, pageIndex) => (
                      <div key={pageIndex}>
                        {page.external ? (
                          <a
                            href={page.path}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 group"
                          >
                            <div className="flex items-center space-x-3 rtl:space-x-reverse">
                              <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center text-primary">
                                {page.icon}
                              </div>
                              <span className="font-medium text-gray-700 rtl-font">{page.name}</span>
                            </div>
                            <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-primary transition-colors" />
                          </a>
                        ) : page.path.startsWith('/#') ? (
                          <a
                            href={page.path}
                            onClick={(e) => {
                              e.preventDefault();
                              const element = document.querySelector(page.path.substring(1));
                              if (element) {
                                element.scrollIntoView({ behavior: 'smooth' });
                              } else {
                                window.location.href = '/' + page.path;
                              }
                            }}
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 group"
                          >
                            <div className="flex items-center space-x-3 rtl:space-x-reverse">
                              <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center text-primary">
                                {page.icon}
                              </div>
                              <span className="font-medium text-gray-700 rtl-font">{page.name}</span>
                            </div>
                            <ArrowLeft className="w-4 h-4 text-gray-400 group-hover:text-primary transition-colors transform group-hover:-translate-x-1 rtl:group-hover:translate-x-1" />
                          </a>
                        ) : (
                          <Link
                            to={page.path}
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-all duration-300 group"
                          >
                            <div className="flex items-center space-x-3 rtl:space-x-reverse">
                              <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center text-primary">
                                {page.icon}
                              </div>
                              <span className="font-medium text-gray-700 rtl-font">{page.name}</span>
                            </div>
                            <ArrowLeft className="w-4 h-4 text-gray-400 group-hover:text-primary transition-colors transform group-hover:-translate-x-1 rtl:group-hover:translate-x-1" />
                          </Link>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="mt-20">
            <div className="bg-gradient-to-r from-primary via-primary/95 to-primary/90 rounded-3xl p-8 md:p-12 text-white text-center relative overflow-hidden">
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full blur-2xl"></div>
                <div className="absolute bottom-0 right-0 w-40 h-40 bg-white rounded-full blur-3xl"></div>
              </div>
              
              <div className="relative z-10">
                <h3 className="text-3xl font-bold mb-4 rtl-font">إجراءات سريعة</h3>
                <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto rtl-font">
                  الوصول السريع للخدمات الأكثر استخداماً
                </p>
                
                <div className="grid md:grid-cols-3 gap-6">
                  <Link
                    to="/#restaurants"
                    className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/30 transition-all duration-300 hover:scale-105"
                  >
                    <ChefHat className="w-8 h-8 text-white mx-auto mb-3" />
                    <h4 className="font-bold mb-2 rtl-font">تصفح المطاعم</h4>
                    <p className="text-sm opacity-90 rtl-font">اكتشف أفضل المطاعم</p>
                  </Link>
                  
                  <Link
                    to="/contact"
                    className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/30 transition-all duration-300 hover:scale-105"
                  >
                    <MessageCircle className="w-8 h-8 text-white mx-auto mb-3" />
                    <h4 className="font-bold mb-2 rtl-font">تواصل معنا</h4>
                    <p className="text-sm opacity-90 rtl-font">نحن هنا لمساعدتك</p>
                  </Link>
                  
                  <a
                    href="https://apps.apple.com/app/wasslti"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/30 transition-all duration-300 hover:scale-105"
                  >
                    <Download className="w-8 h-8 text-white mx-auto mb-3" />
                    <h4 className="font-bold mb-2 rtl-font">حمل التطبيق</h4>
                    <p className="text-sm opacity-90 rtl-font">متاح على جميع المنصات</p>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-accent text-white py-12">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center space-x-3 rtl:space-x-reverse mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center">
              <span className="text-white font-bold text-2xl">و</span>
            </div>
            <div>
              <span className="text-2xl font-bold">وصلتي</span>
              <div className="text-sm text-gray-400">Wasslti</div>
            </div>
          </div>
          <p className="text-gray-300 rtl-font">
            &copy; 2024 وصلتي. جميع الحقوق محفوظة.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default SitemapPage;