import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/performance_card_widget.dart';
import '../widgets/profile_card_widget.dart';
import '../widgets/performance_chart_widget.dart';
import '../widgets/recent_feedback_widget.dart';
import '../widgets/stats_summary_widget.dart';

class DriversDashboardScreen extends StatefulWidget {
  const DriversDashboardScreen({super.key});

  @override
  State<DriversDashboardScreen> createState() => _DriversDashboardScreenState();
}

class _DriversDashboardScreenState extends State<DriversDashboardScreen> {
  String searchQuery = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Performance Cards Row
                        Row(
                          children: [
                            Expanded(
                              child: PerformanceCardWidget(
                                title: 'Performance',
                                percentage: 50,
                                color: AppColors.success,
                                isCircular: true,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: PerformanceCardWidget(
                                title: 'Min Performance',
                                percentage: 80,
                                color: Colors.orange,
                                isCircular: false,
                                showChart: true,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: PerformanceCardWidget(
                                title: 'Avg Performance',
                                percentage: 75,
                                color: AppColors.error,
                                isCircular: false,
                                showChart: true,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ProfileCardWidget(),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Performance Statistics and Stats Summary Row
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Performance Chart
                            Expanded(
                              flex: 2,
                              child: PerformanceChartWidget(),
                            ),
                            
                            const SizedBox(width: 24),
                            
                            // Stats Summary
                            Expanded(
                              flex: 1,
                              child: StatsSummaryWidget(),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Recent Feedback
                        RecentFeedbackWidget(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Text(
                'Dashboard',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 8),
              const Text('😊', style: TextStyle(fontSize: 20)),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Notifications
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.notifications_outlined,
                    color: AppColors.textSecondary,
                    size: 20,
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // User Profile
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primary,
                child: Text(
                  'JS',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Jhon Smith',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'User',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
