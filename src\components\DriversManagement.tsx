import React, { useState } from 'react';
import { 
  Car, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Star, 
  MapPin, 
  Phone, 
  Mail,
  DollarSign,
  Package,
  Users,
  TrendingUp,
  AlertTriangle,
  Plus,
  RefreshCw,
  FileText,
  Shield,
  Award,
  Navigation,
  Activity
} from 'lucide-react';

interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  licenseNumber: string;
  vehicleType: 'motorcycle' | 'car' | 'bicycle';
  vehicleModel: string;
  vehiclePlate: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended' | 'active' | 'offline';
  rating: number;
  reviewCount: number;
  totalDeliveries: number;
  earnings: number;
  avatar: string;
  city: string;
  zone: string;
  createdAt: string;
  lastActive: string;
  documents: {
    drivingLicense: boolean;
    vehicleRegistration: boolean;
    insurance: boolean;
    criminalRecord: boolean;
  };
  performance: {
    onTimeDeliveries: number;
    averageDeliveryTime: number;
    customerRating: number;
    completionRate: number;
  };
}

const DriversManagement = () => {
  const [drivers, setDrivers] = useState<Driver[]>([
    {
      id: '1',
      name: 'محمد الإدريسي',
      email: '<EMAIL>',
      phone: '+212 6XX-XXXXXX',
      licenseNumber: 'DL123456789',
      vehicleType: 'motorcycle',
      vehicleModel: 'Honda CB 125',
      vehiclePlate: 'A-12345-20',
      status: 'active',
      rating: 4.8,
      reviewCount: 324,
      totalDeliveries: 1250,
      earnings: 15000,
      avatar: 'https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
      city: 'الدار البيضاء',
      zone: 'المعاريف',
      createdAt: '2024-01-15',
      lastActive: '2024-01-20 16:30',
      documents: {
        drivingLicense: true,
        vehicleRegistration: true,
        insurance: true,
        criminalRecord: true
      },
      performance: {
        onTimeDeliveries: 95,
        averageDeliveryTime: 18,
        customerRating: 4.8,
        completionRate: 98
      }
    },
    {
      id: '2',
      name: 'عبد الرحمن بنعلي',
      email: '<EMAIL>',
      phone: '+212 6XX-XXXXXX',
      licenseNumber: 'DL987654321',
      vehicleType: 'car',
      vehicleModel: 'Dacia Logan',
      vehiclePlate: 'B-67890-21',
      status: 'active',
      rating: 4.6,
      reviewCount: 189,
      totalDeliveries: 890,
      earnings: 12500,
      avatar: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
      city: 'الرباط',
      zone: 'أكدال',
      createdAt: '2024-01-10',
      lastActive: '2024-01-20 14:15',
      documents: {
        drivingLicense: true,
        vehicleRegistration: true,
        insurance: false,
        criminalRecord: true
      },
      performance: {
        onTimeDeliveries: 88,
        averageDeliveryTime: 22,
        customerRating: 4.6,
        completionRate: 94
      }
    },
    {
      id: '3',
      name: 'يوسف الحسني',
      email: '<EMAIL>',
      phone: '+212 6XX-XXXXXX',
      licenseNumber: 'DL456789123',
      vehicleType: 'bicycle',
      vehicleModel: 'دراجة كهربائية',
      vehiclePlate: 'N/A',
      status: 'pending',
      rating: 0,
      reviewCount: 0,
      totalDeliveries: 0,
      earnings: 0,
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
      city: 'مراكش',
      zone: 'جليز',
      createdAt: '2024-01-18',
      lastActive: 'لم يتم التفعيل بعد',
      documents: {
        drivingLicense: false,
        vehicleRegistration: false,
        insurance: true,
        criminalRecord: true
      },
      performance: {
        onTimeDeliveries: 0,
        averageDeliveryTime: 0,
        customerRating: 0,
        completionRate: 0
      }
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedCity, setSelectedCity] = useState('all');
  const [selectedVehicleType, setSelectedVehicleType] = useState('all');

  const statuses = [
    { id: 'all', name: 'جميع الحالات', color: 'text-gray-600' },
    { id: 'pending', name: 'في الانتظار', color: 'text-yellow-600' },
    { id: 'approved', name: 'مقبول', color: 'text-blue-600' },
    { id: 'active', name: 'نشط', color: 'text-green-600' },
    { id: 'offline', name: 'غير متصل', color: 'text-gray-600' },
    { id: 'suspended', name: 'موقوف', color: 'text-red-600' },
    { id: 'rejected', name: 'مرفوض', color: 'text-red-600' }
  ];

  const cities = [
    { id: 'all', name: 'جميع المدن' },
    { id: 'الدار البيضاء', name: 'الدار البيضاء' },
    { id: 'الرباط', name: 'الرباط' },
    { id: 'مراكش', name: 'مراكش' },
    { id: 'فاس', name: 'فاس' }
  ];

  const vehicleTypes = [
    { id: 'all', name: 'جميع المركبات' },
    { id: 'motorcycle', name: 'دراجة نارية' },
    { id: 'car', name: 'سيارة' },
    { id: 'bicycle', name: 'دراجة هوائية' }
  ];

  const filteredDrivers = drivers.filter(driver => {
    const matchesSearch = driver.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         driver.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         driver.phone.includes(searchTerm);
    const matchesStatus = selectedStatus === 'all' || driver.status === selectedStatus;
    const matchesCity = selectedCity === 'all' || driver.city === selectedCity;
    const matchesVehicleType = selectedVehicleType === 'all' || driver.vehicleType === selectedVehicleType;
    
    return matchesSearch && matchesStatus && matchesCity && matchesVehicleType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'approved': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'offline': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case 'suspended': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    const statusObj = statuses.find(s => s.id === status);
    return statusObj ? statusObj.name : status;
  };

  const getVehicleTypeText = (type: string) => {
    switch (type) {
      case 'motorcycle': return 'دراجة نارية';
      case 'car': return 'سيارة';
      case 'bicycle': return 'دراجة هوائية';
      default: return type;
    }
  };

  const getVehicleIcon = (type: string) => {
    switch (type) {
      case 'motorcycle': return <Car className="w-4 h-4" />;
      case 'car': return <Car className="w-4 h-4" />;
      case 'bicycle': return <Navigation className="w-4 h-4" />;
      default: return <Car className="w-4 h-4" />;
    }
  };

  const handleApproveDriver = (driverId: string) => {
    setDrivers(prev => prev.map(driver => 
      driver.id === driverId ? { ...driver, status: 'approved' as any } : driver
    ));
  };

  const handleRejectDriver = (driverId: string) => {
    if (confirm('هل أنت متأكد من رفض هذا السائق؟')) {
      setDrivers(prev => prev.map(driver => 
        driver.id === driverId ? { ...driver, status: 'rejected' as any } : driver
      ));
    }
  };

  const handleSuspendDriver = (driverId: string) => {
    if (confirm('هل أنت متأكد من توقيف هذا السائق؟')) {
      setDrivers(prev => prev.map(driver => 
        driver.id === driverId ? { ...driver, status: 'suspended' as any } : driver
      ));
    }
  };

  const handleDeleteDriver = (driverId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا السائق؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      setDrivers(prev => prev.filter(driver => driver.id !== driverId));
    }
  };

  const handleViewDriver = (driver: Driver) => {
    console.log('View driver details:', driver);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white rtl-font">
            إدارة السائقين
          </h1>
          <p className="text-gray-600 dark:text-gray-400 rtl-font">
            إدارة ومتابعة جميع السائقين في المنصة
          </p>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
            <Download className="w-4 h-4" />
            <span>تصدير</span>
          </button>
          <button className="bg-primary text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary/90 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
            <Plus className="w-5 h-5" />
            <span>إضافة سائق</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إجمالي السائقين</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">{drivers.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
              <Car className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">السائقين النشطين</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {drivers.filter(d => d.status === 'active').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">في الانتظار</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {drivers.filter(d => d.status === 'pending').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إجمالي الأرباح</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {drivers.reduce((sum, d) => sum + d.earnings, 0).toLocaleString()} درهم
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="البحث عن سائق..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
            />
          </div>

          {/* Status Filter */}
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {statuses.map(status => (
              <option key={status.id} value={status.id}>{status.name}</option>
            ))}
          </select>

          {/* City Filter */}
          <select
            value={selectedCity}
            onChange={(e) => setSelectedCity(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {cities.map(city => (
              <option key={city.id} value={city.id}>{city.name}</option>
            ))}
          </select>

          {/* Vehicle Type Filter */}
          <select
            value={selectedVehicleType}
            onChange={(e) => setSelectedVehicleType(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {vehicleTypes.map(type => (
              <option key={type.id} value={type.id}>{type.name}</option>
            ))}
          </select>

          {/* Refresh Button */}
          <button className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse">
            <RefreshCw className="w-4 h-4" />
            <span>تحديث</span>
          </button>
        </div>
      </div>

      {/* Drivers Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredDrivers.map((driver) => (
          <div key={driver.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
            {/* Driver Header */}
            <div className="p-6 border-b border-gray-100 dark:border-gray-700">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <img 
                  src={driver.avatar} 
                  alt={driver.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
                    {driver.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {driver.email}
                  </p>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                    {getVehicleIcon(driver.vehicleType)}
                    <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                      {getVehicleTypeText(driver.vehicleType)}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(driver.status)}`}>
                    {getStatusText(driver.status)}
                  </span>
                </div>
              </div>
            </div>

            {/* Driver Info */}
            <div className="p-6">
              {/* Performance Stats */}
              {driver.status === 'active' && (
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse mb-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="font-bold text-gray-800 dark:text-white">{driver.rating}</span>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400">({driver.reviewCount} تقييم)</p>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-gray-800 dark:text-white mb-1">
                      {driver.totalDeliveries}
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 rtl-font">توصيلة</p>
                  </div>
                </div>
              )}

              {/* Location & Vehicle */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                  <MapPin className="w-4 h-4" />
                  <span className="rtl-font">{driver.city} - {driver.zone}</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                  <Car className="w-4 h-4" />
                  <span>{driver.vehicleModel} - {driver.vehiclePlate}</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                  <FileText className="w-4 h-4" />
                  <span>رخصة: {driver.licenseNumber}</span>
                </div>
              </div>

              {/* Documents Status */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                  حالة الوثائق:
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className={`w-3 h-3 rounded-full ${driver.documents.drivingLicense ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">رخصة القيادة</span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className={`w-3 h-3 rounded-full ${driver.documents.vehicleRegistration ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">رخصة المركبة</span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className={`w-3 h-3 rounded-full ${driver.documents.insurance ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">التأمين</span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className={`w-3 h-3 rounded-full ${driver.documents.criminalRecord ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">السجل الجنائي</span>
                  </div>
                </div>
              </div>

              {/* Earnings */}
              {driver.status === 'active' && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-3 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">الأرباح:</span>
                    <span className="font-semibold text-gray-800 dark:text-white">
                      {driver.earnings.toLocaleString()} درهم
                    </span>
                  </div>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">معدل الإنجاز:</span>
                    <span className="text-sm font-medium text-primary">
                      {driver.performance.completionRate}%
                    </span>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-2 rtl:space-x-reverse">
                <button 
                  onClick={() => handleViewDriver(driver)}
                  className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-3 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                >
                  <Eye className="w-4 h-4" />
                  <span className="text-sm font-medium">عرض</span>
                </button>

                {driver.status === 'pending' && (
                  <>
                    <button 
                      onClick={() => handleApproveDriver(driver.id)}
                      className="flex-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 py-2 px-3 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">قبول</span>
                    </button>
                    <button 
                      onClick={() => handleRejectDriver(driver.id)}
                      className="flex-1 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 py-2 px-3 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                    >
                      <XCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">رفض</span>
                    </button>
                  </>
                )}

                {(driver.status === 'active' || driver.status === 'approved') && (
                  <button 
                    onClick={() => handleSuspendDriver(driver.id)}
                    className="flex-1 bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400 py-2 px-3 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-900/40 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                  >
                    <AlertTriangle className="w-4 h-4" />
                    <span className="text-sm font-medium">توقيف</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredDrivers.length === 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-12 text-center shadow-lg">
          <Car className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2 rtl-font">
            لا توجد سائقين
          </h3>
          <p className="text-gray-500 dark:text-gray-500 rtl-font">
            لم يتم العثور على سائقين يطابقون معايير البحث
          </p>
        </div>
      )}
    </div>
  );
};

export default DriversManagement;