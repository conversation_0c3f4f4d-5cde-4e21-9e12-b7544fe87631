import React from 'react';
import { Search, ShoppingCart, CreditCard, Truck, MapPin, Clock, CheckCircle, Star } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const HowItWorks = () => {
  const { t, isRTL } = useLanguage();

  const steps = [
    {
      icon: <Search className="w-10 h-10" />,
      titleKey: "how_it_works.step_1_title",
      descriptionKey: "how_it_works.step_1_desc",
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      delay: "0"
    },
    {
      icon: <ShoppingCart className="w-10 h-10" />,
      titleKey: "how_it_works.step_2_title",
      descriptionKey: "how_it_works.step_2_desc",
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      delay: "200"
    },
    {
      icon: <CreditCard className="w-10 h-10" />,
      titleKey: "how_it_works.step_3_title",
      descriptionKey: "how_it_works.step_3_desc",
      color: "from-orange-500 to-orange-600", 
      bgColor: "bg-orange-50",
      delay: "400"
    },
    {
      icon: <Truck className="w-10 h-10" />,
      titleKey: "how_it_works.step_4_title",
      descriptionKey: "how_it_works.step_4_desc",
      color: "from-primary to-green-600",
      bgColor: "bg-green-50", 
      delay: "600"
    }
  ];

  const features = [
    {
      icon: <Clock className="w-6 h-6" />,
      titleKey: "how_it_works.feature_1",
      descriptionKey: "how_it_works.feature_1_desc"
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      titleKey: "how_it_works.feature_2",
      descriptionKey: "how_it_works.feature_2_desc"
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      titleKey: "how_it_works.feature_3",
      descriptionKey: "how_it_works.feature_3_desc"
    },
    {
      icon: <Star className="w-6 h-6" />,
      titleKey: "how_it_works.feature_4",
      descriptionKey: "how_it_works.feature_4_desc"
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-b from-background to-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-orange-400/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <CheckCircle className="w-4 h-4" />
            <span className="rtl-font">{t('how_it_works.badge')}</span>
          </div>
          <h2 className="text-5xl font-bold text-accent mb-6 rtl-font">
            {t('how_it_works.title')}
            <br />
            <span className="text-primary">{t('how_it_works.subtitle')}</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed rtl-font">
            {t('how_it_works.description')}
          </p>
        </div>

        {/* Steps */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {steps.map((step, index) => (
            <div 
              key={index} 
              className="group relative"
              style={{ animationDelay: `${step.delay}ms` }}
            >
              {/* Connection Line */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-gray-200 to-transparent z-0"></div>
              )}
              
              <div className="relative z-10 text-center group-hover:transform group-hover:scale-105 transition-all duration-500">
                <div className={`w-20 h-20 bg-gradient-to-br ${step.color} rounded-3xl flex items-center justify-center mx-auto mb-6 text-white shadow-2xl group-hover:shadow-3xl group-hover:rotate-3 transition-all duration-500`}>
                  {step.icon}
                </div>
                
                <div className={`${step.bgColor} rounded-2xl p-6 group-hover:shadow-xl transition-all duration-500`}>
                  <h3 className="text-2xl font-bold text-accent mb-4 rtl-font">{t(step.titleKey)}</h3>
                  <p className="text-gray-600 leading-relaxed rtl-font">{t(step.descriptionKey)}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Features Grid */}
        <div className="bg-white rounded-3xl shadow-2xl p-8 md:p-12">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-accent mb-4 rtl-font">{t('how_it_works.why_wasslti')}</h3>
            <p className="text-gray-600 text-lg rtl-font">{t('how_it_works.why_desc')}</p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4 text-primary group-hover:scale-110 group-hover:rotate-6 transition-all duration-300">
                  {feature.icon}
                </div>
                <h4 className="text-lg font-bold text-accent mb-2 rtl-font">{t(feature.titleKey)}</h4>
                <p className="text-gray-600 text-sm rtl-font">{t(feature.descriptionKey)}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-primary to-primary/90 rounded-3xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4 rtl-font">{t('how_it_works.ready_to_try')}</h3>
            <p className="text-lg mb-6 opacity-90 rtl-font">{t('how_it_works.join_customers')}</p>
            <button 
              onClick={() => {
                const restaurantsSection = document.getElementById('restaurants');
                if (restaurantsSection) {
                  restaurantsSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="bg-white text-primary px-8 py-3 rounded-2xl font-bold hover:shadow-xl hover:scale-105 transition-all duration-300"
            >
              {t('how_it_works.start_ordering')}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;