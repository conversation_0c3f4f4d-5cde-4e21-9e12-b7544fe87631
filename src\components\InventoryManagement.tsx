import React, { useState } from 'react';
import { 
  Package, 
  Truck, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  TrendingUp,
  TrendingDown,
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Download,
  RefreshCw,
  Calendar,
  DollarSign,
  BarChart3,
  PieChart,
  Building,
  User,
  Phone,
  Mail,
  MapPin,
  Clock,
  Target,
  Zap
} from 'lucide-react';

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  pricePerUnit: number;
  supplierId: string;
  supplierName: string;
  lastRestocked: string;
  expiryDate?: string;
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'expired';
}

interface Supplier {
  id: string;
  name: string;
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  category: string;
  rating: number;
  totalOrders: number;
  lastOrderDate: string;
  paymentTerms: string;
  status: 'active' | 'inactive' | 'pending';
}

interface PurchaseOrder {
  id: string;
  supplierId: string;
  supplierName: string;
  items: {
    itemId: string;
    itemName: string;
    quantity: number;
    pricePerUnit: number;
    total: number;
  }[];
  totalAmount: number;
  orderDate: string;
  expectedDelivery: string;
  actualDelivery?: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  createdBy: string;
}

const InventoryManagement = () => {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([
    {
      id: 'INV001',
      name: 'دجاج طازج',
      category: 'لحوم ودواجن',
      currentStock: 50,
      minStock: 20,
      maxStock: 100,
      unit: 'كيلو',
      pricePerUnit: 45,
      supplierId: 'SUP001',
      supplierName: 'مزرعة الخير',
      lastRestocked: '2024-01-18T10:00:00Z',
      expiryDate: '2024-01-25T00:00:00Z',
      status: 'in_stock'
    },
    {
      id: 'INV002',
      name: 'طماطم',
      category: 'خضروات',
      currentStock: 15,
      minStock: 25,
      maxStock: 80,
      unit: 'كيلو',
      pricePerUnit: 8,
      supplierId: 'SUP002',
      supplierName: 'مزرعة الأرض الخضراء',
      lastRestocked: '2024-01-19T14:30:00Z',
      expiryDate: '2024-01-22T00:00:00Z',
      status: 'low_stock'
    },
    {
      id: 'INV003',
      name: 'أرز بسمتي',
      category: 'حبوب',
      currentStock: 0,
      minStock: 10,
      maxStock: 50,
      unit: 'كيس 25كيلو',
      pricePerUnit: 180,
      supplierId: 'SUP003',
      supplierName: 'مستودع الحبوب',
      lastRestocked: '2024-01-10T09:00:00Z',
      status: 'out_of_stock'
    }
  ]);

  const [suppliers, setSuppliers] = useState<Supplier[]>([
    {
      id: 'SUP001',
      name: 'مزرعة الخير',
      contactPerson: 'أحمد الفلاح',
      phone: '+212600123456',
      email: '<EMAIL>',
      address: 'طريق الرباط، سلا',
      category: 'لحوم ودواجن',
      rating: 4.8,
      totalOrders: 45,
      lastOrderDate: '2024-01-18T10:00:00Z',
      paymentTerms: '30 يوم',
      status: 'active'
    },
    {
      id: 'SUP002',
      name: 'مزرعة الأرض الخضراء',
      contactPerson: 'فاطمة الزراعية',
      phone: '+212600123457',
      email: '<EMAIL>',
      address: 'منطقة الخيايطة، سلا',
      category: 'خضروات وفواكه',
      rating: 4.6,
      totalOrders: 32,
      lastOrderDate: '2024-01-19T14:30:00Z',
      paymentTerms: '15 يوم',
      status: 'active'
    },
    {
      id: 'SUP003',
      name: 'مستودع الحبوب',
      contactPerson: 'محمد التاجر',
      phone: '+212600123458',
      email: '<EMAIL>',
      address: 'المنطقة الصناعية، الدار البيضاء',
      category: 'حبوب ومواد جافة',
      rating: 4.2,
      totalOrders: 28,
      lastOrderDate: '2024-01-10T09:00:00Z',
      paymentTerms: '45 يوم',
      status: 'active'
    }
  ]);

  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([
    {
      id: 'PO001',
      supplierId: 'SUP001',
      supplierName: 'مزرعة الخير',
      items: [
        {
          itemId: 'INV001',
          itemName: 'دجاج طازج',
          quantity: 50,
          pricePerUnit: 45,
          total: 2250
        }
      ],
      totalAmount: 2250,
      orderDate: '2024-01-18T08:00:00Z',
      expectedDelivery: '2024-01-18T10:00:00Z',
      actualDelivery: '2024-01-18T10:15:00Z',
      status: 'delivered',
      createdBy: 'مدير المخزون'
    },
    {
      id: 'PO002',
      supplierId: 'SUP002',
      supplierName: 'مزرعة الأرض الخضراء',
      items: [
        {
          itemId: 'INV002',
          itemName: 'طماطم',
          quantity: 30,
          pricePerUnit: 8,
          total: 240
        }
      ],
      totalAmount: 240,
      orderDate: '2024-01-20T09:00:00Z',
      expectedDelivery: '2024-01-21T14:00:00Z',
      status: 'confirmed',
      createdBy: 'مدير المخزون'
    }
  ]);

  const [selectedTab, setSelectedTab] = useState('inventory');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock': return 'bg-green-100 text-green-800';
      case 'low_stock': return 'bg-yellow-100 text-yellow-800';
      case 'out_of_stock': return 'bg-red-100 text-red-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'shipped': return 'bg-purple-100 text-purple-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in_stock':
      case 'active':
      case 'delivered':
        return <CheckCircle className="w-4 h-4" />;
      case 'low_stock':
      case 'pending':
      case 'confirmed':
        return <AlertTriangle className="w-4 h-4" />;
      case 'out_of_stock':
      case 'inactive':
      case 'cancelled':
        return <XCircle className="w-4 h-4" />;
      case 'shipped':
        return <Truck className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'in_stock': return 'متوفر';
      case 'low_stock': return 'مخزون منخفض';
      case 'out_of_stock': return 'نفد المخزون';
      case 'expired': return 'منتهي الصلاحية';
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'pending': return 'قيد الانتظار';
      case 'confirmed': return 'مؤكد';
      case 'shipped': return 'تم الشحن';
      case 'delivered': return 'تم التسليم';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDateTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString('ar-MA'),
      time: date.toLocaleTimeString('ar-MA', { hour12: false })
    };
  };

  const isExpiringSoon = (expiryDate?: string) => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const today = new Date();
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 3 && diffDays > 0;
  };

  const isExpired = (expiryDate?: string) => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const today = new Date();
    return expiry < today;
  };

  const filteredInventoryItems = inventoryItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory === 'all' || item.category === filterCategory;
    const matchesStatus = filterStatus === 'all' || item.status === filterStatus;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const lowStockItems = inventoryItems.filter(item => item.status === 'low_stock' || item.status === 'out_of_stock');
  const expiringItems = inventoryItems.filter(item => isExpiringSoon(item.expiryDate));
  const expiredItems = inventoryItems.filter(item => isExpired(item.expiryDate));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">إدارة المخزون والموردين</h2>
          <p className="text-gray-600 rtl-font">تتبع المخزون وإدارة الموردين وطلبات الشراء</p>
        </div>
        
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <RefreshCw className="w-4 h-4" />
            <span className="rtl-font">تحديث</span>
          </button>
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Download className="w-4 h-4" />
            <span className="rtl-font">تصدير</span>
          </button>
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary/90 transition-colors">
            <Plus className="w-5 h-5" />
            <span className="font-medium rtl-font">إضافة عنصر</span>
          </button>
        </div>
      </div>

      {/* Alerts */}
      {(lowStockItems.length > 0 || expiringItems.length > 0 || expiredItems.length > 0) && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {lowStockItems.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <h3 className="font-semibold text-red-800 rtl-font">مخزون منخفض</h3>
              </div>
              <p className="text-red-700 text-sm rtl-font">
                {lowStockItems.length} عنصر يحتاج إعادة تموين
              </p>
            </div>
          )}
          
          {expiringItems.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                <Clock className="w-5 h-5 text-yellow-600" />
                <h3 className="font-semibold text-yellow-800 rtl-font">قارب على الانتهاء</h3>
              </div>
              <p className="text-yellow-700 text-sm rtl-font">
                {expiringItems.length} عنصر ينتهي خلال 3 أيام
              </p>
            </div>
          )}
          
          {expiredItems.length > 0 && (
            <div className="bg-gray-50 border border-gray-200 rounded-xl p-4">
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                <XCircle className="w-5 h-5 text-gray-600" />
                <h3 className="font-semibold text-gray-800 rtl-font">منتهي الصلاحية</h3>
              </div>
              <p className="text-gray-700 text-sm rtl-font">
                {expiredItems.length} عنصر منتهي الصلاحية
              </p>
            </div>
          )}
        </div>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">إجمالي العناصر</p>
              <p className="text-2xl font-bold text-blue-600">{inventoryItems.length}</p>
              <div className="flex items-center mt-2">
                <Package className="w-4 h-4 text-blue-500" />
                <span className="text-sm text-blue-500 font-medium rtl-font">عنصر</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">قيمة المخزون</p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(inventoryItems.reduce((sum, item) => sum + (item.currentStock * item.pricePerUnit), 0))}
              </p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500 font-medium">+8%</span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">هذا الشهر</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">الموردين النشطين</p>
              <p className="text-2xl font-bold text-purple-600">
                {suppliers.filter(s => s.status === 'active').length}
              </p>
              <div className="flex items-center mt-2">
                <Building className="w-4 h-4 text-purple-500" />
                <span className="text-sm text-purple-500 font-medium rtl-font">مورد</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Building className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">طلبات الشراء</p>
              <p className="text-2xl font-bold text-orange-600">
                {purchaseOrders.filter(po => po.status === 'pending' || po.status === 'confirmed').length}
              </p>
              <div className="flex items-center mt-2">
                <Truck className="w-4 h-4 text-orange-500" />
                <span className="text-sm text-orange-500 font-medium rtl-font">نشط</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
              <Truck className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InventoryManagement;
