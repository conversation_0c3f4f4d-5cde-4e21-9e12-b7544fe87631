import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:async';
import '../../constants/driver_typography.dart';
import '../../models/driver_status.dart';
import '../../widgets/simple_top_bar.dart';
import '../../utils/settings_helper.dart';
import '../../widgets/incoming_order_popup.dart';

/// الشاشة الرئيسية للسائق مع خريطة كاملة الشاشة
/// تتضمن شريط علوي عائم، نافذة الطلبات الواردة، وزر الوصول السريع
class DriverHomeScreen extends StatefulWidget {
  final String driverName;
  
  const DriverHomeScreen({
    super.key,
    this.driverName = 'Yassine',
  });

  @override
  State<DriverHomeScreen> createState() => _DriverHomeScreenState();
}

class _DriverHomeScreenState extends State<DriverHomeScreen>
    with TickerProviderStateMixin {

  GoogleMapController? _mapController;

  // موقع السائق الحالي (الدار البيضاء كمثال)
  static const LatLng _driverLocation = LatLng(33.5731, -7.5898);

  // حالة السائق
  DriverStatus _driverStatus = DriverStatus.working;

  // حالة الطلب الوارد
  bool _hasIncomingOrder = false;
  
  // متحكم الرسوم المتحركة للنبضة
  late AnimationController _pulseController;
  
  // متحكم الرسوم المتحركة لنافذة الطلب
  late AnimationController _orderPopupController;
  late Animation<Offset> _orderPopupAnimation;

  @override
  void initState() {
    super.initState();
    
    // إعداد رسوم النبضة المتحركة
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseController.repeat(reverse: true);
    
    // إعداد رسوم نافذة الطلب المتحركة
    _orderPopupController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _orderPopupAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _orderPopupController,
      curve: Curves.easeOut,
    ));
    
    // محاكاة طلب وارد بعد 3 ثوان
    Timer(const Duration(seconds: 3), () {
      _showIncomingOrder();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _orderPopupController.dispose();
    super.dispose();
  }

  void _showIncomingOrder() {
    setState(() {
      _hasIncomingOrder = true;
    });
    _orderPopupController.forward();
  }

  void _hideIncomingOrder() {
    _orderPopupController.reverse().then((_) {
      setState(() {
        _hasIncomingOrder = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      body: Stack(
        children: [
          // الخريطة كاملة الشاشة
          _buildMap(),
          
          // الشريط العلوي العائم
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: FloatingTopBar(
              driverStatus: _driverStatus,
              onStatusTap: _handleStatusTap,
              onSettingsTap: _handleSettingsTap,
              onSupportTap: _handleSupportTap,
            ),
          ),
          
          // أزرار التكبير والتصغير
          Positioned(
            right: 16,
            top: MediaQuery.of(context).padding.top + 100,
            child: _buildZoomControls(isDark),
          ),
          
          // نافذة الطلب الوارد
          if (_hasIncomingOrder)
            Positioned(
              bottom: 100,
              left: 16,
              right: 16,
              child: SlideTransition(
                position: _orderPopupAnimation,
                child: IncomingOrderPopup(
                  onAccept: _handleAcceptOrder,
                  onDecline: _handleDeclineOrder,
                ),
              ),
            ),
          

        ],
      ),
    );
  }

  Widget _buildMap() {
    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
      },
      initialCameraPosition: const CameraPosition(
        target: _driverLocation,
        zoom: 15.0,
      ),
      markers: _buildMarkers(),
      myLocationEnabled: false, // نستخدم علامة مخصصة
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      mapToolbarEnabled: false,
      compassEnabled: false,
      trafficEnabled: true,
      buildingsEnabled: true,
      mapType: MapType.normal,
      style: _getMapStyle(),
    );
  }

  Set<Marker> _buildMarkers() {
    return {
      // علامة موقع السائق مع النبضة
      Marker(
        markerId: const MarkerId('driver_location'),
        position: _driverLocation,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        infoWindow: const InfoWindow(
          title: 'موقعك الحالي',
          snippet: 'السائق متصل',
        ),
      ),
      
      // علامات المطاعم (نقاط الاستلام)
      const Marker(
        markerId: MarkerId('restaurant_1'),
        position: LatLng(33.5751, -7.5878),
        icon: BitmapDescriptor.defaultMarker,
        infoWindow: InfoWindow(
          title: 'مطعم البركة',
          snippet: 'نقطة استلام',
        ),
      ),
      
      // علامات العملاء (نقاط التسليم)
      const Marker(
        markerId: MarkerId('customer_1'),
        position: LatLng(33.5711, -7.5918),
        icon: BitmapDescriptor.defaultMarker,
        infoWindow: InfoWindow(
          title: 'عميل',
          snippet: 'نقطة تسليم',
        ),
      ),
    };
  }

  Widget _buildZoomControls(bool isDark) {
    return Column(
      children: [
        _buildZoomButton(
          icon: Icons.add,
          onPressed: () => _mapController?.animateCamera(
            CameraUpdate.zoomIn(),
          ),
          isDark: isDark,
        ),
        const SizedBox(height: 8),
        _buildZoomButton(
          icon: Icons.remove,
          onPressed: () => _mapController?.animateCamera(
            CameraUpdate.zoomOut(),
          ),
          isDark: isDark,
        ),
      ],
    );
  }

  Widget _buildZoomButton({
    required IconData icon,
    required VoidCallback onPressed,
    required bool isDark,
  }) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: isDark ? Colors.white : Colors.black87,
          size: 20,
        ),
        onPressed: onPressed,
      ),
    );
  }

  String? _getMapStyle() {
    // يمكن إضافة نمط مخصص للخريطة هنا
    return null;
  }

  // Event handlers
  void _handleStatusTap() {
    setState(() {
      _driverStatus = _driverStatus.nextStatus;
    });
    _showSnackBar('Status changed to: ${_driverStatus.displayText}');
  }

  void _handleProfileTap() {
    // TODO: Open profile
    _showSnackBar('Open Profile');
  }

  void _handleWalletTap() {
    // TODO: Open wallet
    _showSnackBar('Open Wallet');
  }

  void _handleDeliveriesTap() {
    // TODO: Open deliveries list
    _showSnackBar('Open Deliveries');
  }

  void _handleSupportTap() {
    // TODO: Open support
    _showSnackBar('Open Support');
  }

  void _handleAcceptOrder() {
    _hideIncomingOrder();
    _showSnackBar('تم قبول الطلب');
  }

  void _handleDeclineOrder() {
    _hideIncomingOrder();
    _showSnackBar('تم رفض الطلب');
  }

  void _handleSettingsTap() {
    SettingsHelper.navigateToSettingsWithProvider(context);
  }

  void _handleCallSupportTap() {
    // TODO: Call support
    _showSnackBar('Call Support');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyMedium,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF11B96F),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
