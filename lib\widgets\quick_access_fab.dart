import 'package:flutter/material.dart';

/// زر الوصول السريع العائم (FAB)
/// يتوسع لإظهار قائمة من الخيارات السريعة
class QuickAccessFAB extends StatefulWidget {
  final VoidCallback onSettingsTap;
  final VoidCallback onDeliveriesTap;
  final VoidCallback onCallSupportTap;

  const QuickAccessFAB({
    super.key,
    required this.onSettingsTap,
    required this.onDeliveriesTap,
    required this.onCallSupportTap,
  });

  @override
  State<QuickAccessFAB> createState() => _QuickAccessFABState();
}

class _QuickAccessFABState extends State<QuickAccessFAB>
    with TickerProviderStateMixin {
  
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _rotateAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    _rotateAnimation = Tween<double>(
      begin: 0.0,
      end: 0.75, // 3/4 دورة (270 درجة)
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // خيارات الوصول السريع
        AnimatedBuilder(
          animation: _expandAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _expandAnimation.value,
              child: Opacity(
                opacity: _expandAnimation.value,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildQuickOption(
                      icon: Icons.settings,
                      label: 'الإعدادات',
                      onTap: () {
                        _toggleExpansion();
                        widget.onSettingsTap();
                      },
                      isDark: isDark,
                    ),
                    
                    const SizedBox(height: 12),
                    
                    _buildQuickOption(
                      icon: Icons.local_shipping,
                      label: 'توصيلاتي',
                      onTap: () {
                        _toggleExpansion();
                        widget.onDeliveriesTap();
                      },
                      isDark: isDark,
                    ),
                    
                    const SizedBox(height: 12),
                    
                    _buildQuickOption(
                      icon: Icons.phone,
                      label: 'اتصال بالدعم',
                      onTap: () {
                        _toggleExpansion();
                        widget.onCallSupportTap();
                      },
                      isDark: isDark,
                    ),
                    
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            );
          },
        ),
        
        // الزر الرئيسي
        AnimatedBuilder(
          animation: _rotateAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotateAnimation.value * 2 * 3.14159, // تحويل إلى راديان
              child: FloatingActionButton(
                onPressed: _toggleExpansion,
                backgroundColor: const Color(0xFF11B96F),
                foregroundColor: Colors.white,
                elevation: 8,
                child: Icon(
                  _isExpanded ? Icons.close : Icons.menu,
                  size: 24,
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildQuickOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // التسمية
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // الأيقونة
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: const Color(0xFF11B96F),
              size: 20,
            ),
          ),
        ],
      ),
    );
  }
}
