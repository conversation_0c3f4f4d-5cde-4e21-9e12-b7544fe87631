import React from 'react';
import { Smartphone, Apple, PlayCircle, Download, Star, Shield, Zap, Award } from 'lucide-react';

const AppDownload = () => {
  const features = [
    {
      icon: <Zap className="w-6 h-6" />,
      title: "طلب سريع",
      description: "اطلب في ثوانٍ معدودة"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "دفع آمن",
      description: "حماية كاملة لبياناتك"
    },
    {
      icon: <Star className="w-6 h-6" />,
      title: "تتبع مباشر",
      description: "راقب طلبك لحظة بلحظة"
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: "عروض حصرية",
      description: "خصومات خاصة للتطبيق"
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-accent via-accent/95 to-accent text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-orange-400/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div>
            <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/20 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <Download className="w-4 h-4" />
              <span className="rtl-font">حمل التطبيق</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold mb-6 rtl-font">
              احصل على تطبيق
              <br />
              <span className="text-primary">وصلتي</span>
            </h2>
            
            <p className="text-xl text-gray-300 mb-8 leading-relaxed rtl-font">
              حمل تطبيقنا للجوال للحصول على أفضل تجربة توصيل طعام. اطلب بشكل أسرع، تتبع في الوقت الفعلي، واستمتع بعروض حصرية للتطبيق فقط.
            </p>
            
            {/* Features */}
            <div className="grid grid-cols-2 gap-4 mb-8">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center text-primary">
                    {feature.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold rtl-font">{feature.title}</h4>
                    <p className="text-sm text-gray-400 rtl-font">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Download Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <a 
                href="https://apps.apple.com/app/wasslti" 
                target="_blank"
                rel="noopener noreferrer"
                className="group flex items-center justify-center space-x-3 rtl:space-x-reverse bg-black text-white px-6 py-4 rounded-2xl hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-xl"
              >
                <Apple className="w-8 h-8" />
                <div className="text-right rtl:text-left">
                  <div className="text-xs opacity-80">حمل من</div>
                  <div className="text-lg font-bold">App Store</div>
                </div>
              </a>
              
              <a 
                href="https://play.google.com/store/apps/details?id=com.wasslti" 
                target="_blank"
                rel="noopener noreferrer"
                className="group flex items-center justify-center space-x-3 rtl:space-x-reverse bg-black text-white px-6 py-4 rounded-2xl hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-xl"
              >
                <PlayCircle className="w-8 h-8" />
                <div className="text-right rtl:text-left">
                  <div className="text-xs opacity-80">متوفر على</div>
                  <div className="text-lg font-bold">Google Play</div>
                </div>
              </a>
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 text-center">
              <div className="border-r border-gray-600 pr-4 rtl:border-r-0 rtl:border-l rtl:pr-0 rtl:pl-4">
                <div className="text-2xl font-bold text-primary">2M+</div>
                <div className="text-sm text-gray-400 rtl-font">تحميل</div>
              </div>
              <div className="border-r border-gray-600 pr-4 rtl:border-r-0 rtl:border-l rtl:pr-0 rtl:pl-4">
                <div className="text-2xl font-bold text-primary">4.9</div>
                <div className="text-sm text-gray-400 rtl-font">تقييم التطبيق</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">24/7</div>
                <div className="text-sm text-gray-400 rtl-font">دعم فني</div>
              </div>
            </div>
          </div>
          
          {/* Phone Mockup */}
          <div className="relative">
            <div className="relative z-10 max-w-sm mx-auto">
              {/* Phone Frame */}
              <div className="relative bg-gray-900 rounded-[3rem] p-2 shadow-2xl">
                <div className="bg-black rounded-[2.5rem] overflow-hidden">
                  {/* Screen */}
                  <div className="relative">
                    <img 
                      src="https://images.pexels.com/photos/788946/pexels-photo-788946.jpeg?auto=compress&cs=tinysrgb&w=400&h=800&fit=crop"
                      alt="Wasslti Mobile App"
                      className="w-full h-[600px] object-cover"
                    />
                    
                    {/* App UI Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/20">
                      <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 mb-4">
                          <div className="flex items-center space-x-3 rtl:space-x-reverse">
                            <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                              <span className="text-white font-bold">و</span>
                            </div>
                            <div>
                              <h3 className="font-bold rtl-font">وصلتي</h3>
                              <p className="text-sm opacity-80 rtl-font">طعامك المفضل</p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                          <div className="bg-primary/20 backdrop-blur-sm rounded-xl p-3 text-center">
                            <div className="text-lg font-bold">15 دقيقة</div>
                            <div className="text-xs opacity-80">توصيل سريع</div>
                          </div>
                          <div className="bg-primary/20 backdrop-blur-sm rounded-xl p-3 text-center">
                            <div className="text-lg font-bold">500+</div>
                            <div className="text-xs opacity-80">مطعم</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-primary/20 rounded-full animate-pulse"></div>
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-orange-400/20 rounded-full animate-pulse delay-1000"></div>
            </div>
            
            {/* Background Glow */}
            <div className="absolute inset-0 bg-primary/20 rounded-3xl blur-3xl scale-110"></div>
          </div>
        </div>
        
        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4 rtl-font">جاهز للبدء؟</h3>
            <p className="text-gray-300 mb-6 rtl-font">انضم إلى ملايين المستخدمين الذين يثقون في وصلتي</p>
            <button 
              onClick={() => window.open('https://apps.apple.com/app/wasslti', '_blank')}
              className="bg-primary text-white px-8 py-3 rounded-2xl font-bold hover:bg-primary/90 hover:scale-105 transition-all duration-300"
            >
              حمل التطبيق الآن
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppDownload;