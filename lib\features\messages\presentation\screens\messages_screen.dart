import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/message_list_widget.dart';
import '../widgets/chat_widget.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> {
  String searchQuery = '';
  String selectedFilter = 'All';
  String? selectedChatId;

  final List<String> filters = ['All', 'Compose', 'Sent', 'Important', 'Draft', 'Trash'];

  final List<MessageContact> contacts = [
    MessageContact(
      id: '1',
      name: '<PERSON>',
      message: 'Lorem about your Design',
      time: '10:30 AM',
      avatar: 'assets/images/avatar1.png',
      isOnline: true,
      unreadCount: 0,
    ),
    MessageContact(
      id: '2',
      name: '<PERSON>',
      message: '<PERSON><PERSON> about your Design',
      time: '10:30 AM',
      avatar: 'assets/images/avatar2.png',
      isOnline: false,
      unreadCount: 2,
    ),
    MessageContact(
      id: '3',
      name: 'Darrell Steward',
      message: 'Lorem about your Design',
      time: '10:30 AM',
      avatar: 'assets/images/avatar3.png',
      isOnline: true,
      unreadCount: 0,
    ),
    MessageContact(
      id: '4',
      name: 'Ralph Edwards',
      message: 'Lorem about your Design',
      time: '10:30 AM',
      avatar: 'assets/images/avatar4.png',
      isOnline: false,
      unreadCount: 1,
    ),
    MessageContact(
      id: '5',
      name: 'Leslie Alexander',
      message: 'Lorem about your Design',
      time: '10:30 AM',
      avatar: 'assets/images/avatar5.png',
      isOnline: true,
      unreadCount: 0,
    ),
    MessageContact(
      id: '6',
      name: 'Wade Warren',
      message: 'Lorem about your Design',
      time: '10:30 AM',
      avatar: 'assets/images/avatar6.png',
      isOnline: false,
      unreadCount: 0,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: Row(
                    children: [
                      // Messages List
                      Container(
                        width: 400,
                        decoration: const BoxDecoration(
                          color: AppColors.white,
                          border: Border(
                            right: BorderSide(
                              color: AppColors.inputBorder,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Column(
                          children: [
                            // Filters
                            _buildFilters(),
                            
                            // Messages List
                            Expanded(
                              child: MessageListWidget(
                                contacts: contacts,
                                searchQuery: searchQuery,
                                selectedFilter: selectedFilter,
                                selectedChatId: selectedChatId,
                                onContactSelected: (contactId) {
                                  setState(() {
                                    selectedChatId = contactId;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Chat Area
                      Expanded(
                        child: selectedChatId != null
                            ? ChatWidget(
                                contact: contacts.firstWhere((c) => c.id == selectedChatId),
                              )
                            : _buildEmptyChat(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Text(
                'Inbox',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 8),
              const Text('😊', style: TextStyle(fontSize: 20)),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Notifications
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.notifications_outlined,
                    color: AppColors.textSecondary,
                    size: 20,
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // User Profile
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primary,
                child: Text(
                  'JS',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Jhon Smith',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'User',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: filters.map((filter) {
          final isSelected = selectedFilter == filter;
          return Container(
            margin: const EdgeInsets.only(bottom: 4),
            child: ListTile(
              leading: _getFilterIcon(filter),
              title: Text(
                filter,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? AppColors.primary : AppColors.textSecondary,
                ),
              ),
              onTap: () {
                setState(() {
                  selectedFilter = filter;
                });
              },
              selected: isSelected,
              selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              dense: true,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _getFilterIcon(String filter) {
    IconData icon;
    switch (filter) {
      case 'Compose':
        icon = Icons.edit_outlined;
        break;
      case 'Sent':
        icon = Icons.send_outlined;
        break;
      case 'Important':
        icon = Icons.star_outline;
        break;
      case 'Draft':
        icon = Icons.drafts_outlined;
        break;
      case 'Trash':
        icon = Icons.delete_outline;
        break;
      default:
        icon = Icons.inbox_outlined;
    }
    
    final isSelected = selectedFilter == filter;
    return Icon(
      icon,
      color: isSelected ? AppColors.primary : AppColors.textSecondary,
      size: 20,
    );
  }

  Widget _buildEmptyChat() {
    return Container(
      color: AppColors.backgroundLight,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'Select a conversation',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose a contact to start messaging',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MessageContact {
  final String id;
  final String name;
  final String message;
  final String time;
  final String avatar;
  final bool isOnline;
  final int unreadCount;

  MessageContact({
    required this.id,
    required this.name,
    required this.message,
    required this.time,
    required this.avatar,
    required this.isOnline,
    required this.unreadCount,
  });
}
