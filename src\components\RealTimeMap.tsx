import React, { useState, useEffect } from 'react';
import { 
  MapPin, 
  Car, 
  Clock, 
  Navigation, 
  Zap, 
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Filter,
  Eye,
  MoreVertical,
  Phone,
  MessageCircle
} from 'lucide-react';

interface Driver {
  id: string;
  name: string;
  lat: number;
  lng: number;
  status: 'available' | 'busy' | 'offline';
  rating: number;
  currentOrder?: string;
  phone: string;
}

interface Order {
  id: string;
  restaurantName: string;
  restaurantLat: number;
  restaurantLng: number;
  customerLat: number;
  customerLng: number;
  driverLat?: number;
  driverLng?: number;
  status: 'preparing' | 'pickup' | 'delivery' | 'completed';
  estimatedTime: number;
  driverId?: string;
  customerName: string;
  total: number;
}

interface Restaurant {
  id: string;
  name: string;
  lat: number;
  lng: number;
  isOpen: boolean;
  activeOrders: number;
  rating: number;
}

const RealTimeMap = () => {
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Mock data
  useEffect(() => {
    const mockDrivers: Driver[] = [
      {
        id: '1',
        name: 'أحمد محمد',
        lat: 33.5731,
        lng: -7.5898,
        status: 'busy',
        rating: 4.8,
        currentOrder: 'ORD001',
        phone: '+212600123456'
      },
      {
        id: '2',
        name: 'يوسف العلوي',
        lat: 33.5851,
        lng: -7.6034,
        status: 'available',
        rating: 4.9,
        phone: '+212600123457'
      },
      {
        id: '3',
        name: 'محمد الإدريسي',
        lat: 33.5641,
        lng: -7.5756,
        status: 'busy',
        rating: 4.7,
        currentOrder: 'ORD002',
        phone: '+212600123458'
      }
    ];

    const mockOrders: Order[] = [
      {
        id: 'ORD001',
        restaurantName: 'مطعم الأصالة',
        restaurantLat: 33.5731,
        restaurantLng: -7.5898,
        customerLat: 33.5891,
        customerLng: -7.6134,
        driverLat: 33.5751,
        driverLng: -7.5918,
        status: 'delivery',
        estimatedTime: 15,
        driverId: '1',
        customerName: 'سارة أحمد',
        total: 120
      },
      {
        id: 'ORD002',
        restaurantName: 'مطعم الشام',
        restaurantLat: 33.5641,
        restaurantLng: -7.5756,
        customerLat: 33.5781,
        customerLng: -7.5896,
        status: 'pickup',
        estimatedTime: 8,
        driverId: '3',
        customerName: 'محمد الكريم',
        total: 85
      },
      {
        id: 'ORD003',
        restaurantName: 'مطعم البحر',
        restaurantLat: 33.5891,
        restaurantLng: -7.6134,
        customerLat: 33.5671,
        customerLng: -7.5776,
        status: 'preparing',
        estimatedTime: 25,
        customerName: 'فاطمة الزهراء',
        total: 95
      }
    ];

    const mockRestaurants: Restaurant[] = [
      {
        id: '1',
        name: 'مطعم الأصالة',
        lat: 33.5731,
        lng: -7.5898,
        isOpen: true,
        activeOrders: 5,
        rating: 4.6
      },
      {
        id: '2',
        name: 'مطعم الشام',
        lat: 33.5641,
        lng: -7.5756,
        isOpen: true,
        activeOrders: 3,
        rating: 4.8
      },
      {
        id: '3',
        name: 'مطعم البحر',
        lat: 33.5891,
        lng: -7.6134,
        isOpen: false,
        activeOrders: 0,
        rating: 4.4
      }
    ];

    setDrivers(mockDrivers);
    setOrders(mockOrders);
    setRestaurants(mockRestaurants);
  }, []);

  const refreshData = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-500';
      case 'busy': return 'bg-yellow-500';
      case 'offline': return 'bg-gray-500';
      case 'preparing': return 'bg-blue-500';
      case 'pickup': return 'bg-orange-500';
      case 'delivery': return 'bg-purple-500';
      case 'completed': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available': return 'متاح';
      case 'busy': return 'مشغول';
      case 'offline': return 'غير متصل';
      case 'preparing': return 'قيد التحضير';
      case 'pickup': return 'في الطريق للاستلام';
      case 'delivery': return 'في الطريق للتوصيل';
      case 'completed': return 'مكتمل';
      default: return status;
    }
  };

  const filteredDrivers = drivers.filter(driver => {
    if (selectedFilter === 'all') return true;
    return driver.status === selectedFilter;
  });

  const filteredOrders = orders.filter(order => {
    if (selectedFilter === 'all') return true;
    return order.status === selectedFilter;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">الخريطة التفاعلية</h2>
          <p className="text-gray-600 rtl-font">تتبع السائقين والطلبات في الوقت الفعلي</p>
        </div>
        
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {/* Filters */}
          <select
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="all">جميع العناصر</option>
            <option value="available">السائقين المتاحين</option>
            <option value="busy">السائقين المشغولين</option>
            <option value="preparing">طلبات قيد التحضير</option>
            <option value="delivery">طلبات قيد التوصيل</option>
          </select>
          
          <button
            onClick={refreshData}
            disabled={isLoading}
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span className="rtl-font">تحديث</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Map Area */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            {/* Map Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-800 rtl-font">خريطة الدار البيضاء</h3>
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-600 rtl-font">متاح ({drivers.filter(d => d.status === 'available').length})</span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm text-gray-600 rtl-font">مشغول ({drivers.filter(d => d.status === 'busy').length})</span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-600 rtl-font">طلبات ({orders.length})</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Simulated Map */}
            <div className="relative h-96 bg-gradient-to-br from-blue-50 to-green-50">
              {/* Map Background */}
              <div className="absolute inset-0 opacity-20">
                <svg viewBox="0 0 400 300" className="w-full h-full">
                  {/* Streets */}
                  <path d="M0,100 L400,100" stroke="#ccc" strokeWidth="2"/>
                  <path d="M0,200 L400,200" stroke="#ccc" strokeWidth="2"/>
                  <path d="M100,0 L100,300" stroke="#ccc" strokeWidth="2"/>
                  <path d="M200,0 L200,300" stroke="#ccc" strokeWidth="2"/>
                  <path d="M300,0 L300,300" stroke="#ccc" strokeWidth="2"/>
                </svg>
              </div>
              
              {/* Drivers */}
              {filteredDrivers.map((driver, index) => (
                <div
                  key={driver.id}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                  style={{
                    left: `${20 + index * 25}%`,
                    top: `${30 + index * 20}%`
                  }}
                  onClick={() => setSelectedItem(driver)}
                >
                  <div className={`w-8 h-8 rounded-full ${getStatusColor(driver.status)} flex items-center justify-center shadow-lg border-2 border-white`}>
                    <Car className="w-4 h-4 text-white" />
                  </div>
                  <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded text-xs font-medium shadow-lg whitespace-nowrap rtl-font">
                    {driver.name}
                  </div>
                </div>
              ))}
              
              {/* Orders */}
              {filteredOrders.map((order, index) => (
                <div
                  key={order.id}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                  style={{
                    left: `${60 + index * 15}%`,
                    top: `${20 + index * 25}%`
                  }}
                  onClick={() => setSelectedItem(order)}
                >
                  <div className={`w-6 h-6 rounded-full ${getStatusColor(order.status)} flex items-center justify-center shadow-lg border-2 border-white`}>
                    <MapPin className="w-3 h-3 text-white" />
                  </div>
                </div>
              ))}
              
              {/* Restaurants */}
              {restaurants.map((restaurant, index) => (
                <div
                  key={restaurant.id}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                  style={{
                    left: `${15 + index * 30}%`,
                    top: `${70 + index * 10}%`
                  }}
                  onClick={() => setSelectedItem(restaurant)}
                >
                  <div className={`w-6 h-6 rounded-lg ${restaurant.isOpen ? 'bg-green-600' : 'bg-gray-400'} flex items-center justify-center shadow-lg border-2 border-white`}>
                    <span className="text-white text-xs font-bold">م</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Details Panel */}
        <div className="space-y-4">
          {/* Live Stats */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h3 className="font-semibold text-gray-800 mb-4 rtl-font">إحصائيات مباشرة</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 rtl-font">السائقين النشطين</span>
                <span className="font-bold text-green-600">{drivers.filter(d => d.status !== 'offline').length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 rtl-font">الطلبات الجارية</span>
                <span className="font-bold text-blue-600">{orders.filter(o => o.status !== 'completed').length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 rtl-font">المطاعم المفتوحة</span>
                <span className="font-bold text-purple-600">{restaurants.filter(r => r.isOpen).length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 rtl-font">متوسط وقت التوصيل</span>
                <span className="font-bold text-orange-600">18 دقيقة</span>
              </div>
            </div>
          </div>

          {/* Selected Item Details */}
          {selectedItem && (
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-800 rtl-font">تفاصيل العنصر</h3>
                <button
                  onClick={() => setSelectedItem(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              {selectedItem.phone ? (
                // Driver Details
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Car className="w-5 h-5 text-primary" />
                    <span className="font-medium rtl-font">{selectedItem.name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 rtl-font">الحالة</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getStatusColor(selectedItem.status)}`}>
                      {getStatusText(selectedItem.status)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 rtl-font">التقييم</span>
                    <span className="font-medium">⭐ {selectedItem.rating}</span>
                  </div>
                  <div className="flex space-x-2 rtl:space-x-reverse mt-4">
                    <button className="flex-1 bg-primary text-white py-2 px-4 rounded-lg text-sm rtl-font">
                      <Phone className="w-4 h-4 inline ml-2" />
                      اتصال
                    </button>
                    <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg text-sm rtl-font">
                      <MessageCircle className="w-4 h-4 inline ml-2" />
                      رسالة
                    </button>
                  </div>
                </div>
              ) : selectedItem.customerName ? (
                // Order Details
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <MapPin className="w-5 h-5 text-primary" />
                    <span className="font-medium rtl-font">طلب #{selectedItem.id}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 rtl-font">العميل</span>
                    <span className="font-medium rtl-font">{selectedItem.customerName}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 rtl-font">المطعم</span>
                    <span className="font-medium rtl-font">{selectedItem.restaurantName}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 rtl-font">الحالة</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getStatusColor(selectedItem.status)}`}>
                      {getStatusText(selectedItem.status)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 rtl-font">المبلغ</span>
                    <span className="font-bold text-green-600">{selectedItem.total} درهم</span>
                  </div>
                </div>
              ) : (
                // Restaurant Details
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <span className="w-5 h-5 bg-primary rounded text-white text-xs flex items-center justify-center">م</span>
                    <span className="font-medium rtl-font">{selectedItem.name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 rtl-font">الحالة</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${selectedItem.isOpen ? 'bg-green-500' : 'bg-gray-500'}`}>
                      {selectedItem.isOpen ? 'مفتوح' : 'مغلق'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 rtl-font">الطلبات النشطة</span>
                    <span className="font-medium">{selectedItem.activeOrders}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 rtl-font">التقييم</span>
                    <span className="font-medium">⭐ {selectedItem.rating}</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RealTimeMap;
