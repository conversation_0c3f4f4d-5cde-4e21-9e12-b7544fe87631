import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'lib/core/theme/app_colors.dart';
import 'lib/features/settings/presentation/screens/settings_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Settings Test',
      theme: ThemeData(
        primarySwatch: Colors.green,
        textTheme: GoogleFonts.interTextTheme(),
      ),
      home: const SettingsScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
