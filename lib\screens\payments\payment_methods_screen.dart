import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';
import 'add_bank_account_screen.dart';
import '../earnings/earnings_overview_screen.dart';

/// Payment methods management screen
class PaymentMethodsScreen extends StatefulWidget {
  const PaymentMethodsScreen({super.key});

  @override
  State<PaymentMethodsScreen> createState() => _PaymentMethodsScreenState();
}

class _PaymentMethodsScreenState extends State<PaymentMethodsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Mock data - في التطبيق الحقيقي سيأتي من API
  final List<Map<String, dynamic>> _bankAccounts = [
    {
      'id': '1',
      'bankName': 'Al Rajhi Bank',
      'bankNameAr': 'مصرف الراجحي',
      'accountNumber': '****1234',
      'iban': 'SA**************1234',
      'isDefault': true,
      'isVerified': true,
    },
    {
      'id': '2',
      'bankName': 'National Commercial Bank',
      'bankNameAr': 'البنك الأهلي التجاري',
      'accountNumber': '****5678',
      'iban': 'SA**************5678',
      'isDefault': false,
      'isVerified': false,
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Payment Methods', 'طرق الدفع'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.add, color: Color(0xFF11B96F)),
              onPressed: () => _addBankAccount(),
            ),
          ],
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Header
                _buildHeader(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Quick Actions
                _buildQuickActions(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Bank Accounts
                _buildBankAccounts(isDark, languageProvider),
                
                const SizedBox(height: 16),
                
                // Add Account Button
                _buildAddAccountButton(isDark, languageProvider),
                
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF11B96F),
            const Color(0xFF11B96F).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF11B96F).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.payment,
            size: 60,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('Payment Management', 'إدارة المدفوعات'),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Manage your bank accounts and payment preferences',
              'إدارة حساباتك البنكية وتفضيلات الدفع'
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(bool isDark, LanguageProvider languageProvider) {
    return Row(
      children: [
        Expanded(
          child: _buildQuickActionCard(
            title: languageProvider.getText('View Earnings', 'عرض الأرباح'),
            subtitle: languageProvider.getText('Check your earnings', 'تحقق من أرباحك'),
            icon: Icons.trending_up,
            color: Colors.blue,
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const EarningsOverviewScreen()),
            ),
            isDark: isDark,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickActionCard(
            title: languageProvider.getText('Payment History', 'تاريخ المدفوعات'),
            subtitle: languageProvider.getText('View transactions', 'عرض المعاملات'),
            icon: Icons.history,
            color: Colors.orange,
            onTap: () {
              // Navigate to payment history
            },
            isDark: isDark,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankAccounts(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageProvider.getText('Bank Accounts', 'الحسابات البنكية'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              Text(
                '${_bankAccounts.length} ${languageProvider.getText('accounts', 'حسابات')}',
                style: TextStyle(
                  fontSize: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (_bankAccounts.isEmpty)
            _buildEmptyState(isDark, languageProvider)
          else
            ..._bankAccounts.map((account) => _buildBankAccountCard(account, isDark, languageProvider)),
        ],
      ),
    );
  }

  Widget _buildEmptyState(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.account_balance_outlined,
            size: 64,
            color: isDark ? Colors.grey[600] : Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('No Bank Accounts', 'لا توجد حسابات بنكية'),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Add a bank account to receive your earnings',
              'أضف حساباً بنكياً لاستلام أرباحك'
            ),
            style: TextStyle(
              fontSize: 14,
              color: isDark ? Colors.grey[500] : Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBankAccountCard(Map<String, dynamic> account, bool isDark, LanguageProvider languageProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF0F231A) : const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: account['isDefault'] 
              ? const Color(0xFF11B96F) 
              : (isDark ? Colors.grey[700]! : Colors.grey[200]!),
          width: account['isDefault'] ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.account_balance,
                  color: Color(0xFF11B96F),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageProvider.isArabic ? account['bankNameAr'] : account['bankName'],
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    Text(
                      account['accountNumber'],
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
                onSelected: (value) => _handleAccountAction(value, account),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'default',
                    child: Text(languageProvider.getText('Set as Default', 'تعيين كافتراضي')),
                  ),
                  PopupMenuItem(
                    value: 'edit',
                    child: Text(languageProvider.getText('Edit', 'تعديل')),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Text(
                      languageProvider.getText('Delete', 'حذف'),
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              if (account['isDefault'])
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    languageProvider.getText('Default', 'افتراضي'),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF11B96F),
                    ),
                  ),
                ),
              if (account['isDefault']) const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: account['isVerified'] 
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      account['isVerified'] ? Icons.verified : Icons.pending,
                      size: 12,
                      color: account['isVerified'] ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      account['isVerified'] 
                          ? languageProvider.getText('Verified', 'مُتحقق')
                          : languageProvider.getText('Pending', 'في الانتظار'),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: account['isVerified'] ? Colors.green : Colors.orange,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            account['iban'],
            style: TextStyle(
              fontSize: 12,
              color: isDark ? Colors.grey[500] : Colors.grey[500],
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddAccountButton(bool isDark, LanguageProvider languageProvider) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: _addBankAccount,
        style: OutlinedButton.styleFrom(
          foregroundColor: const Color(0xFF11B96F),
          side: const BorderSide(color: Color(0xFF11B96F)),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
        icon: const Icon(Icons.add),
        label: Text(
          languageProvider.getText('Add New Bank Account', 'إضافة حساب بنكي جديد'),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _addBankAccount() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AddBankAccountScreen()),
    );
    
    if (result == true) {
      // Refresh bank accounts list
      // In real app, this would fetch from API
      setState(() {
        // Mock adding new account
      });
    }
  }

  void _handleAccountAction(String action, Map<String, dynamic> account) {
    final languageProvider = context.read<LanguageProvider>();
    
    switch (action) {
      case 'default':
        setState(() {
          for (var acc in _bankAccounts) {
            acc['isDefault'] = acc['id'] == account['id'];
          }
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              languageProvider.getText('Default account updated', 'تم تحديث الحساب الافتراضي')
            ),
            backgroundColor: const Color(0xFF11B96F),
          ),
        );
        break;
      case 'edit':
        // Navigate to edit screen
        break;
      case 'delete':
        _showDeleteConfirmation(account);
        break;
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> account) {
    final languageProvider = context.read<LanguageProvider>();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageProvider.getText('Delete Account', 'حذف الحساب')),
        content: Text(
          languageProvider.getText(
            'Are you sure you want to delete this bank account?',
            'هل أنت متأكد من حذف هذا الحساب البنكي؟'
          )
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageProvider.getText('Cancel', 'إلغاء')),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _bankAccounts.removeWhere((acc) => acc['id'] == account['id']);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    languageProvider.getText('Account deleted', 'تم حذف الحساب')
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(languageProvider.getText('Delete', 'حذف')),
          ),
        ],
      ),
    );
  }
}
