import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/bill_item_widget.dart';

class BillScreen extends StatefulWidget {
  const BillScreen({super.key});

  @override
  State<BillScreen> createState() => _BillScreenState();
}

class _BillScreenState extends State<BillScreen> {
  String searchQuery = '';
  String selectedPeriod = 'Monthly';
  
  final List<BillItem> bills = [
    BillItem(
      id: 'Order #1',
      status: BillStatus.completed,
      address: '4518 Glenwood',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$6.05',
      paymentMethod: 'Card',
    ),
    BillItem(
      id: 'Order #2',
      status: BillStatus.completed,
      address: '5542 Glenwood',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$3.19',
      paymentMethod: 'Card',
    ),
    BillItem(
      id: 'Order #3',
      status: BillStatus.cancelled,
      address: '4518 Haymarket Sq',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$9.05',
      paymentMethod: 'Cash',
    ),
    BillItem(
      id: 'Order #4',
      status: BillStatus.completed,
      address: '2510 Glenwood Sq',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$7.05',
      paymentMethod: 'Card',
    ),
    BillItem(
      id: 'Order #5',
      status: BillStatus.cancelled,
      address: '4518 St. King St. Johns',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$9.05',
      paymentMethod: 'Cash',
    ),
    BillItem(
      id: 'Order #6',
      status: BillStatus.completed,
      address: '4502 Preston Rd',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$6.05',
      paymentMethod: 'Card',
    ),
    BillItem(
      id: 'Order #7',
      status: BillStatus.completed,
      address: '2510 Glenwood Sq',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$6.19',
      paymentMethod: 'Card',
    ),
    BillItem(
      id: 'Order #8',
      status: BillStatus.pending,
      address: '2510 Thornridge Cir',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$6.05',
      paymentMethod: 'Card',
    ),
    BillItem(
      id: 'Order #9',
      status: BillStatus.pending,
      address: '2118 Thornridge Cir',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$6.05',
      paymentMethod: 'Card',
    ),
    BillItem(
      id: 'Order #10',
      status: BillStatus.pending,
      address: '2118 Royal Ln. Mesa',
      method: 'Visa Card',
      date: '27/05/2022 - 8:14',
      total: '\$6.05',
      paymentMethod: 'Card',
    ),
  ];

  List<BillItem> get filteredBills {
    if (searchQuery.isEmpty) return bills;
    return bills.where((bill) =>
        bill.id.toLowerCase().contains(searchQuery.toLowerCase()) ||
        bill.address.toLowerCase().contains(searchQuery.toLowerCase())).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.shadow,
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // Content Header
                          _buildContentHeader(),
                          
                          // Table Header
                          _buildTableHeader(),
                          
                          // Bills List
                          Expanded(
                            child: ListView.builder(
                              itemCount: filteredBills.length,
                              itemBuilder: (context, index) {
                                return BillItemWidget(
                                  bill: filteredBills[index],
                                  onTap: () => _showBillDetails(filteredBills[index]),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Text(
                'Bill',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 8),
              const Text('😊', style: TextStyle(fontSize: 20)),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Notifications
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.notifications_outlined,
                    color: AppColors.textSecondary,
                    size: 20,
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // User Profile
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primary,
                child: Text(
                  'JS',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Jhon Smith',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContentHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Period Dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.inputBorder),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButton<String>(
              value: selectedPeriod,
              underline: const SizedBox(),
              items: ['Monthly', 'Weekly', 'Daily'].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(
                    value,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: AppColors.textPrimary,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  selectedPeriod = newValue!;
                });
              },
            ),
          ),
          
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          Checkbox(
            value: false,
            onChanged: (value) {},
            activeColor: AppColors.primary,
          ),
          
          const SizedBox(width: 16),
          
          // Headers
          Expanded(
            flex: 2,
            child: Text(
              'Number',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 2,
            child: Text(
              'Status',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 3,
            child: Text(
              'Address',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 2,
            child: Text(
              'Date',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 1,
            child: Text(
              'Total',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 2,
            child: Text(
              'Payment Method',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showBillDetails(BillItem bill) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Bill Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order: ${bill.id}'),
            Text('Status: ${bill.status.name}'),
            Text('Address: ${bill.address}'),
            Text('Date: ${bill.date}'),
            Text('Total: ${bill.total}'),
            Text('Payment: ${bill.paymentMethod}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}

enum BillStatus {
  completed,
  cancelled,
  pending,
}

class BillItem {
  final String id;
  final BillStatus status;
  final String address;
  final String method;
  final String date;
  final String total;
  final String paymentMethod;

  BillItem({
    required this.id,
    required this.status,
    required this.address,
    required this.method,
    required this.date,
    required this.total,
    required this.paymentMethod,
  });
}
