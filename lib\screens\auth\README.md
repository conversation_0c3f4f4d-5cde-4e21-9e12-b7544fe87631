# شاشات المصادقة (Authentication Screens)

## 📱 صفحة تسجيل الدخول (Login Screen)

### 🎨 التصميم
صفحة تسجيل الدخول مصممة بناءً على التصميمات المقدمة مع دعم كامل للوضع الفاتح والمظلم:

#### الوضع الفاتح (Light Mode):
- خلفية بيضاء نظيفة
- حقول إدخال بحدود رمادية فاتحة وظلال خفيفة
- نصوص سوداء ورمادية داكنة
- زر أخضر (#11B96F) مع نص أبيض

#### الوضع المظلم (Dark Mode):
- خلفية داكنة (#0F231A)
- حقول إدخال بخلفية (#1B3B2E) وحدود داكنة
- نصوص بيضاء ورمادية فاتحة
- زر أخضر (#11B96F) مع نص أبيض

### 🔧 المكونات

#### العناصر الأساسية:
1. **العنوان الرئيسي**: "Welcome to Wasslti Partner"
2. **العنوان الفرعي**: "Log in to start delivering orders."
3. **حقل رقم الهاتف**: مع أيقونة هاتف
4. **حقل كلمة المرور**: مع أيقونة قفل وإمكانية إظهار/إخفاء
5. **رابط نسيان كلمة المرور**: "Forgot Password?"
6. **زر تسجيل الدخول**: أخضر مع مؤشر تحميل
7. **فاصل**: "Or continue with"
8. **زر Google**: مع شعار Google
9. **رابط التواصل**: "Don't have an account? Contact admin."

#### الميزات التفاعلية:
- ✅ **التحقق من صحة البيانات** (Validation)
- ✅ **إظهار/إخفاء كلمة المرور**
- ✅ **مؤشر التحميل** أثناء تسجيل الدخول
- ✅ **رسائل تأكيد** (SnackBar)
- ✅ **تصميم متجاوب** (Responsive)

### 🎯 الاستخدام

```dart
import 'package:flutter/material.dart';
import 'screens/auth/login_screen.dart';

// في التطبيق الرئيسي
MaterialApp(
  home: const LoginScreen(),
)
```

### 🔄 التكامل مع نظام الثيمات

الصفحة تستخدم نظام الثيمات المخصص:
- `DriverColors` للألوان
- `DriverTypography` للخطوط
- `DriverThemes` للثيمات العامة

### 📝 المهام المستقبلية (TODO)

1. **تنفيذ منطق تسجيل الدخول الفعلي**:
   ```dart
   void _handleLogin() async {
     // API call to authenticate user
     // Handle success/error responses
     // Navigate to main app
   }
   ```

2. **تنفيذ تسجيل الدخول بـ Google**:
   ```dart
   void _handleGoogleLogin() async {
     // Google Sign-In implementation
     // Firebase Auth integration
   }
   ```

3. **تنفيذ صفحة نسيان كلمة المرور**:
   ```dart
   void _handleForgotPassword() {
     // Navigate to forgot password screen
   }
   ```

4. **تنفيذ التواصل مع الإدارة**:
   ```dart
   void _handleContactAdmin() {
     // Open contact methods (phone, email, chat)
   }
   ```

5. **إضافة شعار Google الفعلي**:
   - استبدال الملف النائب في `assets/images/google_logo.png`

### 🧪 الاختبار

```dart
// في test/widget_test.dart
testWidgets('Login screen loads correctly', (WidgetTester tester) async {
  await tester.pumpWidget(const WassltiPartnerApp());
  
  expect(find.text('Welcome to Wasslti Partner'), findsOneWidget);
  expect(find.text('Log In'), findsOneWidget);
});
```

### 🎨 التخصيص

يمكن تخصيص الألوان والخطوط من خلال:
- `lib/constants/driver_colors.dart`
- `lib/constants/driver_typography.dart`
- `lib/constants/driver_themes.dart`

### 📱 التوافق

- ✅ **iOS & Android**
- ✅ **الوضع الفاتح والمظلم**
- ✅ **أحجام شاشات مختلفة**
- ✅ **دعم RTL** (جاهز للعربية)

---

## 📱 صفحة تأكيد رقم الهاتف (OTP Verification)

### 🎨 التصميم
صفحة تأكيد رقم الهاتف مصممة بناءً على التصميمات المقدمة مع دعم كامل للوضع الفاتح والمظلم:

#### الوضع الفاتح (Light Mode):
- خلفية بيضاء نظيفة
- حقول OTP بحدود رمادية مع تركيز أخضر
- شعار بخلفية رمادية فاتحة وحدود داكنة
- زر أخضر (#11B96F) مع نص أبيض

#### الوضع المظلم (Dark Mode):
- خلفية داكنة (#0F231A)
- حقول OTP بخلفية (#1B3B2E) وحدود خضراء عند التركيز
- شعار بخلفية داكنة وحدود خضراء
- زر أخضر (#11B96F) مع نص أبيض

### 🔧 المكونات

#### العناصر الأساسية:
1. **زر الرجوع**: في أعلى اليسار
2. **الشعار**: أيقونة الشحن في مربع مدور
3. **العنوان الرئيسي**: "Verify Your Number"
4. **العنوان الفرعي**: وصف مع رقم الهاتف المخفي جزئياً
5. **حقول OTP**: 6 حقول منفصلة للأرقام
6. **رابط إعادة الإرسال**: مع عداد تنازلي (30 ثانية)
7. **زر التأكيد**: يتفعل عند ملء جميع الحقول

#### الميزات التفاعلية:
- ✅ **التنقل التلقائي** بين حقول OTP
- ✅ **التركيز التلقائي** على أول حقل
- ✅ **العداد التنازلي** لإعادة الإرسال
- ✅ **التحقق من اكتمال الكود** قبل التفعيل
- ✅ **تنسيق رقم الهاتف** مع إخفاء جزئي
- ✅ **مؤشر التحميل** أثناء التحقق

### 🎯 الاستخدام

```dart
import 'package:flutter/material.dart';
import 'screens/auth/otp_verification_screen.dart';

// الانتقال إلى صفحة OTP
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => OtpVerificationScreen(
      phoneNumber: '+212 6XXXXXXXX',
    ),
  ),
);
```
