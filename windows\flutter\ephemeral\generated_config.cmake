# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "F:\\wssalti_pos" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter\\flutter"
  "PROJECT_DIR=F:\\wssalti_pos"
  "FLUTTER_ROOT=C:\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=F:\\wssalti_pos\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=F:\\wssalti_pos"
  "FLUTTER_TARGET=F:\\wssalti_pos\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=F:\\wssalti_pos\\.dart_tool\\package_config.json"
)
