import 'package:flutter/material.dart';
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/dashboard/presentation/screens/dashboard_screen.dart';
import '../../features/orders/presentation/screens/order_history_screen.dart';
import '../../features/orders/presentation/screens/orders_screen.dart';
import '../../features/bills/presentation/screens/bill_screen.dart';
import '../../features/drivers/presentation/screens/drivers_dashboard_screen.dart';
import '../../features/drivers/presentation/screens/drivers_orders_screen.dart';
import '../../features/drivers/presentation/screens/drivers_feedback_screen.dart';
import '../../features/messages/presentation/screens/messages_screen.dart';
import '../../features/notifications/presentation/screens/notifications_screen.dart';
import '../../features/bulk_supply/presentation/screens/bulk_supply_screen.dart';
import '../../features/favorite_menu/presentation/screens/favorite_menu_screen.dart';
import '../../features/settings/presentation/screens/settings_screen.dart';

class AppRoutes {
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String orderHistory = '/order-history';
  static const String orders = '/orders';
  static const String favoriteMenu = '/favorite-menu';
  static const String bills = '/bills';
  static const String messages = '/messages';
  static const String notifications = '/notifications';
  static const String bulkSupply = '/bulk-supply';
  static const String driversDashboard = '/drivers-dashboard';
  static const String driversOrders = '/drivers-orders';
  static const String driversFeedback = '/drivers-feedback';
  static const String settings = '/settings';

  static Map<String, WidgetBuilder> get routes => {
    login: (context) => const LoginScreen(),
    dashboard: (context) => const DashboardScreen(),
    orderHistory: (context) => const OrderHistoryScreen(),
    orders: (context) => const OrdersScreen(),
    favoriteMenu: (context) => const FavoriteMenuScreen(),
    bills: (context) => const BillScreen(),
    messages: (context) => const MessagesScreen(),
    notifications: (context) => const NotificationsScreen(),
    bulkSupply: (context) => const BulkSupplyScreen(),
    driversDashboard: (context) => const DriversDashboardScreen(),
    driversOrders: (context) => const DriversOrdersScreen(),
    driversFeedback: (context) => const DriversFeedbackScreen(),
    settings: (context) => const SettingsScreen(),
  };

  static Route<dynamic>? onGenerateRoute(RouteSettings routeSettings) {
    switch (routeSettings.name) {
      case login:
        return MaterialPageRoute(builder: (context) => const LoginScreen());
      case dashboard:
        return MaterialPageRoute(builder: (context) => const DashboardScreen());
      case orderHistory:
        return MaterialPageRoute(builder: (context) => const OrderHistoryScreen());
      case orders:
        return MaterialPageRoute(builder: (context) => const OrdersScreen());
      case favoriteMenu:
        return MaterialPageRoute(builder: (context) => const FavoriteMenuScreen());
      case bills:
        return MaterialPageRoute(builder: (context) => const BillScreen());
      case messages:
        return MaterialPageRoute(builder: (context) => const MessagesScreen());
      case notifications:
        return MaterialPageRoute(builder: (context) => const NotificationsScreen());
      case bulkSupply:
        return MaterialPageRoute(builder: (context) => const BulkSupplyScreen());
      case driversDashboard:
        return MaterialPageRoute(builder: (context) => const DriversDashboardScreen());
      case driversOrders:
        return MaterialPageRoute(builder: (context) => const DriversOrdersScreen());
      case driversFeedback:
        return MaterialPageRoute(builder: (context) => const DriversFeedbackScreen());
      case settings:
        return MaterialPageRoute(builder: (context) => const SettingsScreen());
      default:
        return MaterialPageRoute(
          builder: (context) => Scaffold(
            body: Center(
              child: Text('Page not found: ${routeSettings.name}'),
            ),
          ),
        );
    }
  }
}
