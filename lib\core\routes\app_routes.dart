import 'package:flutter/material.dart';
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/dashboard/presentation/screens/dashboard_screen.dart';
import '../../features/orders/presentation/screens/order_history_screen.dart';
import '../../features/bills/presentation/screens/bill_screen.dart';
import '../../features/drivers/presentation/screens/drivers_dashboard_screen.dart';
import '../../features/drivers/presentation/screens/drivers_orders_screen.dart';
import '../../features/drivers/presentation/screens/drivers_feedback_screen.dart';
import '../../features/settings/presentation/screens/settings_screen.dart';

class AppRoutes {
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String orderHistory = '/order-history';
  static const String bills = '/bills';
  static const String driversDashboard = '/drivers-dashboard';
  static const String driversOrders = '/drivers-orders';
  static const String driversFeedback = '/drivers-feedback';
  static const String settings = '/settings';

  static Map<String, WidgetBuilder> get routes => {
    login: (context) => const LoginScreen(),
    dashboard: (context) => const DashboardScreen(),
    orderHistory: (context) => const OrderHistoryScreen(),
    bills: (context) => const BillScreen(),
    driversDashboard: (context) => const DriversDashboardScreen(),
    driversOrders: (context) => const DriversOrdersScreen(),
    driversFeedback: (context) => const DriversFeedbackScreen(),
    settings: (context) => const SettingsScreen(),
  };

  static Route<dynamic>? onGenerateRoute(RouteSettings routeSettings) {
    switch (routeSettings.name) {
      case login:
        return MaterialPageRoute(builder: (context) => const LoginScreen());
      case dashboard:
        return MaterialPageRoute(builder: (context) => const DashboardScreen());
      case orderHistory:
        return MaterialPageRoute(builder: (context) => const OrderHistoryScreen());
      case bills:
        return MaterialPageRoute(builder: (context) => const BillScreen());
      case driversDashboard:
        return MaterialPageRoute(builder: (context) => const DriversDashboardScreen());
      case driversOrders:
        return MaterialPageRoute(builder: (context) => const DriversOrdersScreen());
      case driversFeedback:
        return MaterialPageRoute(builder: (context) => const DriversFeedbackScreen());
      case settings:
        return MaterialPageRoute(builder: (context) => const SettingsScreen());
      default:
        return MaterialPageRoute(
          builder: (context) => Scaffold(
            body: Center(
              child: Text('Page not found: ${routeSettings.name}'),
            ),
          ),
        );
    }
  }
}
