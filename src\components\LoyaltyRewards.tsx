import React from 'react';
import { Gift, Star, Crown, Zap, Award, Coins, Target, TrendingUp } from 'lucide-react';

const LoyaltyRewards = () => {
  const rewards = [
    {
      icon: <Gift className="w-10 h-10" />,
      title: "مكافأة الترحيب",
      titleEn: "Welcome Bonus",
      description: "احصل على خصم 50 درهم على طلبك الأول",
      descriptionEn: "Get 50 MAD off your first order",
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: <Coins className="w-10 h-10" />,
      title: "نظام النقاط",
      titleEn: "Points System",
      description: "اكسب نقطة واحدة مقابل كل 10 درهم تنفقها",
      descriptionEn: "Earn 1 point for every 10 MAD spent",
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: <Crown className="w-10 h-10" />,
      title: "عضوية VIP",
      titleEn: "VIP Status",
      description: "افتح مطاعم حصرية وخصومات مميزة",
      descriptionEn: "Unlock exclusive restaurants and discounts",
      color: "from-yellow-500 to-yellow-600",
      bgColor: "bg-yellow-50"
    },
    {
      icon: <Zap className="w-10 h-10" />,
      title: "توصيل مجاني",
      titleEn: "Free Delivery",
      description: "توصيل مجاني على الطلبات أكثر من 100 درهم",
      descriptionEn: "Free delivery on orders over 100 MAD",
      color: "from-primary to-green-600",
      bgColor: "bg-green-50"
    }
  ];

  const tiers = [
    {
      name: "برونزي",
      nameEn: "Bronze",
      minOrders: 0,
      benefits: ["خصم 5%", "نقاط مضاعفة أحياناً"],
      color: "from-orange-600 to-orange-700",
      icon: <Award className="w-6 h-6" />
    },
    {
      name: "فضي",
      nameEn: "Silver", 
      minOrders: 10,
      benefits: ["خصم 10%", "توصيل مجاني أحياناً", "دعم أولوية"],
      color: "from-gray-400 to-gray-600",
      icon: <Star className="w-6 h-6" />
    },
    {
      name: "ذهبي",
      nameEn: "Gold",
      minOrders: 25,
      benefits: ["خصم 15%", "توصيل مجاني دائماً", "عروض حصرية"],
      color: "from-yellow-400 to-yellow-600",
      icon: <Crown className="w-6 h-6" />
    },
    {
      name: "بلاتيني",
      nameEn: "Platinum",
      minOrders: 50,
      benefits: ["خصم 20%", "وصول مبكر للعروض", "مدير حساب شخصي"],
      color: "from-purple-400 to-purple-600",
      icon: <TrendingUp className="w-6 h-6" />
    }
  ];

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-72 h-72 bg-yellow-400/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <Gift className="w-4 h-4" />
            <span className="rtl-font">برنامج الولاء</span>
          </div>
          <h2 className="text-5xl font-bold text-accent mb-6 rtl-font">
            مكافآت الولاء
            <br />
            <span className="text-primary">وصلتي</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed rtl-font">
            كل طلب يقربك من مكافآت مذهلة. انضم إلى برنامج الولاء الخاص بنا وابدأ في الادخار اليوم!
          </p>
        </div>

        {/* Rewards Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {rewards.map((reward, index) => (
            <div key={index} className="group text-center">
              <div className={`w-20 h-20 bg-gradient-to-br ${reward.color} rounded-3xl flex items-center justify-center mx-auto mb-6 text-white shadow-2xl group-hover:shadow-3xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500`}>
                {reward.icon}
              </div>
              
              <div className={`${reward.bgColor} rounded-2xl p-6 group-hover:shadow-xl transition-all duration-500`}>
                <h3 className="text-xl font-bold text-accent mb-3 rtl-font">{reward.title}</h3>
                <p className="text-gray-600 leading-relaxed rtl-font">{reward.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Loyalty Tiers */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-accent mb-4 rtl-font">مستويات العضوية</h3>
            <p className="text-gray-600 text-lg rtl-font">كلما طلبت أكثر، كلما حصلت على مكافآت أفضل</p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {tiers.map((tier, index) => (
              <div key={index} className="relative group">
                <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border-2 border-transparent hover:border-primary/20">
                  <div className={`w-16 h-16 bg-gradient-to-br ${tier.color} rounded-2xl flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform duration-300`}>
                    {tier.icon}
                  </div>
                  
                  <h4 className="text-xl font-bold text-accent mb-2 rtl-font">{tier.name}</h4>
                  <p className="text-sm text-gray-500 mb-4 rtl-font">من {tier.minOrders} طلب</p>
                  
                  <ul className="space-y-2">
                    {tier.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600">
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                        <span className="rtl-font">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {index === 2 && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-primary text-white px-3 py-1 rounded-full text-xs font-bold">
                      الأكثر شعبية
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-primary via-primary/95 to-primary/90 rounded-3xl p-8 md:p-12 text-white text-center relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 right-0 w-40 h-40 bg-white rounded-full blur-3xl"></div>
          </div>
          
          <div className="relative z-10">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Target className="w-10 h-10 text-white" />
            </div>
            
            <h3 className="text-3xl font-bold mb-4 rtl-font">جاهز لبدء الكسب؟</h3>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto rtl-font">
              انضم إلى برنامج مكافآت وصلتي اليوم واحصل على فوائد فورية في طلبك التالي
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={() => alert('سيتم توجيهك لصفحة التسجيل في برنامج المكافآت...')}
                className="bg-white text-primary px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 hover:scale-105 transition-all duration-300 shadow-xl"
              >
                انضم لبرنامج المكافآت
              </button>
              <button 
                onClick={() => alert('سيتم فتح صفحة تفاصيل برنامج المكافآت...')}
                className="bg-white/20 backdrop-blur-sm text-white border-2 border-white/30 px-8 py-4 rounded-2xl font-bold hover:bg-white/30 hover:scale-105 transition-all duration-300"
              >
                تعرف على المزيد
              </button>
            </div>
            
            <div className="grid grid-cols-3 gap-8 mt-12 pt-8 border-t border-white/20">
              <div>
                <div className="text-2xl font-bold mb-1">2M+</div>
                <div className="text-sm opacity-80 rtl-font">عضو نشط</div>
              </div>
              <div>
                <div className="text-2xl font-bold mb-1">50M</div>
                <div className="text-sm opacity-80 rtl-font">نقطة موزعة</div>
              </div>
              <div>
                <div className="text-2xl font-bold mb-1">15M</div>
                <div className="text-sm opacity-80 rtl-font">درهم موفر</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LoyaltyRewards;