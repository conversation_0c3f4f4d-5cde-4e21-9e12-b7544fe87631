import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';
import 'help_center_screen.dart';

/// Support screen for driver assistance
class SupportScreen extends StatefulWidget {
  const SupportScreen({super.key});

  @override
  State<SupportScreen> createState() => _SupportScreenState();
}

class _SupportScreenState extends State<SupportScreen> {
  final _messageController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Support & Help', 'الدعم والمساعدة'),
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header
              _buildHeader(isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // Quick Actions
              _buildQuickActions(isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // FAQ Section
              _buildFAQSection(isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // Contact Form
              _buildContactForm(isDark, languageProvider),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.support_agent,
            size: 60,
            color: Color(0xFF11B96F),
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('How can we help you?', 'كيف يمكننا مساعدتك؟'),
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'We\'re here to support you 24/7. Choose the best way to reach us.',
              'نحن هنا لدعمك على مدار الساعة. اختر أفضل طريقة للتواصل معنا.'
            ),
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.bodyMedium,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(bool isDark, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageProvider.getText('Quick Actions', 'إجراءات سريعة'),
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.titleMedium,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                icon: Icons.phone,
                title: languageProvider.getText('Call Support', 'اتصل بالدعم'),
                subtitle: languageProvider.getText('24/7 Available', 'متاح 24/7'),
                color: const Color(0xFF11B96F),
                onTap: () => _handleCallSupport(),
                isDark: isDark,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                icon: Icons.chat,
                title: languageProvider.getText('Live Chat', 'محادثة مباشرة'),
                subtitle: languageProvider.getText('Instant Help', 'مساعدة فورية'),
                color: Colors.blue,
                onTap: () => _handleLiveChat(),
                isDark: isDark,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                icon: Icons.email,
                title: languageProvider.getText('Email Support', 'دعم البريد'),
                subtitle: languageProvider.getText('Detailed Help', 'مساعدة مفصلة'),
                color: Colors.orange,
                onTap: () => _handleEmailSupport(),
                isDark: isDark,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                icon: Icons.help_center,
                title: languageProvider.getText('Help Center', 'مركز المساعدة'),
                subtitle: languageProvider.getText('Browse Articles', 'تصفح المقالات'),
                color: Colors.teal,
                onTap: () => _handleHelpCenter(),
                isDark: isDark,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                icon: Icons.video_call,
                title: languageProvider.getText('Video Call', 'مكالمة فيديو'),
                subtitle: languageProvider.getText('Face to Face', 'وجهاً لوجه'),
                color: Colors.purple,
                onTap: () => _handleVideoCall(),
                isDark: isDark,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Container(), // Empty space for symmetry
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: DriverTypography.getContextualStyle(
                context,
                fontSize: DriverTypography.bodyMedium,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: DriverTypography.getContextualStyle(
                context,
                fontSize: DriverTypography.bodySmall,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQSection(bool isDark, LanguageProvider languageProvider) {
    final faqs = [
      {
        'question': languageProvider.getText('How do I update my profile?', 'كيف أحدث ملفي الشخصي؟'),
        'answer': languageProvider.getText(
          'Go to Settings > Account > Edit Profile to update your information.',
          'اذهب إلى الإعدادات > الحساب > تعديل الملف الشخصي لتحديث معلوماتك.'
        ),
      },
      {
        'question': languageProvider.getText('How do I change my password?', 'كيف أغير كلمة المرور؟'),
        'answer': languageProvider.getText(
          'Go to Settings > Security > Change Password to update your password.',
          'اذهب إلى الإعدادات > الأمان > تغيير كلمة المرور لتحديث كلمة المرور.'
        ),
      },
      {
        'question': languageProvider.getText('How do I upload documents?', 'كيف أرفع الوثائق؟'),
        'answer': languageProvider.getText(
          'Go to Settings > Account > Manage Documents to upload required documents.',
          'اذهب إلى الإعدادات > الحساب > إدارة الوثائق لرفع الوثائق المطلوبة.'
        ),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageProvider.getText('Frequently Asked Questions', 'الأسئلة الشائعة'),
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.titleMedium,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: faqs.length,
          separatorBuilder: (context, index) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final faq = faqs[index];
            return _buildFAQItem(
              question: faq['question']!,
              answer: faq['answer']!,
              isDark: isDark,
            );
          },
        ),
      ],
    );
  }

  Widget _buildFAQItem({
    required String question,
    required String answer,
    required bool isDark,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        title: Text(
          question,
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyMedium,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(
              answer,
              style: DriverTypography.getContextualStyle(
                context,
                fontSize: DriverTypography.bodyMedium,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactForm(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              languageProvider.getText('Send us a message', 'أرسل لنا رسالة'),
              style: DriverTypography.getContextualStyle(
                context,
                fontSize: DriverTypography.titleMedium,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF11B96F),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _messageController,
              maxLines: 5,
              style: DriverTypography.getContextualStyle(
                context,
                fontSize: DriverTypography.bodyMedium,
                color: isDark ? Colors.white : Colors.black87,
              ),
              decoration: InputDecoration(
                labelText: languageProvider.getText('Your message', 'رسالتك'),
                hintText: languageProvider.getText(
                  'Describe your issue or question...',
                  'اوصف مشكلتك أو سؤالك...'
                ),
                prefixIcon: const Icon(Icons.message, color: Color(0xFF11B96F)),
                labelStyle: TextStyle(color: isDark ? Colors.grey[400] : Colors.grey[600]),
                hintStyle: TextStyle(color: isDark ? Colors.grey[500] : Colors.grey[500]),
                filled: true,
                fillColor: isDark ? const Color(0xFF0F231A) : Colors.grey[50],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return languageProvider.getText(
                    'Please enter your message',
                    'يرجى إدخال رسالتك'
                  );
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton.icon(
                onPressed: _isSubmitting ? null : _handleSubmitMessage,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF11B96F),
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                ),
                icon: _isSubmitting
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.send),
                label: Text(
                  languageProvider.getText('Send Message', 'إرسال الرسالة'),
                  style: DriverTypography.getContextualStyle(
                    context,
                    fontSize: DriverTypography.bodyLarge,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Action handlers
  void _handleCallSupport() async {
    const phoneNumber = 'tel:+966123456789';
    try {
      if (await canLaunchUrl(Uri.parse(phoneNumber))) {
        await launchUrl(Uri.parse(phoneNumber));
      } else {
        _showSnackBar(context.read<LanguageProvider>().getText(
          'Unable to make phone call',
          'غير قادر على إجراء مكالمة هاتفية'
        ));
      }
    } catch (e) {
      _showSnackBar(context.read<LanguageProvider>().getText(
        'Call Support - Coming Soon',
        'الاتصال بالدعم - قريباً'
      ));
    }
  }

  void _handleLiveChat() {
    _showSnackBar(context.read<LanguageProvider>().getText(
      'Live Chat - Coming Soon',
      'المحادثة المباشرة - قريباً'
    ));
  }

  void _handleHelpCenter() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const HelpCenterScreen(),
      ),
    );
  }

  void _handleEmailSupport() async {
    const email = 'mailto:<EMAIL>?subject=Driver Support Request';
    try {
      if (await canLaunchUrl(Uri.parse(email))) {
        await launchUrl(Uri.parse(email));
      } else {
        _showSnackBar(context.read<LanguageProvider>().getText(
          'Unable to open email app',
          'غير قادر على فتح تطبيق البريد'
        ));
      }
    } catch (e) {
      _showSnackBar(context.read<LanguageProvider>().getText(
        'Email Support - Coming Soon',
        'دعم البريد الإلكتروني - قريباً'
      ));
    }
  }

  void _handleVideoCall() {
    _showSnackBar(context.read<LanguageProvider>().getText(
      'Video Call - Coming Soon',
      'مكالمة الفيديو - قريباً'
    ));
  }

  void _handleSubmitMessage() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isSubmitting = true);

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        setState(() => _isSubmitting = false);
        _messageController.clear();

        final languageProvider = context.read<LanguageProvider>();
        _showSnackBar(languageProvider.getText(
          'Message sent successfully! We\'ll get back to you soon.',
          'تم إرسال الرسالة بنجاح! سنعود إليك قريباً.'
        ));
      }
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF11B96F),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
