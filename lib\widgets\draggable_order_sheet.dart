import 'package:flutter/material.dart';
import '../models/delivery_order.dart';
import '../models/order_status.dart';

/// Draggable bottom sheet for order tracking
/// Similar to the design in the provided image
class DraggableOrderSheet extends StatefulWidget {
  final DeliveryOrder order;
  final VoidCallback onActionPressed;
  final VoidCallback? onCancelPressed;

  const DraggableOrderSheet({
    super.key,
    required this.order,
    required this.onActionPressed,
    this.onCancelPressed,
  });

  @override
  State<DraggableOrderSheet> createState() => _DraggableOrderSheetState();
}

class _DraggableOrderSheetState extends State<DraggableOrderSheet> {
  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return DraggableScrollableSheet(
      initialChildSize: 0.3, // Start at 30% of screen height
      minChildSize: 0.15,    // Minimum 15% (just the header)
      maxChildSize: 0.8,     // Maximum 80% of screen height
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: isDark ? const Color(0xFF1B1B1B) : Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Drag handle
              _buildDragHandle(),
              
              // Content
              Expanded(
                child: ListView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  children: [
                    // Status header (always visible)
                    _buildStatusHeader(isDark),
                    
                    const SizedBox(height: 16),
                    
                    // Customer info
                    _buildCustomerInfo(isDark),
                    
                    const SizedBox(height: 20),
                    
                    // Delivery details
                    _buildDeliveryDetails(isDark),
                    
                    const SizedBox(height: 20),
                    
                    // Order items
                    _buildOrderItems(isDark),
                    
                    const SizedBox(height: 20),
                    
                    // Action buttons
                    _buildActionButtons(isDark),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDragHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildStatusHeader(bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.order.status.color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.order.status == OrderStatus.goingToCustomer
                  ? 'Your food will be arrived in ${widget.order.estimatedTime}'
                  : widget.order.status.description,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInfo(bool isDark) {
    return Row(
      children: [
        // Customer avatar
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey[300],
          ),
          child: ClipOval(
            child: Image.asset(
              'assets/images/customer_avatar.png',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.person,
                  color: Colors.grey[600],
                  size: 30,
                );
              },
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Customer details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.order.customerName,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              
              const SizedBox(height: 4),
              
              Text(
                widget.order.restaurantName,
                style: TextStyle(
                  fontSize: 14,
                  color: isDark ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
                ),
              ),
            ],
          ),
        ),
        
        // Action buttons
        Row(
          children: [
            // Message button
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.message,
                color: Colors.white,
                size: 20,
              ),
            ),
            
            const SizedBox(width: 8),
            
            // Call button
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.phone,
                color: Colors.white,
                size: 20,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDeliveryDetails(bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery details',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Pickup location
        _buildLocationItem(
          icon: Icons.restaurant,
          title: 'Pickup location',
          address: widget.order.restaurantAddress,
          isDark: isDark,
        ),
        
        const SizedBox(height: 12),
        
        // Delivery location
        _buildLocationItem(
          icon: Icons.location_on,
          title: 'Delivery location',
          address: widget.order.customerAddress,
          isDark: isDark,
        ),
      ],
    );
  }

  Widget _buildLocationItem({
    required IconData icon,
    required String title,
    required String address,
    required bool isDark,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: const Color(0xFF11B96F).withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: const Color(0xFF11B96F),
            size: 18,
          ),
        ),
        
        const SizedBox(width: 12),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              
              const SizedBox(height: 4),
              
              Text(
                address,
                style: TextStyle(
                  fontSize: 13,
                  color: isDark ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOrderItems(bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order items',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        
        const SizedBox(height: 12),
        
        ...widget.order.items.map((item) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Text(
                '${item.quantity}x',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              
              const SizedBox(width: 8),
              
              Expanded(
                child: Text(
                  item.name,
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
                  ),
                ),
              ),
              
              Text(
                item.formattedTotalPrice,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        )),
        
        const Divider(),
        
        Row(
          children: [
            const Expanded(
              child: Text(
                'Total',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            
            Text(
              widget.order.formattedTotal,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF11B96F),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons(bool isDark) {
    return Column(
      children: [
        // Main action button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: widget.order.status == OrderStatus.delivered ? null : widget.onActionPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.order.status.color,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              widget.order.status.actionButtonText,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        
        // Cancel button (if allowed)
        if (widget.order.status.canCancel && widget.onCancelPressed != null) ...[
          const SizedBox(height: 12),
          
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: widget.onCancelPressed,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: BorderSide(
                  color: isDark ? const Color(0xFF757575) : const Color(0xFFBDBDBD),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Cancel Order',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isDark ? const Color(0xFF757575) : const Color(0xFF424242),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
