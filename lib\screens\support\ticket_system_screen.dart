import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Ticket System screen for tracking support requests
class TicketSystemScreen extends StatefulWidget {
  const TicketSystemScreen({super.key});

  @override
  State<TicketSystemScreen> createState() => _TicketSystemScreenState();
}

class _TicketSystemScreenState extends State<TicketSystemScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<SupportTicket> _tickets = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSampleTickets();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadSampleTickets() {
    final languageProvider = context.read<LanguageProvider>();
    
    _tickets.addAll([
      SupportTicket(
        id: 'TK001',
        title: languageProvider.getText('Payment not received', 'لم أستلم الدفعة'),
        description: languageProvider.getText(
          'I completed 5 trips yesterday but haven\'t received payment yet.',
          'أكملت 5 رحلات أمس لكن لم أستلم الدفعة بعد.'
        ),
        status: TicketStatus.open,
        priority: TicketPriority.high,
        category: 'Payment',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        lastUpdated: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
      SupportTicket(
        id: 'TK002',
        title: languageProvider.getText('App crashes during trip', 'التطبيق يتعطل أثناء الرحلة'),
        description: languageProvider.getText(
          'The app keeps crashing when I try to start a trip. This is affecting my work.',
          'التطبيق يستمر في التعطل عندما أحاول بدء رحلة. هذا يؤثر على عملي.'
        ),
        status: TicketStatus.inProgress,
        priority: TicketPriority.urgent,
        category: 'Technical',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        lastUpdated: DateTime.now().subtract(const Duration(hours: 4)),
        agentName: languageProvider.getText('Ahmed', 'أحمد'),
      ),
      SupportTicket(
        id: 'TK003',
        title: languageProvider.getText('Document verification', 'التحقق من الوثائق'),
        description: languageProvider.getText(
          'My driving license was rejected. Can you please review it again?',
          'تم رفض رخصة القيادة الخاصة بي. هل يمكنك مراجعتها مرة أخرى؟'
        ),
        status: TicketStatus.resolved,
        priority: TicketPriority.medium,
        category: 'Documents',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
        agentName: languageProvider.getText('Sarah', 'سارة'),
        resolution: languageProvider.getText(
          'Document has been approved. You can now start accepting trips.',
          'تم الموافقة على الوثيقة. يمكنك الآن البدء في قبول الرحلات.'
        ),
      ),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('My Tickets', 'تذاكري'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.add, color: Color(0xFF11B96F)),
              onPressed: () => _createNewTicket(),
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            labelColor: const Color(0xFF11B96F),
            unselectedLabelColor: isDark ? Colors.grey[400] : Colors.grey[600],
            indicatorColor: const Color(0xFF11B96F),
            tabs: [
              Tab(text: languageProvider.getText('Open', 'مفتوحة')),
              Tab(text: languageProvider.getText('In Progress', 'قيد المعالجة')),
              Tab(text: languageProvider.getText('Resolved', 'محلولة')),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildTicketList(TicketStatus.open, isDark, languageProvider),
            _buildTicketList(TicketStatus.inProgress, isDark, languageProvider),
            _buildTicketList(TicketStatus.resolved, isDark, languageProvider),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _createNewTicket(),
          backgroundColor: const Color(0xFF11B96F),
          child: const Icon(Icons.add, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildTicketList(TicketStatus status, bool isDark, LanguageProvider languageProvider) {
    final filteredTickets = _tickets.where((ticket) => ticket.status == status).toList();

    if (filteredTickets.isEmpty) {
      return _buildEmptyState(status, isDark, languageProvider);
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: filteredTickets.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        return _buildTicketCard(filteredTickets[index], isDark, languageProvider);
      },
    );
  }

  Widget _buildEmptyState(TicketStatus status, bool isDark, LanguageProvider languageProvider) {
    String title, subtitle;
    IconData icon;

    switch (status) {
      case TicketStatus.open:
        title = languageProvider.getText('No open tickets', 'لا توجد تذاكر مفتوحة');
        subtitle = languageProvider.getText('Create a new ticket if you need help', 'أنشئ تذكرة جديدة إذا كنت تحتاج مساعدة');
        icon = Icons.inbox_outlined;
        break;
      case TicketStatus.inProgress:
        title = languageProvider.getText('No tickets in progress', 'لا توجد تذاكر قيد المعالجة');
        subtitle = languageProvider.getText('Your tickets will appear here when being processed', 'ستظهر تذاكرك هنا عند معالجتها');
        icon = Icons.hourglass_empty;
        break;
      case TicketStatus.resolved:
        title = languageProvider.getText('No resolved tickets', 'لا توجد تذاكر محلولة');
        subtitle = languageProvider.getText('Completed tickets will appear here', 'ستظهر التذاكر المكتملة هنا');
        icon = Icons.check_circle_outline;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: isDark ? Colors.grey[600] : Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: isDark ? Colors.grey[500] : Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (status == TicketStatus.open) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _createNewTicket(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF11B96F),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              icon: const Icon(Icons.add),
              label: Text(languageProvider.getText('Create Ticket', 'إنشاء تذكرة')),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTicketCard(SupportTicket ticket, bool isDark, LanguageProvider languageProvider) {
    return GestureDetector(
      onTap: () => _showTicketDetails(ticket),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getPriorityColor(ticket.priority).withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(ticket.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    ticket.id,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: _getStatusColor(ticket.status),
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getPriorityColor(ticket.priority).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getPriorityIcon(ticket.priority),
                        size: 12,
                        color: _getPriorityColor(ticket.priority),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getPriorityText(ticket.priority, languageProvider),
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: _getPriorityColor(ticket.priority),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Title
            Text(
              ticket.title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),

            const SizedBox(height: 8),

            // Description
            Text(
              ticket.description,
              style: TextStyle(
                fontSize: 14,
                color: isDark ? Colors.grey[300] : Colors.grey[700],
                height: 1.4,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 12),

            // Footer
            Row(
              children: [
                Icon(
                  Icons.category_outlined,
                  size: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  ticket.category,
                  style: TextStyle(
                    fontSize: 12,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDateTime(ticket.lastUpdated),
                  style: TextStyle(
                    fontSize: 12,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                if (ticket.agentName != null) ...[
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      ticket.agentName!,
                      style: const TextStyle(
                        fontSize: 11,
                        color: Color(0xFF11B96F),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(TicketStatus status) {
    switch (status) {
      case TicketStatus.open:
        return Colors.blue;
      case TicketStatus.inProgress:
        return Colors.orange;
      case TicketStatus.resolved:
        return const Color(0xFF11B96F);
    }
  }

  Color _getPriorityColor(TicketPriority priority) {
    switch (priority) {
      case TicketPriority.low:
        return const Color(0xFF11B96F);
      case TicketPriority.medium:
        return Colors.orange;
      case TicketPriority.high:
        return Colors.red;
      case TicketPriority.urgent:
        return Colors.purple;
    }
  }

  IconData _getPriorityIcon(TicketPriority priority) {
    switch (priority) {
      case TicketPriority.low:
        return Icons.keyboard_arrow_down;
      case TicketPriority.medium:
        return Icons.remove;
      case TicketPriority.high:
        return Icons.keyboard_arrow_up;
      case TicketPriority.urgent:
        return Icons.priority_high;
    }
  }

  String _getPriorityText(TicketPriority priority, LanguageProvider languageProvider) {
    switch (priority) {
      case TicketPriority.low:
        return languageProvider.getText('Low', 'منخفضة');
      case TicketPriority.medium:
        return languageProvider.getText('Medium', 'متوسطة');
      case TicketPriority.high:
        return languageProvider.getText('High', 'عالية');
      case TicketPriority.urgent:
        return languageProvider.getText('Urgent', 'عاجلة');
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _showTicketDetails(SupportTicket ticket) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Ticket Details: ${ticket.title}'),
        backgroundColor: const Color(0xFF11B96F),
      ),
    );
  }

  void _createNewTicket() {
    final languageProvider = context.read<LanguageProvider>();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.getText('Create new ticket feature coming soon', 'ميزة إنشاء تذكرة جديدة قريباً')
        ),
        backgroundColor: const Color(0xFF11B96F),
      ),
    );
  }
}

/// Support ticket model
class SupportTicket {
  final String id;
  final String title;
  final String description;
  final TicketStatus status;
  final TicketPriority priority;
  final String category;
  final DateTime createdAt;
  final DateTime lastUpdated;
  final String? agentName;
  final String? resolution;

  SupportTicket({
    required this.id,
    required this.title,
    required this.description,
    required this.status,
    required this.priority,
    required this.category,
    required this.createdAt,
    required this.lastUpdated,
    this.agentName,
    this.resolution,
  });
}

enum TicketStatus { open, inProgress, resolved }
enum TicketPriority { low, medium, high, urgent }
