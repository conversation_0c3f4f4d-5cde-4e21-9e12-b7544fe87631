# Drivers Module

This module contains the driver management functionality for the Wssalti POS system.

## Features

### 1. Drivers Dashboard
- **Performance metrics** with circular and bar chart visualizations
- **Profile card** with status indicators and ratings
- **Performance statistics** with interactive charts
- **Stats summary** showing completed orders, deliveries, cancellations
- **Earnings overview** with today's earnings
- **Trip summary** with total trips, distance, and time
- **Recent feedback** from customers

### 2. Drivers Orders
- **Ongoing orders** with detailed information including:
  - Customer details and delivery address
  - Order menu items with images and prices
  - Restaurant information with ratings
  - Payment method and total amount
- **Order history** with comprehensive table showing:
  - Customer information with avatars
  - Order totals and delivery addresses
  - Order status with color-coded badges
  - Action buttons for each order

### 3. Drivers Feedback
- **Feedback charts** showing positive and negative feedback trends
- **Recent feedback list** with customer reviews and ratings
- **Filter options** for contact and low rating feedback
- **Grid layout** displaying feedback cards with:
  - Customer information and avatars
  - Review comments and ratings
  - Food type and ratings

## File Structure

```
lib/features/drivers/
├── presentation/
│   ├── screens/
│   │   ├── drivers_dashboard_screen.dart
│   │   ├── drivers_orders_screen.dart
│   │   └── drivers_feedback_screen.dart
│   └── widgets/
│       ├── performance_card_widget.dart
│       ├── profile_card_widget.dart
│       ├── performance_chart_widget.dart
│       ├── stats_summary_widget.dart
│       ├── recent_feedback_widget.dart
│       ├── ongoing_order_widget.dart
│       ├── order_history_widget.dart
│       ├── feedback_chart_widget.dart
│       └── feedback_list_widget.dart
└── README.md
```

## Navigation

The drivers module is accessible through the sidebar navigation:
- **Drivers** → **Dashboard** (`/drivers-dashboard`)
- **Drivers** → **Orders** (`/drivers-orders`)
- **Drivers** → **Feedback** (`/drivers-feedback`)

## Design Features

- **Consistent theming** using AppColors
- **Responsive layout** with proper spacing and padding
- **Interactive elements** with hover states and animations
- **Data visualization** with custom painted charts
- **Status indicators** with color-coded badges
- **Search functionality** in top navigation bar
- **User profile** display in header

## Usage

1. Navigate to the Drivers section from the sidebar
2. Select the desired subsection (Dashboard, Orders, or Feedback)
3. View real-time data and statistics
4. Interact with orders and feedback as needed

## Dependencies

- `flutter/material.dart` - Material Design components
- `google_fonts/google_fonts.dart` - Typography
- Custom theme colors from `core/theme/app_colors.dart`
- Navigation routing from `core/routes/app_routes.dart`

## Future Enhancements

- Real-time data integration
- Push notifications for new orders
- Driver location tracking
- Performance analytics
- Earnings reports
- Customer communication features
