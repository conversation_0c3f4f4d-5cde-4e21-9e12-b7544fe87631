import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/driver_themes.dart';
import '../providers/auth_provider.dart';
import '../screens/settings/settings_screen_en.dart';

/// English Settings screen demo
void main() {
  runApp(const SettingsDemoENApp());
}

class SettingsDemoENApp extends StatelessWidget {
  const SettingsDemoENApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'Settings Demo (English)',
        debugShowCheckedModeBanner: false,
        theme: DriverThemes.lightTheme,
        darkTheme: DriverThemes.darkTheme,
        themeMode: ThemeMode.system,
        home: const SettingsDemoENScreen(),
      ),
    );
  }
}

class SettingsDemoENScreen extends StatefulWidget {
  const SettingsDemoENScreen({super.key});

  @override
  State<SettingsDemoENScreen> createState() => _SettingsDemoENScreenState();
}

class _SettingsDemoENScreenState extends State<SettingsDemoENScreen> {
  ThemeMode _themeMode = ThemeMode.system;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Settings Demo (English)',
      debugShowCheckedModeBanner: false,
      theme: DriverThemes.lightTheme,
      darkTheme: DriverThemes.darkTheme,
      themeMode: _themeMode,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Settings Demo (English)'),
          backgroundColor: const Color(0xFF11B96F),
          foregroundColor: Colors.white,
          actions: [
            PopupMenuButton<ThemeMode>(
              icon: Icon(_getThemeIcon()),
              onSelected: (ThemeMode mode) {
                setState(() {
                  _themeMode = mode;
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: ThemeMode.light,
                  child: Row(
                    children: [
                      Icon(Icons.light_mode),
                      SizedBox(width: 8),
                      Text('Light Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: ThemeMode.dark,
                  child: Row(
                    children: [
                      Icon(Icons.dark_mode),
                      SizedBox(width: 8),
                      Text('Dark Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: ThemeMode.system,
                  child: Row(
                    children: [
                      Icon(Icons.settings),
                      SizedBox(width: 8),
                      Text('System'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
          ],
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.settings,
                  size: 80,
                  color: Color(0xFF11B96F),
                ),
                SizedBox(height: 20),
                Text(
                  'English Settings Screen Demo',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'Test both Light and Dark modes',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 40),
                _DemoButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getThemeIcon() {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.settings;
    }
  }
}

class _DemoButton extends StatelessWidget {
  const _DemoButton();

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const SettingsScreenEN(),
          ),
        );
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF11B96F),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      icon: const Icon(Icons.settings),
      label: const Text(
        'Open English Settings Screen',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

/// Function to run the English settings demo
void runSettingsDemoEN() {
  runApp(const SettingsDemoENApp());
}

/// Usage example:
/// 
/// ```dart
/// // In main.dart or any other file
/// import 'demo/settings_demo_en.dart';
/// 
/// void main() {
///   runSettingsDemoEN();
/// }
/// ```
