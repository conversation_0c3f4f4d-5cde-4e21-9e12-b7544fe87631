
-- =====================================================
-- WASSLTI COMPLETE DATABASE SCHEMA
-- نظام قاعدة بيانات وصلتي الشامل
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- =====================================================
-- ENUMS (التعدادات)
-- =====================================================

-- User roles
CREATE TYPE user_role AS ENUM ('customer', 'driver', 'restaurant', 'admin', 'support');

-- Order status
CREATE TYPE order_status AS ENUM (
    'pending', 'confirmed', 'preparing', 'ready', 'picked_up',
    'on_the_way', 'delivered', 'cancelled', 'refunded'
);

-- Driver status
CREATE TYPE driver_status AS ENUM ('offline', 'available', 'busy', 'on_break');

-- Payment status
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded', 'cancelled');

-- Payment method
CREATE TYPE payment_method AS ENUM ('cash', 'card', 'wallet', 'bank_transfer');

-- Notification type
CREATE TYPE notification_type AS ENUM ('order', 'promotion', 'system', 'payment', 'delivery');

-- Vehicle type
CREATE TYPE vehicle_type AS ENUM ('motorcycle', 'bicycle', 'car', 'scooter');

-- =====================================================
-- CORE TABLES (الجداول الأساسية)
-- =====================================================

-- Table: users (المستخدمين - العملاء والإداريين)
CREATE TABLE users (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    full_name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT UNIQUE NOT NULL,
    password_hash TEXT,
    profile_image TEXT,
    role user_role DEFAULT 'customer',
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMP,
    phone_verified_at TIMESTAMP,
    last_login_at TIMESTAMP,
    preferences JSONB DEFAULT '{}',
    address TEXT,
    location GEOGRAPHY(POINT, 4326),
    date_of_birth DATE,
    gender TEXT,
    language TEXT DEFAULT 'ar',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: drivers (السائقين)
CREATE TABLE drivers (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT UNIQUE NOT NULL,
    password_hash TEXT,
    profile_image TEXT,
    vehicle_type vehicle_type,
    vehicle_model TEXT,
    vehicle_plate TEXT,
    vehicle_color TEXT,
    license_number TEXT,
    documents JSONB DEFAULT '{}',
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    status driver_status DEFAULT 'offline',
    current_location GEOGRAPHY(POINT, 4326),
    last_location_update TIMESTAMP,
    rating FLOAT DEFAULT 5.0,
    total_ratings INTEGER DEFAULT 0,
    total_deliveries INTEGER DEFAULT 0,
    earnings_total DECIMAL(10, 2) DEFAULT 0,
    earnings_today DECIMAL(10, 2) DEFAULT 0,
    working_hours JSONB DEFAULT '{}',
    emergency_contact JSONB DEFAULT '{}',
    bank_details JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: restaurants (المطاعم)
CREATE TABLE restaurants (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    name_en TEXT,
    email TEXT UNIQUE,
    phone TEXT,
    location GEOGRAPHY(POINT, 4326),
    address TEXT NOT NULL,
    description TEXT,
    cuisine_type TEXT[],
    rating FLOAT DEFAULT 0.0,
    total_ratings INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_open BOOLEAN DEFAULT FALSE,
    logo TEXT,
    cover_image TEXT,
    gallery JSONB DEFAULT '[]',
    opening_hours JSONB DEFAULT '{}',
    delivery_fee DECIMAL(10, 2) DEFAULT 0,
    minimum_order DECIMAL(10, 2) DEFAULT 0,
    estimated_delivery_time INTEGER DEFAULT 30,
    accepts_cash BOOLEAN DEFAULT TRUE,
    accepts_card BOOLEAN DEFAULT TRUE,
    license_number TEXT,
    tax_number TEXT,
    bank_details JSONB DEFAULT '{}',
    social_media JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: categories (فئات الطعام)
CREATE TABLE categories (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    name_en TEXT,
    description TEXT,
    image TEXT,
    icon TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: meals (الوجبات)
CREATE TABLE meals (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE CASCADE,
    category_id uuid REFERENCES categories(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    name_en TEXT,
    description TEXT,
    description_en TEXT,
    image TEXT,
    gallery JSONB DEFAULT '[]',
    price DECIMAL(10, 2) NOT NULL,
    original_price DECIMAL(10, 2),
    discount_percentage INTEGER DEFAULT 0,
    ingredients TEXT[],
    allergens TEXT[],
    nutritional_info JSONB DEFAULT '{}',
    preparation_time INTEGER DEFAULT 15,
    calories INTEGER,
    is_available BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_spicy BOOLEAN DEFAULT FALSE,
    is_vegetarian BOOLEAN DEFAULT FALSE,
    is_vegan BOOLEAN DEFAULT FALSE,
    is_halal BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    tags TEXT[],
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: meal_options (خيارات الوجبات)
CREATE TABLE meal_options (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    meal_id uuid REFERENCES meals(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- 'size', 'addon', 'choice'
    is_required BOOLEAN DEFAULT FALSE,
    max_selections INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: meal_option_values (قيم خيارات الوجبات)
CREATE TABLE meal_option_values (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    option_id uuid REFERENCES meal_options(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    price_modifier DECIMAL(10, 2) DEFAULT 0,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: orders (الطلبات)
CREATE TABLE orders (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number TEXT UNIQUE NOT NULL,
    user_id uuid REFERENCES users(id) ON DELETE SET NULL,
    driver_id uuid REFERENCES drivers(id) ON DELETE SET NULL,
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE SET NULL,
    status order_status DEFAULT 'pending',
    subtotal DECIMAL(10, 2) NOT NULL,
    delivery_fee DECIMAL(10, 2) DEFAULT 0,
    service_fee DECIMAL(10, 2) DEFAULT 0,
    tax_amount DECIMAL(10, 2) DEFAULT 0,
    discount_amount DECIMAL(10, 2) DEFAULT 0,
    total_price DECIMAL(10, 2) NOT NULL,
    delivery_address TEXT NOT NULL,
    delivery_location GEOGRAPHY(POINT, 4326),
    delivery_instructions TEXT,
    customer_phone TEXT,
    customer_name TEXT,
    scheduled_time TIMESTAMP,
    estimated_delivery_time TIMESTAMP,
    actual_delivery_time TIMESTAMP,
    preparation_time INTEGER,
    payment_method payment_method,
    payment_status payment_status DEFAULT 'pending',
    promo_code TEXT,
    notes TEXT,
    rating INTEGER,
    review TEXT,
    is_scheduled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: order_items (عناصر الطلب)
CREATE TABLE order_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id uuid REFERENCES orders(id) ON DELETE CASCADE,
    meal_id uuid REFERENCES meals(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    special_instructions TEXT,
    selected_options JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: order_tracking (تتبع الطلبات)
CREATE TABLE order_tracking (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id uuid REFERENCES orders(id) ON DELETE CASCADE,
    status order_status NOT NULL,
    location GEOGRAPHY(POINT, 4326),
    notes TEXT,
    created_by uuid,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: payments (المدفوعات)
CREATE TABLE payments (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id uuid REFERENCES orders(id) ON DELETE CASCADE,
    user_id uuid REFERENCES users(id) ON DELETE SET NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency TEXT DEFAULT 'MAD',
    payment_method payment_method NOT NULL,
    status payment_status DEFAULT 'pending',
    payment_reference TEXT,
    gateway_response JSONB DEFAULT '{}',
    transaction_id TEXT,
    gateway_fee DECIMAL(10, 2) DEFAULT 0,
    net_amount DECIMAL(10, 2),
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- ADDITIONAL TABLES (الجداول الإضافية)
-- =====================================================

-- Table: wallets (المحافظ الإلكترونية)
CREATE TABLE wallets (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    driver_id uuid REFERENCES drivers(id) ON DELETE CASCADE,
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE CASCADE,
    balance DECIMAL(10, 2) DEFAULT 0,
    pending_balance DECIMAL(10, 2) DEFAULT 0,
    total_earned DECIMAL(10, 2) DEFAULT 0,
    total_spent DECIMAL(10, 2) DEFAULT 0,
    currency TEXT DEFAULT 'MAD',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    CONSTRAINT wallet_owner_check CHECK (
        (user_id IS NOT NULL AND driver_id IS NULL AND restaurant_id IS NULL) OR
        (user_id IS NULL AND driver_id IS NOT NULL AND restaurant_id IS NULL) OR
        (user_id IS NULL AND driver_id IS NULL AND restaurant_id IS NOT NULL)
    )
);

-- Table: wallet_transactions (معاملات المحفظة)
CREATE TABLE wallet_transactions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id uuid REFERENCES wallets(id) ON DELETE CASCADE,
    order_id uuid REFERENCES orders(id) ON DELETE SET NULL,
    type TEXT NOT NULL, -- 'credit', 'debit', 'refund', 'commission', 'withdrawal'
    amount DECIMAL(10, 2) NOT NULL,
    balance_before DECIMAL(10, 2) NOT NULL,
    balance_after DECIMAL(10, 2) NOT NULL,
    description TEXT,
    reference_id TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: promotions (العروض والخصومات)
CREATE TABLE promotions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL, -- 'percentage', 'fixed', 'free_delivery'
    value DECIMAL(10, 2) NOT NULL,
    minimum_order DECIMAL(10, 2) DEFAULT 0,
    maximum_discount DECIMAL(10, 2),
    usage_limit INTEGER,
    usage_count INTEGER DEFAULT 0,
    user_usage_limit INTEGER DEFAULT 1,
    target_audience TEXT DEFAULT 'all', -- 'all', 'new_users', 'existing_users'
    applicable_restaurants uuid[],
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by uuid REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: promotion_usage (استخدام العروض)
CREATE TABLE promotion_usage (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    promotion_id uuid REFERENCES promotions(id) ON DELETE CASCADE,
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    order_id uuid REFERENCES orders(id) ON DELETE CASCADE,
    discount_amount DECIMAL(10, 2) NOT NULL,
    used_at TIMESTAMP DEFAULT NOW()
);

-- Table: notifications (الإشعارات)
CREATE TABLE notifications (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    driver_id uuid REFERENCES drivers(id) ON DELETE CASCADE,
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type notification_type NOT NULL,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    CONSTRAINT notification_recipient_check CHECK (
        (user_id IS NOT NULL AND driver_id IS NULL AND restaurant_id IS NULL) OR
        (user_id IS NULL AND driver_id IS NOT NULL AND restaurant_id IS NULL) OR
        (user_id IS NULL AND driver_id IS NULL AND restaurant_id IS NOT NULL)
    )
);

-- Table: reviews (التقييمات والمراجعات)
CREATE TABLE reviews (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id uuid REFERENCES orders(id) ON DELETE CASCADE,
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE CASCADE,
    driver_id uuid REFERENCES drivers(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    food_rating INTEGER CHECK (food_rating >= 1 AND food_rating <= 5),
    delivery_rating INTEGER CHECK (delivery_rating >= 1 AND delivery_rating <= 5),
    service_rating INTEGER CHECK (service_rating >= 1 AND service_rating <= 5),
    images TEXT[],
    is_anonymous BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: delivery_zones (مناطق التوصيل)
CREATE TABLE delivery_zones (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    polygon GEOGRAPHY(POLYGON, 4326) NOT NULL,
    delivery_fee DECIMAL(10, 2) NOT NULL,
    minimum_order DECIMAL(10, 2) DEFAULT 0,
    estimated_time INTEGER DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: support_tickets (تذاكر الدعم الفني)
CREATE TABLE support_tickets (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_number TEXT UNIQUE NOT NULL,
    user_id uuid REFERENCES users(id) ON DELETE SET NULL,
    driver_id uuid REFERENCES drivers(id) ON DELETE SET NULL,
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE SET NULL,
    order_id uuid REFERENCES orders(id) ON DELETE SET NULL,
    subject TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    priority TEXT DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    status TEXT DEFAULT 'open', -- 'open', 'in_progress', 'resolved', 'closed'
    assigned_to uuid REFERENCES users(id),
    attachments JSONB DEFAULT '[]',
    resolution TEXT,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: support_messages (رسائل الدعم الفني)
CREATE TABLE support_messages (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id uuid REFERENCES support_tickets(id) ON DELETE CASCADE,
    sender_id uuid REFERENCES users(id) ON DELETE SET NULL,
    message TEXT NOT NULL,
    attachments JSONB DEFAULT '[]',
    is_internal BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: admin_users (المستخدمين الإداريين)
CREATE TABLE admin_users (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    full_name TEXT NOT NULL,
    role TEXT NOT NULL, -- 'super_admin', 'admin', 'manager', 'support'
    permissions JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP,
    login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_by uuid REFERENCES admin_users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: audit_logs (سجل العمليات)
CREATE TABLE audit_logs (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid,
    admin_id uuid REFERENCES admin_users(id),
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id TEXT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: system_settings (إعدادات النظام)
CREATE TABLE system_settings (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    key TEXT UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    category TEXT DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE,
    updated_by uuid REFERENCES admin_users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: content_management (إدارة المحتوى)
CREATE TABLE content_management (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    type TEXT NOT NULL, -- 'banner', 'promotion', 'blog', 'notification', 'page'
    status TEXT DEFAULT 'draft', -- 'draft', 'published', 'archived'
    target_audience TEXT DEFAULT 'all',
    publish_date TIMESTAMP,
    expire_date TIMESTAMP,
    image_url TEXT,
    metadata JSONB DEFAULT '{}',
    created_by uuid REFERENCES admin_users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: analytics_events (أحداث التحليلات)
CREATE TABLE analytics_events (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    event_name TEXT NOT NULL,
    user_id uuid,
    session_id TEXT,
    properties JSONB DEFAULT '{}',
    timestamp TIMESTAMP DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    platform TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: inventory_items (عناصر المخزون)
CREATE TABLE inventory_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    category TEXT,
    unit TEXT NOT NULL,
    current_stock DECIMAL(10, 3) DEFAULT 0,
    minimum_stock DECIMAL(10, 3) DEFAULT 0,
    maximum_stock DECIMAL(10, 3),
    cost_per_unit DECIMAL(10, 2),
    supplier_info JSONB DEFAULT '{}',
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Table: inventory_transactions (معاملات المخزون)
CREATE TABLE inventory_transactions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    item_id uuid REFERENCES inventory_items(id) ON DELETE CASCADE,
    type TEXT NOT NULL, -- 'in', 'out', 'adjustment', 'expired'
    quantity DECIMAL(10, 3) NOT NULL,
    unit_cost DECIMAL(10, 2),
    total_cost DECIMAL(10, 2),
    reference_id TEXT,
    notes TEXT,
    created_by uuid,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: delivery_routes (مسارات التوصيل)
CREATE TABLE delivery_routes (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    driver_id uuid REFERENCES drivers(id) ON DELETE CASCADE,
    start_location GEOGRAPHY(POINT, 4326),
    end_location GEOGRAPHY(POINT, 4326),
    waypoints JSONB DEFAULT '[]',
    total_distance DECIMAL(10, 2),
    estimated_duration INTEGER,
    actual_duration INTEGER,
    orders uuid[],
    status TEXT DEFAULT 'planned', -- 'planned', 'active', 'completed', 'cancelled'
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: financial_reports (التقارير المالية)
CREATE TABLE financial_reports (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    report_type TEXT NOT NULL, -- 'daily', 'weekly', 'monthly', 'yearly'
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_revenue DECIMAL(12, 2) DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    commission_earned DECIMAL(12, 2) DEFAULT 0,
    delivery_fees DECIMAL(12, 2) DEFAULT 0,
    refunds_issued DECIMAL(12, 2) DEFAULT 0,
    driver_earnings DECIMAL(12, 2) DEFAULT 0,
    restaurant_earnings DECIMAL(12, 2) DEFAULT 0,
    platform_earnings DECIMAL(12, 2) DEFAULT 0,
    data JSONB DEFAULT '{}',
    generated_by uuid REFERENCES admin_users(id),
    generated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- INDEXES (الفهارس)
-- =====================================================

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_location ON users USING GIST(location);

-- Drivers indexes
CREATE INDEX idx_drivers_status ON drivers(status);
CREATE INDEX idx_drivers_location ON drivers USING GIST(current_location);
CREATE INDEX idx_drivers_rating ON drivers(rating);
CREATE INDEX idx_drivers_phone ON drivers(phone);

-- Restaurants indexes
CREATE INDEX idx_restaurants_location ON restaurants USING GIST(location);
CREATE INDEX idx_restaurants_rating ON restaurants(rating);
CREATE INDEX idx_restaurants_is_active ON restaurants(is_active);
CREATE INDEX idx_restaurants_cuisine_type ON restaurants USING GIN(cuisine_type);

-- Meals indexes
CREATE INDEX idx_meals_restaurant_id ON meals(restaurant_id);
CREATE INDEX idx_meals_category_id ON meals(category_id);
CREATE INDEX idx_meals_is_available ON meals(is_available);
CREATE INDEX idx_meals_price ON meals(price);

-- Orders indexes
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_driver_id ON orders(driver_id);
CREATE INDEX idx_orders_restaurant_id ON orders(restaurant_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_delivery_location ON orders USING GIST(delivery_location);
CREATE INDEX idx_orders_order_number ON orders(order_number);

-- Payments indexes
CREATE INDEX idx_payments_order_id ON payments(order_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_created_at ON payments(created_at);

-- Notifications indexes
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_driver_id ON notifications(driver_id);
CREATE INDEX idx_notifications_restaurant_id ON notifications(restaurant_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- Reviews indexes
CREATE INDEX idx_reviews_restaurant_id ON reviews(restaurant_id);
CREATE INDEX idx_reviews_driver_id ON reviews(driver_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);
CREATE INDEX idx_reviews_created_at ON reviews(created_at);

-- Audit logs indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_admin_id ON audit_logs(admin_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- =====================================================
-- FUNCTIONS (الدوال)
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to calculate distance between two points
CREATE OR REPLACE FUNCTION calculate_distance(lat1 FLOAT, lon1 FLOAT, lat2 FLOAT, lon2 FLOAT)
RETURNS FLOAT AS $$
BEGIN
    RETURN ST_Distance(
        ST_GeogFromText('POINT(' || lon1 || ' ' || lat1 || ')'),
        ST_GeogFromText('POINT(' || lon2 || ' ' || lat2 || ')')
    ) / 1000; -- Convert to kilometers
END;
$$ LANGUAGE plpgsql;

-- Function to generate order number
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := 'ORD' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(NEXTVAL('order_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create sequence for order numbers
CREATE SEQUENCE IF NOT EXISTS order_number_seq START 1;

-- Function to update restaurant rating
CREATE OR REPLACE FUNCTION update_restaurant_rating()
RETURNS TRIGGER AS $$
DECLARE
    target_restaurant_id uuid;
BEGIN
    -- Determine which restaurant to update
    IF TG_OP = 'DELETE' THEN
        target_restaurant_id := OLD.restaurant_id;
    ELSE
        target_restaurant_id := NEW.restaurant_id;
    END IF;

    -- Update restaurant rating and count
    UPDATE restaurants
    SET rating = (
        SELECT COALESCE(AVG(rating), 0)
        FROM reviews
        WHERE restaurant_id = target_restaurant_id
    ),
    total_ratings = (
        SELECT COUNT(*)
        FROM reviews
        WHERE restaurant_id = target_restaurant_id
    )
    WHERE id = target_restaurant_id;

    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to update driver rating
CREATE OR REPLACE FUNCTION update_driver_rating()
RETURNS TRIGGER AS $$
DECLARE
    target_driver_id uuid;
BEGIN
    -- Determine which driver to update
    IF TG_OP = 'DELETE' THEN
        target_driver_id := OLD.driver_id;
    ELSE
        target_driver_id := NEW.driver_id;
    END IF;

    -- Only update if driver_id is not null
    IF target_driver_id IS NOT NULL THEN
        UPDATE drivers
        SET rating = (
            SELECT COALESCE(AVG(delivery_rating), 5.0)
            FROM reviews
            WHERE driver_id = target_driver_id AND delivery_rating IS NOT NULL
        ),
        total_ratings = (
            SELECT COUNT(*)
            FROM reviews
            WHERE driver_id = target_driver_id AND delivery_rating IS NOT NULL
        )
        WHERE id = target_driver_id;
    END IF;

    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to update wallet balance
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE wallets
        SET balance = balance + CASE
            WHEN NEW.type IN ('credit', 'refund') THEN NEW.amount
            ELSE -NEW.amount
        END
        WHERE id = NEW.wallet_id;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to create wallet for new users
CREATE OR REPLACE FUNCTION create_user_wallet()
RETURNS TRIGGER AS $$
BEGIN
    -- Create wallet for customer
    IF NEW.role = 'customer' THEN
        INSERT INTO wallets (user_id, balance, currency)
        VALUES (NEW.id, 0, 'MAD');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create wallet for new drivers
CREATE OR REPLACE FUNCTION create_driver_wallet()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO wallets (driver_id, balance, currency)
    VALUES (NEW.id, 0, 'MAD');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create wallet for new restaurants
CREATE OR REPLACE FUNCTION create_restaurant_wallet()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO wallets (restaurant_id, balance, currency)
    VALUES (NEW.id, 0, 'MAD');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update driver statistics when order is completed
CREATE OR REPLACE FUNCTION update_driver_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update driver stats when order status changes to delivered
    IF NEW.status = 'delivered' AND (OLD.status IS NULL OR OLD.status != 'delivered') THEN
        UPDATE drivers
        SET total_deliveries = total_deliveries + 1,
            earnings_today = CASE
                WHEN DATE(NOW()) = DATE(NEW.updated_at) THEN earnings_today + COALESCE((NEW.total_price * 0.1), 0)
                ELSE earnings_today
            END,
            earnings_total = earnings_total + COALESCE((NEW.total_price * 0.1), 0)
        WHERE id = NEW.driver_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS (المشغلات)
-- =====================================================

-- Triggers for updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_drivers_updated_at BEFORE UPDATE ON drivers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_restaurants_updated_at BEFORE UPDATE ON restaurants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meals_updated_at BEFORE UPDATE ON meals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON wallets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_promotions_updated_at BEFORE UPDATE ON promotions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_delivery_zones_updated_at BEFORE UPDATE ON delivery_zones FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_support_tickets_updated_at BEFORE UPDATE ON support_tickets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_management_updated_at BEFORE UPDATE ON content_management FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_items_updated_at BEFORE UPDATE ON inventory_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to generate order number
CREATE TRIGGER set_order_number BEFORE INSERT ON orders FOR EACH ROW EXECUTE FUNCTION generate_order_number();

-- Triggers for rating updates
CREATE TRIGGER update_restaurant_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_restaurant_rating();

CREATE TRIGGER update_driver_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_driver_rating();

-- Trigger for wallet balance updates
CREATE TRIGGER update_wallet_balance_trigger AFTER INSERT ON wallet_transactions FOR EACH ROW EXECUTE FUNCTION update_wallet_balance();

-- Triggers for automatic wallet creation
CREATE TRIGGER create_user_wallet_trigger AFTER INSERT ON users FOR EACH ROW EXECUTE FUNCTION create_user_wallet();
CREATE TRIGGER create_driver_wallet_trigger AFTER INSERT ON drivers FOR EACH ROW EXECUTE FUNCTION create_driver_wallet();
CREATE TRIGGER create_restaurant_wallet_trigger AFTER INSERT ON restaurants FOR EACH ROW EXECUTE FUNCTION create_restaurant_wallet();

-- Trigger for driver statistics updates
CREATE TRIGGER update_driver_stats_trigger AFTER UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_driver_stats();

-- =====================================================
-- INITIAL DATA (البيانات الأولية)
-- =====================================================

-- Insert default categories
INSERT INTO categories (name, name_en, description, icon, sort_order) VALUES
('وجبات سريعة', 'Fast Food', 'وجبات سريعة ولذيذة', '🍔', 1),
('بيتزا', 'Pizza', 'بيتزا بأنواعها المختلفة', '🍕', 2),
('مأكولات عربية', 'Arabic Food', 'أطباق عربية تقليدية', '🥙', 3),
('حلويات', 'Desserts', 'حلويات شرقية وغربية', '🍰', 4),
('مشروبات', 'Beverages', 'مشروبات ساخنة وباردة', '🥤', 5),
('سلطات', 'Salads', 'سلطات صحية ومنوعة', '🥗', 6),
('مأكولات آسيوية', 'Asian Food', 'أطباق آسيوية متنوعة', '🍜', 7),
('مأكولات بحرية', 'Seafood', 'أسماك ومأكولات بحرية طازجة', '🐟', 8);

-- Insert default admin user
INSERT INTO admin_users (username, email, password_hash, full_name, role, permissions) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlDO', 'مدير النظام', 'super_admin', '["all"]');

-- Insert default system settings
INSERT INTO system_settings (key, value, description, category, is_public) VALUES
('app_name', '"وصلتي"', 'اسم التطبيق', 'general', true),
('app_version', '"1.0.0"', 'إصدار التطبيق', 'general', true),
('currency', '"MAD"', 'العملة الافتراضية', 'general', true),
('default_language', '"ar"', 'اللغة الافتراضية', 'general', true),
('delivery_fee', '15', 'رسوم التوصيل الافتراضية', 'delivery', true),
('minimum_order', '50', 'الحد الأدنى للطلب', 'orders', true),
('commission_rate', '0.15', 'نسبة العمولة', 'financial', false),
('max_delivery_distance', '20', 'أقصى مسافة توصيل بالكيلومتر', 'delivery', false),
('support_phone', '"+212600000000"', 'رقم هاتف الدعم الفني', 'support', true),
('support_email', '"<EMAIL>"', 'بريد الدعم الفني', 'support', true);

-- =====================================================
-- VIEWS (العروض)
-- =====================================================

-- View for order statistics
CREATE VIEW order_statistics AS
SELECT
    DATE(created_at) as order_date,
    COUNT(*) as total_orders,
    SUM(total_price) as total_revenue,
    AVG(total_price) as average_order_value,
    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders
FROM orders
GROUP BY DATE(created_at)
ORDER BY order_date DESC;

-- View for restaurant performance
CREATE VIEW restaurant_performance AS
SELECT
    r.id,
    r.name,
    r.rating,
    r.total_ratings,
    COUNT(o.id) as total_orders,
    SUM(o.total_price) as total_revenue,
    AVG(o.total_price) as average_order_value,
    COUNT(CASE WHEN o.status = 'delivered' THEN 1 END) as successful_orders,
    ROUND(
        COUNT(CASE WHEN o.status = 'delivered' THEN 1 END)::DECIMAL /
        NULLIF(COUNT(o.id), 0) * 100, 2
    ) as success_rate
FROM restaurants r
LEFT JOIN orders o ON r.id = o.restaurant_id
GROUP BY r.id, r.name, r.rating, r.total_ratings;

-- View for driver performance
CREATE VIEW driver_performance AS
SELECT
    d.id,
    d.full_name,
    d.rating,
    d.total_ratings,
    d.total_deliveries,
    d.earnings_total,
    COUNT(o.id) as orders_today,
    SUM(CASE WHEN o.status = 'delivered' THEN 1 ELSE 0 END) as successful_deliveries,
    ROUND(
        SUM(CASE WHEN o.status = 'delivered' THEN 1 ELSE 0 END)::DECIMAL /
        NULLIF(COUNT(o.id), 0) * 100, 2
    ) as success_rate
FROM drivers d
LEFT JOIN orders o ON d.id = o.driver_id AND DATE(o.created_at) = CURRENT_DATE
GROUP BY d.id, d.full_name, d.rating, d.total_ratings, d.total_deliveries, d.earnings_total;

-- =====================================================
-- SECURITY POLICIES (سياسات الأمان)
-- =====================================================

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE restaurants ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (يمكن تخصيصها حسب الحاجة)
CREATE POLICY "Users can view own data" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own data" ON users FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Drivers can view own data" ON drivers FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Drivers can update own data" ON drivers FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Restaurants can view own data" ON restaurants FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Restaurants can update own data" ON restaurants FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- COMMENTS (التعليقات)
-- =====================================================

COMMENT ON TABLE users IS 'جدول المستخدمين - العملاء والإداريين';
COMMENT ON TABLE drivers IS 'جدول السائقين';
COMMENT ON TABLE restaurants IS 'جدول المطاعم';
COMMENT ON TABLE meals IS 'جدول الوجبات';
COMMENT ON TABLE orders IS 'جدول الطلبات';
COMMENT ON TABLE payments IS 'جدول المدفوعات';
COMMENT ON TABLE wallets IS 'جدول المحافظ الإلكترونية';
COMMENT ON TABLE notifications IS 'جدول الإشعارات';
COMMENT ON TABLE reviews IS 'جدول التقييمات والمراجعات';
COMMENT ON TABLE promotions IS 'جدول العروض والخصومات';
COMMENT ON TABLE support_tickets IS 'جدول تذاكر الدعم الفني';
COMMENT ON TABLE admin_users IS 'جدول المستخدمين الإداريين';
COMMENT ON TABLE audit_logs IS 'جدول سجل العمليات';
COMMENT ON TABLE system_settings IS 'جدول إعدادات النظام';
COMMENT ON TABLE analytics_events IS 'جدول أحداث التحليلات';
COMMENT ON TABLE inventory_items IS 'جدول عناصر المخزون';
COMMENT ON TABLE delivery_routes IS 'جدول مسارات التوصيل';
COMMENT ON TABLE financial_reports IS 'جدول التقارير المالية';
