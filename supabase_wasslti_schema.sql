
-- Table: users (العملاء)
CREATE TABLE users (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    full_name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT UNIQUE NOT NULL,
    password TEXT,
    profile_image TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: drivers (السائقين)
CREATE TABLE drivers (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    full_name TEXT NOT NULL,
    phone TEXT UNIQUE NOT NULL,
    vehicle_type TEXT,
    documents JSONB,
    is_verified BOOLEAN DEFAULT FALSE,
    current_location GEOGRAPHY(POINT, 4326),
    rating FLOAT DEFAULT 5.0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: restaurants
CREATE TABLE restaurants (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT,
    location GEOGRAPHY(POINT, 4326),
    address TEXT,
    rating FLOAT DEFAULT 0.0,
    is_verified BOOLEAN DEFAULT FALSE,
    logo TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: meals
CREATE TABLE meals (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    image TEXT,
    price DECIMAL(10, 2) NOT NULL,
    category TEXT,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: orders
CREATE TABLE orders (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE SET NULL,
    driver_id uuid REFERENCES drivers(id) ON DELETE SET NULL,
    restaurant_id uuid REFERENCES restaurants(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'pending',
    total_price DECIMAL(10, 2),
    delivery_address TEXT,
    delivery_lat DOUBLE PRECISION,
    delivery_lng DOUBLE PRECISION,
    scheduled_time TIMESTAMP,
    payment_method TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Table: order_items
CREATE TABLE order_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id uuid REFERENCES orders(id) ON DELETE CASCADE,
    meal_id uuid REFERENCES meals(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL
);

-- Table: payments
CREATE TABLE payments (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id uuid REFERENCES orders(id) ON DELETE CASCADE,
    user_id uuid REFERENCES users(id),
    amount DECIMAL(10, 2),
    payment_method TEXT,
    status TEXT DEFAULT 'unpaid',
    payment_reference TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
