import 'driver_location.dart';

class Driver {
  final String id;
  final String name;
  final String phone;
  final String email;
  final String vehicleType;
  final String vehiclePlate;
  final double rating;
  final int totalDeliveries;
  final bool isOnline;
  final DriverLocation currentLocation;
  final DateTime? joinDate;

  const Driver({
    required this.id,
    required this.name,
    required this.phone,
    required this.email,
    required this.vehicleType,
    required this.vehiclePlate,
    required this.rating,
    required this.totalDeliveries,
    required this.isOnline,
    required this.currentLocation,
    this.joinDate,
  });

  Driver copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? vehicleType,
    String? vehiclePlate,
    double? rating,
    int? totalDeliveries,
    bool? isOnline,
    DriverLocation? currentLocation,
    DateTime? joinDate,
  }) {
    return Driver(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      vehicleType: vehicleType ?? this.vehicleType,
      vehiclePlate: vehiclePlate ?? this.vehiclePlate,
      rating: rating ?? this.rating,
      totalDeliveries: totalDeliveries ?? this.totalDeliveries,
      isOnline: isOnline ?? this.isOnline,
      currentLocation: currentLocation ?? this.currentLocation,
      joinDate: joinDate ?? this.joinDate,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'vehicleType': vehicleType,
      'vehiclePlate': vehiclePlate,
      'rating': rating,
      'totalDeliveries': totalDeliveries,
      'isOnline': isOnline,
      'currentLocation': currentLocation.toJson(),
      'joinDate': joinDate?.toIso8601String(),
    };
  }

  factory Driver.fromJson(Map<String, dynamic> json) {
    return Driver(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      vehicleType: json['vehicleType'] ?? '',
      vehiclePlate: json['vehiclePlate'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      totalDeliveries: json['totalDeliveries'] ?? 0,
      isOnline: json['isOnline'] ?? false,
      currentLocation: DriverLocation.fromJson(json['currentLocation'] ?? {}),
      joinDate: json['joinDate'] != null 
          ? DateTime.parse(json['joinDate']) 
          : null,
    );
  }
}

class DriverStats {
  final double todayEarnings;
  final double weeklyEarnings;
  final double monthlyEarnings;
  final int todayDeliveries;
  final int weeklyDeliveries;
  final int monthlyDeliveries;
  final double averageRating;
  final int totalRatings;

  const DriverStats({
    required this.todayEarnings,
    required this.weeklyEarnings,
    required this.monthlyEarnings,
    required this.todayDeliveries,
    required this.weeklyDeliveries,
    required this.monthlyDeliveries,
    required this.averageRating,
    required this.totalRatings,
  });

  Map<String, dynamic> toJson() {
    return {
      'todayEarnings': todayEarnings,
      'weeklyEarnings': weeklyEarnings,
      'monthlyEarnings': monthlyEarnings,
      'todayDeliveries': todayDeliveries,
      'weeklyDeliveries': weeklyDeliveries,
      'monthlyDeliveries': monthlyDeliveries,
      'averageRating': averageRating,
      'totalRatings': totalRatings,
    };
  }

  factory DriverStats.fromJson(Map<String, dynamic> json) {
    return DriverStats(
      todayEarnings: (json['todayEarnings'] ?? 0.0).toDouble(),
      weeklyEarnings: (json['weeklyEarnings'] ?? 0.0).toDouble(),
      monthlyEarnings: (json['monthlyEarnings'] ?? 0.0).toDouble(),
      todayDeliveries: json['todayDeliveries'] ?? 0,
      weeklyDeliveries: json['weeklyDeliveries'] ?? 0,
      monthlyDeliveries: json['monthlyDeliveries'] ?? 0,
      averageRating: (json['averageRating'] ?? 0.0).toDouble(),
      totalRatings: json['totalRatings'] ?? 0,
    );
  }
}
