import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'constants/driver_themes.dart';
import 'providers/auth_provider.dart';
import 'providers/language_provider.dart';
import 'models/driver_status.dart';
import 'widgets/simple_top_bar.dart';
import 'screens/settings/settings_screen_en.dart';

/// Test language switching functionality
void main() {
  runApp(const TestLanguageSwitchApp());
}

class TestLanguageSwitchApp extends StatelessWidget {
  const TestLanguageSwitchApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
      ],
      child: Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
          return MaterialApp(
            title: 'Test Language Switch',
            debugShowCheckedModeBanner: false,
            theme: DriverThemes.lightTheme,
            darkTheme: DriverThemes.darkTheme,
            themeMode: ThemeMode.system,
            home: const TestLanguageSwitchScreen(),
          );
        },
      ),
    );
  }
}

class TestLanguageSwitchScreen extends StatefulWidget {
  const TestLanguageSwitchScreen({super.key});

  @override
  State<TestLanguageSwitchScreen> createState() => _TestLanguageSwitchScreenState();
}

class _TestLanguageSwitchScreenState extends State<TestLanguageSwitchScreen> {
  DriverStatus _driverStatus = DriverStatus.working;

  @override
  void initState() {
    super.initState();
    // Initialize language provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LanguageProvider>().initializeLanguage();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
      body: Stack(
        children: [
          // Background
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  isDark ? const Color(0xFF0F231A) : const Color(0xFFF0F8FF),
                  isDark ? const Color(0xFF1B3B2E) : const Color(0xFFE8F5E8),
                ],
              ),
            ),
          ),

          // Content
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.language,
                  size: 80,
                  color: Color(0xFF11B96F),
                ),
                const SizedBox(height: 20),
                Text(
                  languageProvider.isEnglish 
                      ? 'Language Switch Test'
                      : 'اختبار تغيير اللغة',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                Text(
                  languageProvider.isEnglish
                      ? 'Click the settings gear to change language'
                      : 'انقر على زر الترس لتغيير اللغة',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                
                // Current language info
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.symmetric(horizontal: 32),
                  decoration: BoxDecoration(
                    color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Text(
                        languageProvider.isEnglish 
                            ? 'Current Language:' 
                            : 'اللغة الحالية:',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        languageProvider.currentLanguage,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF11B96F),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        languageProvider.isEnglish 
                            ? 'Text Direction:' 
                            : 'اتجاه النص:',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        languageProvider.textDirection == TextDirection.ltr 
                            ? 'Left to Right (LTR)' 
                            : 'Right to Left (RTL)',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF11B96F),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 40),
                
                // Status buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildStatusButton(
                      languageProvider.statusWorking, 
                      DriverStatus.working, 
                      Colors.green
                    ),
                    const SizedBox(width: 10),
                    _buildStatusButton(
                      languageProvider.statusResting, 
                      DriverStatus.resting, 
                      Colors.orange
                    ),
                    const SizedBox(width: 10),
                    _buildStatusButton(
                      languageProvider.statusStopped, 
                      DriverStatus.stopped, 
                      Colors.red
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Top bar with settings
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: FloatingTopBar(
              driverStatus: _driverStatus,
              onStatusTap: _handleStatusTap,
              onSettingsTap: _handleSettingsTap,
              onSupportTap: _handleSupportTap,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusButton(String text, DriverStatus status, Color color) {
    final isSelected = _driverStatus == status;
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _driverStatus = status;
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? color : Colors.grey,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        text,
        style: const TextStyle(fontSize: 11),
      ),
    );
  }

  void _handleStatusTap() {
    setState(() {
      switch (_driverStatus) {
        case DriverStatus.working:
          _driverStatus = DriverStatus.resting;
          break;
        case DriverStatus.resting:
          _driverStatus = DriverStatus.stopped;
          break;
        case DriverStatus.stopped:
          _driverStatus = DriverStatus.working;
          break;
      }
    });
  }

  void _handleSettingsTap() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SettingsScreenEN(),
      ),
    );
  }

  void _handleSupportTap() {
    final languageProvider = context.read<LanguageProvider>();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.isEnglish 
              ? 'Support button pressed' 
              : 'تم النقر على زر الدعم'
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
