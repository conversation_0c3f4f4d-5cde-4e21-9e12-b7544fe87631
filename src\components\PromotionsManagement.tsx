import React, { useState } from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar, 
  Percent, 
  Gift, 
  Target,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Copy,
  Share2,
  BarChart3
} from 'lucide-react';

interface Promotion {
  id: string;
  title: string;
  description: string;
  type: 'percentage' | 'fixed' | 'bogo' | 'free_delivery';
  value: number;
  code: string;
  startDate: string;
  endDate: string;
  status: 'active' | 'inactive' | 'expired' | 'scheduled';
  usageLimit: number;
  usageCount: number;
  minOrderAmount: number;
  targetAudience: 'all' | 'new_users' | 'loyal_customers' | 'specific_restaurants';
  restaurants: string[];
  createdAt: string;
}

const PromotionsManagement = () => {
  const [promotions, setPromotions] = useState<Promotion[]>([
    {
      id: '1',
      title: 'خصم 20% على الطلب الأول',
      description: 'خصم 20% للعملاء الجدد على أول طلب',
      type: 'percentage',
      value: 20,
      code: 'WELCOME20',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      status: 'active',
      usageLimit: 1000,
      usageCount: 245,
      minOrderAmount: 50,
      targetAudience: 'new_users',
      restaurants: [],
      createdAt: '2024-01-01'
    },
    {
      id: '2',
      title: 'توصيل مجاني',
      description: 'توصيل مجاني للطلبات أكثر من 100 درهم',
      type: 'free_delivery',
      value: 0,
      code: 'FREEDEL100',
      startDate: '2024-01-15',
      endDate: '2024-02-15',
      status: 'active',
      usageLimit: 500,
      usageCount: 89,
      minOrderAmount: 100,
      targetAudience: 'all',
      restaurants: [],
      createdAt: '2024-01-15'
    },
    {
      id: '3',
      title: 'خصم 50 درهم',
      description: 'خصم ثابت 50 درهم للعملاء المميزين',
      type: 'fixed',
      value: 50,
      code: 'LOYAL50',
      startDate: '2024-02-01',
      endDate: '2024-02-28',
      status: 'scheduled',
      usageLimit: 200,
      usageCount: 0,
      minOrderAmount: 150,
      targetAudience: 'loyal_customers',
      restaurants: [],
      createdAt: '2024-01-20'
    }
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedPromotion, setSelectedPromotion] = useState<Promotion | null>(null);
  const [filterStatus, setFilterStatus] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'inactive': return <XCircle className="w-4 h-4" />;
      case 'expired': return <AlertCircle className="w-4 h-4" />;
      case 'scheduled': return <Clock className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'percentage': return 'نسبة مئوية';
      case 'fixed': return 'مبلغ ثابت';
      case 'bogo': return 'اشتري واحد واحصل على آخر';
      case 'free_delivery': return 'توصيل مجاني';
      default: return type;
    }
  };

  const getAudienceLabel = (audience: string) => {
    switch (audience) {
      case 'all': return 'جميع العملاء';
      case 'new_users': return 'العملاء الجدد';
      case 'loyal_customers': return 'العملاء المميزين';
      case 'specific_restaurants': return 'مطاعم محددة';
      default: return audience;
    }
  };

  const filteredPromotions = promotions.filter(promo => {
    if (filterStatus === 'all') return true;
    return promo.status === filterStatus;
  });

  const togglePromotionStatus = (id: string) => {
    setPromotions(prev => prev.map(promo => 
      promo.id === id 
        ? { ...promo, status: promo.status === 'active' ? 'inactive' : 'active' }
        : promo
    ));
  };

  const deletePromotion = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا العرض؟')) {
      setPromotions(prev => prev.filter(promo => promo.id !== id));
    }
  };

  const copyPromotionCode = (code: string) => {
    navigator.clipboard.writeText(code);
    alert(`تم نسخ الكود: ${code}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">إدارة العروض والخصومات</h2>
          <p className="text-gray-600 rtl-font">إنشاء وإدارة العروض الترويجية والكوبونات</p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center space-x-2 rtl:space-x-reverse bg-primary text-white px-6 py-3 rounded-xl hover:bg-primary/90 transition-colors"
        >
          <Plus className="w-5 h-5" />
          <span className="font-medium rtl-font">إضافة عرض جديد</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">العروض النشطة</p>
              <p className="text-2xl font-bold text-green-600">
                {promotions.filter(p => p.status === 'active').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">إجمالي الاستخدامات</p>
              <p className="text-2xl font-bold text-blue-600">
                {promotions.reduce((sum, p) => sum + p.usageCount, 0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">معدل الاستخدام</p>
              <p className="text-2xl font-bold text-purple-600">
                {Math.round((promotions.reduce((sum, p) => sum + p.usageCount, 0) / promotions.reduce((sum, p) => sum + p.usageLimit, 0)) * 100)}%
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">العروض المجدولة</p>
              <p className="text-2xl font-bold text-orange-600">
                {promotions.filter(p => p.status === 'scheduled').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <span className="font-medium text-gray-700 rtl-font">فلترة حسب الحالة:</span>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
            >
              <option value="all">جميع العروض</option>
              <option value="active">نشط</option>
              <option value="inactive">غير نشط</option>
              <option value="scheduled">مجدول</option>
              <option value="expired">منتهي الصلاحية</option>
            </select>
          </div>
          
          <div className="text-sm text-gray-600 rtl-font">
            عرض {filteredPromotions.length} من {promotions.length} عرض
          </div>
        </div>
      </div>

      {/* Promotions List */}
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">العرض</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">النوع</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الكود</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الفترة</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الاستخدام</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الحالة</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredPromotions.map((promotion) => (
                <tr key={promotion.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div>
                      <div className="font-medium text-gray-900 rtl-font">{promotion.title}</div>
                      <div className="text-sm text-gray-500 rtl-font">{promotion.description}</div>
                      <div className="text-xs text-gray-400 rtl-font mt-1">
                        {getAudienceLabel(promotion.targetAudience)}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      {promotion.type === 'percentage' && <Percent className="w-4 h-4 text-blue-500" />}
                      {promotion.type === 'fixed' && <Gift className="w-4 h-4 text-green-500" />}
                      {promotion.type === 'free_delivery' && <Target className="w-4 h-4 text-purple-500" />}
                      <span className="text-sm text-gray-700 rtl-font">{getTypeLabel(promotion.type)}</span>
                    </div>
                    {promotion.type !== 'free_delivery' && (
                      <div className="text-sm font-medium text-primary">
                        {promotion.type === 'percentage' ? `${promotion.value}%` : `${promotion.value} درهم`}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                        {promotion.code}
                      </code>
                      <button
                        onClick={() => copyPromotionCode(promotion.code)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-700">
                      <div className="rtl-font">{new Date(promotion.startDate).toLocaleDateString('ar-MA')}</div>
                      <div className="text-gray-500 rtl-font">إلى {new Date(promotion.endDate).toLocaleDateString('ar-MA')}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">
                        {promotion.usageCount} / {promotion.usageLimit}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div 
                          className="bg-primary h-2 rounded-full" 
                          style={{ width: `${(promotion.usageCount / promotion.usageLimit) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center space-x-1 rtl:space-x-reverse px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(promotion.status)}`}>
                      {getStatusIcon(promotion.status)}
                      <span className="rtl-font">
                        {promotion.status === 'active' ? 'نشط' : 
                         promotion.status === 'inactive' ? 'غير نشط' :
                         promotion.status === 'expired' ? 'منتهي' : 'مجدول'}
                      </span>
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        onClick={() => setSelectedPromotion(promotion)}
                        className="text-blue-600 hover:text-blue-800"
                        title="عرض التفاصيل"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => togglePromotionStatus(promotion.id)}
                        className={`${promotion.status === 'active' ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'}`}
                        title={promotion.status === 'active' ? 'إيقاف' : 'تفعيل'}
                      >
                        {promotion.status === 'active' ? <XCircle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                      </button>
                      <button
                        className="text-gray-600 hover:text-gray-800"
                        title="تعديل"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => deletePromotion(promotion.id)}
                        className="text-red-600 hover:text-red-800"
                        title="حذف"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Promotion Details Modal */}
      {selectedPromotion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800 rtl-font">تفاصيل العرض</h3>
              <button
                onClick={() => setSelectedPromotion(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">عنوان العرض</label>
                  <p className="text-gray-900 rtl-font">{selectedPromotion.title}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">نوع العرض</label>
                  <p className="text-gray-900 rtl-font">{getTypeLabel(selectedPromotion.type)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">كود الخصم</label>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <code className="bg-gray-100 px-3 py-2 rounded font-mono">{selectedPromotion.code}</code>
                    <button
                      onClick={() => copyPromotionCode(selectedPromotion.code)}
                      className="text-primary hover:text-primary/80"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">قيمة الخصم</label>
                  <p className="text-gray-900">
                    {selectedPromotion.type === 'percentage' ? `${selectedPromotion.value}%` : 
                     selectedPromotion.type === 'fixed' ? `${selectedPromotion.value} درهم` : 'توصيل مجاني'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">الحد الأدنى للطلب</label>
                  <p className="text-gray-900">{selectedPromotion.minOrderAmount} درهم</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">الجمهور المستهدف</label>
                  <p className="text-gray-900 rtl-font">{getAudienceLabel(selectedPromotion.targetAudience)}</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">وصف العرض</label>
                <p className="text-gray-900 rtl-font">{selectedPromotion.description}</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-xl">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                    <Users className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-800 rtl-font">الاستخدامات</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600">
                    {selectedPromotion.usageCount} / {selectedPromotion.usageLimit}
                  </p>
                </div>
                
                <div className="bg-green-50 p-4 rounded-xl">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                    <Calendar className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-green-800 rtl-font">تاريخ البداية</span>
                  </div>
                  <p className="text-lg font-semibold text-green-600">
                    {new Date(selectedPromotion.startDate).toLocaleDateString('ar-MA')}
                  </p>
                </div>
                
                <div className="bg-red-50 p-4 rounded-xl">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                    <Calendar className="w-5 h-5 text-red-600" />
                    <span className="font-medium text-red-800 rtl-font">تاريخ الانتهاء</span>
                  </div>
                  <p className="text-lg font-semibold text-red-600">
                    {new Date(selectedPromotion.endDate).toLocaleDateString('ar-MA')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PromotionsManagement;
