import React, { useState } from 'react';
import { 
  CreditCard, 
  Wallet, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  Download,
  Eye,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Filter,
  Search,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Building,
  User,
  Car,
  Percent,
  PieChart,
  BarChart3
} from 'lucide-react';

interface Transaction {
  id: string;
  type: 'payment' | 'refund' | 'commission' | 'withdrawal' | 'deposit';
  amount: number;
  currency: string;
  status: 'completed' | 'pending' | 'failed' | 'cancelled';
  method: 'card' | 'cash' | 'wallet' | 'bank_transfer';
  from: string;
  to: string;
  description: string;
  timestamp: string;
  orderId?: string;
  fees: number;
  netAmount: number;
}

interface WalletBalance {
  userId: string;
  userType: 'customer' | 'restaurant' | 'driver';
  userName: string;
  balance: number;
  pendingAmount: number;
  totalEarnings: number;
  lastTransaction: string;
}

const PaymentManagement = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([
    {
      id: 'TXN001',
      type: 'payment',
      amount: 125.50,
      currency: 'MAD',
      status: 'completed',
      method: 'card',
      from: 'أحمد محمد',
      to: 'مطعم الأصالة',
      description: 'دفع طلب #ORD123',
      timestamp: '2024-01-20T14:30:00Z',
      orderId: 'ORD123',
      fees: 3.15,
      netAmount: 122.35
    },
    {
      id: 'TXN002',
      type: 'commission',
      amount: 18.75,
      currency: 'MAD',
      status: 'completed',
      method: 'wallet',
      from: 'مطعم الأصالة',
      to: 'وصلتي',
      description: 'عمولة طلب #ORD123',
      timestamp: '2024-01-20T14:31:00Z',
      orderId: 'ORD123',
      fees: 0,
      netAmount: 18.75
    },
    {
      id: 'TXN003',
      type: 'withdrawal',
      amount: 500.00,
      currency: 'MAD',
      status: 'pending',
      method: 'bank_transfer',
      from: 'محفظة السائق',
      to: 'يوسف العلوي',
      description: 'سحب أرباح السائق',
      timestamp: '2024-01-20T13:15:00Z',
      fees: 10.00,
      netAmount: 490.00
    }
  ]);

  const [walletBalances, setWalletBalances] = useState<WalletBalance[]>([
    {
      userId: 'REST001',
      userType: 'restaurant',
      userName: 'مطعم الأصالة',
      balance: 2450.75,
      pendingAmount: 320.50,
      totalEarnings: 15680.25,
      lastTransaction: '2024-01-20T14:30:00Z'
    },
    {
      userId: 'DRV001',
      userType: 'driver',
      userName: 'يوسف العلوي',
      balance: 890.30,
      pendingAmount: 125.00,
      totalEarnings: 8950.75,
      lastTransaction: '2024-01-20T13:45:00Z'
    },
    {
      userId: 'CUST001',
      userType: 'customer',
      userName: 'فاطمة الزهراء',
      balance: 45.20,
      pendingAmount: 0,
      totalEarnings: 0,
      lastTransaction: '2024-01-19T19:20:00Z'
    }
  ]);

  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'failed': return <XCircle className="w-4 h-4" />;
      case 'cancelled': return <AlertCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'payment': return 'دفع';
      case 'refund': return 'استرداد';
      case 'commission': return 'عمولة';
      case 'withdrawal': return 'سحب';
      case 'deposit': return 'إيداع';
      default: return type;
    }
  };

  const getMethodLabel = (method: string) => {
    switch (method) {
      case 'card': return 'بطاقة ائتمان';
      case 'cash': return 'نقداً';
      case 'wallet': return 'محفظة';
      case 'bank_transfer': return 'تحويل بنكي';
      default: return method;
    }
  };

  const getUserTypeIcon = (userType: string) => {
    switch (userType) {
      case 'restaurant': return <Building className="w-4 h-4" />;
      case 'driver': return <Car className="w-4 h-4" />;
      case 'customer': return <User className="w-4 h-4" />;
      default: return <User className="w-4 h-4" />;
    }
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesType = filterType === 'all' || transaction.type === filterType;
    const matchesStatus = filterStatus === 'all' || transaction.status === filterStatus;
    const matchesSearch = transaction.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         transaction.to.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         transaction.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesType && matchesStatus && matchesSearch;
  });

  const totalBalance = walletBalances.reduce((sum, wallet) => sum + wallet.balance, 0);
  const totalPending = walletBalances.reduce((sum, wallet) => sum + wallet.pendingAmount, 0);
  const totalEarnings = walletBalances.reduce((sum, wallet) => sum + wallet.totalEarnings, 0);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDateTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString('ar-MA'),
      time: date.toLocaleTimeString('ar-MA', { hour12: false })
    };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">إدارة المدفوعات والمحافظ</h2>
          <p className="text-gray-600 rtl-font">تتبع وإدارة جميع المعاملات المالية والمحافظ الإلكترونية</p>
        </div>
        
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <RefreshCw className="w-4 h-4" />
            <span className="rtl-font">تحديث</span>
          </button>
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Download className="w-4 h-4" />
            <span className="rtl-font">تصدير</span>
          </button>
        </div>
      </div>

      {/* Financial Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">إجمالي الأرصدة</p>
              <p className="text-2xl font-bold text-blue-600">{formatCurrency(totalBalance)}</p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500 font-medium">+12.5%</span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">من الشهر السابق</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Wallet className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">المبالغ المعلقة</p>
              <p className="text-2xl font-bold text-yellow-600">{formatCurrency(totalPending)}</p>
              <div className="flex items-center mt-2">
                <Clock className="w-4 h-4 text-yellow-500" />
                <span className="text-sm text-yellow-500 font-medium">قيد المعالجة</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">إجمالي الأرباح</p>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(totalEarnings)}</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500 font-medium">+8.3%</span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">نمو مستمر</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">المعاملات اليوم</p>
              <p className="text-2xl font-bold text-purple-600">
                {transactions.filter(t => {
                  const today = new Date().toDateString();
                  const txDate = new Date(t.timestamp).toDateString();
                  return today === txDate;
                }).length}
              </p>
              <div className="flex items-center mt-2">
                <CreditCard className="w-4 h-4 text-purple-500" />
                <span className="text-sm text-purple-500 font-medium">معاملة نشطة</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <CreditCard className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Wallet Balances */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800 rtl-font">أرصدة المحافظ</h3>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <PieChart className="w-5 h-5 text-gray-400" />
            <span className="text-sm text-gray-500 rtl-font">توزيع الأرصدة</span>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {walletBalances.map((wallet) => (
            <div key={wallet.userId} className="border border-gray-200 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  {getUserTypeIcon(wallet.userType)}
                  <span className="font-medium text-gray-900 rtl-font">{wallet.userName}</span>
                </div>
                <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full rtl-font">
                  {wallet.userType === 'restaurant' ? 'مطعم' : 
                   wallet.userType === 'driver' ? 'سائق' : 'عميل'}
                </span>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 rtl-font">الرصيد المتاح</span>
                  <span className="font-semibold text-green-600">{formatCurrency(wallet.balance)}</span>
                </div>
                
                {wallet.pendingAmount > 0 && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 rtl-font">قيد المعالجة</span>
                    <span className="font-semibold text-yellow-600">{formatCurrency(wallet.pendingAmount)}</span>
                  </div>
                )}
                
                {wallet.totalEarnings > 0 && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 rtl-font">إجمالي الأرباح</span>
                    <span className="font-semibold text-blue-600">{formatCurrency(wallet.totalEarnings)}</span>
                  </div>
                )}
                
                <div className="pt-2 border-t border-gray-100">
                  <span className="text-xs text-gray-500 rtl-font">
                    آخر معاملة: {formatDateTime(wallet.lastTransaction).date}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Transactions Filters */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="البحث في المعاملات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
            />
          </div>
          
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="all">جميع الأنواع</option>
            <option value="payment">دفع</option>
            <option value="refund">استرداد</option>
            <option value="commission">عمولة</option>
            <option value="withdrawal">سحب</option>
            <option value="deposit">إيداع</option>
          </select>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="all">جميع الحالات</option>
            <option value="completed">مكتمل</option>
            <option value="pending">قيد المعالجة</option>
            <option value="failed">فشل</option>
            <option value="cancelled">ملغي</option>
          </select>
          
          <div className="text-sm text-gray-600 rtl-font flex items-center">
            عرض {filteredTransactions.length} من {transactions.length} معاملة
          </div>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">رقم المعاملة</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">النوع</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">المبلغ</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">من/إلى</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الطريقة</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الحالة</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">التاريخ</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredTransactions.map((transaction) => {
                const { date, time } = formatDateTime(transaction.timestamp);
                return (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">#{transaction.id}</div>
                      {transaction.orderId && (
                        <div className="text-xs text-gray-500">طلب: {transaction.orderId}</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <span className="inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 rtl-font">
                        {getTypeLabel(transaction.type)}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-semibold text-gray-900">{formatCurrency(transaction.amount)}</div>
                      {transaction.fees > 0 && (
                        <div className="text-xs text-gray-500">رسوم: {formatCurrency(transaction.fees)}</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 rtl-font">{transaction.from}</div>
                      <div className="text-xs text-gray-500 rtl-font">إلى: {transaction.to}</div>
                    </td>
                    <td className="px-6 py-4">
                      <span className="text-sm text-gray-700 rtl-font">{getMethodLabel(transaction.method)}</span>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center space-x-1 rtl:space-x-reverse px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                        {getStatusIcon(transaction.status)}
                        <span className="rtl-font">
                          {transaction.status === 'completed' ? 'مكتمل' :
                           transaction.status === 'pending' ? 'قيد المعالجة' :
                           transaction.status === 'failed' ? 'فشل' : 'ملغي'}
                        </span>
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{time}</div>
                      <div className="text-xs text-gray-500">{date}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <button className="text-blue-600 hover:text-blue-800" title="عرض التفاصيل">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="text-green-600 hover:text-green-800" title="تصدير">
                          <Download className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PaymentManagement;
