import React, { useState } from 'react';
import { 
  Shield, 
  <PERSON>, 
  Key, 
  Eye, 
  EyeOff,
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Clock,
  User,
  Users,
  Settings,
  Activity,
  Globe,
  Smartphone,
  Monitor,
  MapPin,
  Calendar,
  Search,
  Filter,
  Download,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Ban,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  UserX,
  Zap
} from 'lucide-react';

interface SecurityEvent {
  id: string;
  type: 'login_attempt' | 'password_change' | 'permission_change' | 'suspicious_activity' | 'data_access' | 'system_breach';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  userName?: string;
  userRole?: string;
  description: string;
  ipAddress: string;
  userAgent: string;
  location?: string;
  timestamp: string;
  status: 'resolved' | 'investigating' | 'open' | 'false_positive';
  actionTaken?: string;
}

interface AccessControl {
  id: string;
  userId: string;
  userName: string;
  userRole: string;
  permissions: string[];
  lastAccess: string;
  accessCount: number;
  status: 'active' | 'suspended' | 'revoked';
  expiryDate?: string;
  createdBy: string;
  createdAt: string;
}

interface SecurityPolicy {
  id: string;
  name: string;
  category: 'authentication' | 'authorization' | 'data_protection' | 'session' | 'audit';
  description: string;
  isEnabled: boolean;
  settings: {
    [key: string]: any;
  };
  lastModified: string;
  modifiedBy: string;
}

const SecurityManagement = () => {
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([
    {
      id: 'SEC001',
      type: 'login_attempt',
      severity: 'high',
      userId: 'unknown',
      description: 'محاولة دخول فاشلة متكررة من IP مشبوه',
      ipAddress: '45.123.456.789',
      userAgent: 'curl/7.68.0',
      location: 'غير محدد',
      timestamp: '2024-01-20T14:30:00Z',
      status: 'investigating',
      actionTaken: 'تم حظر IP مؤقتاً'
    },
    {
      id: 'SEC002',
      type: 'permission_change',
      severity: 'medium',
      userId: 'ADM001',
      userName: 'أحمد المدير',
      userRole: 'مدير النظام',
      description: 'تم تعديل صلاحيات المستخدم محمد الموظف',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      location: 'الدار البيضاء، المغرب',
      timestamp: '2024-01-20T13:15:00Z',
      status: 'resolved'
    },
    {
      id: 'SEC003',
      type: 'suspicious_activity',
      severity: 'critical',
      userId: 'USR123',
      userName: 'مستخدم مجهول',
      description: 'محاولة الوصول لبيانات حساسة خارج ساعات العمل',
      ipAddress: '************',
      userAgent: 'Mozilla/5.0 (compatible; Bot/1.0)',
      location: 'خارج البلاد',
      timestamp: '2024-01-20T02:45:00Z',
      status: 'open',
      actionTaken: 'تم تعليق الحساب فوراً'
    }
  ]);

  const [accessControls, setAccessControls] = useState<AccessControl[]>([
    {
      id: 'AC001',
      userId: 'ADM001',
      userName: 'أحمد المدير',
      userRole: 'مدير النظام',
      permissions: ['read_all', 'write_all', 'delete_all', 'manage_users', 'system_config'],
      lastAccess: '2024-01-20T14:30:00Z',
      accessCount: 1250,
      status: 'active',
      createdBy: 'النظام',
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'AC002',
      userId: 'MGR001',
      userName: 'فاطمة المديرة',
      userRole: 'مدير المطاعم',
      permissions: ['read_restaurants', 'write_restaurants', 'read_orders', 'manage_menus'],
      lastAccess: '2024-01-20T12:15:00Z',
      accessCount: 890,
      status: 'active',
      expiryDate: '2024-12-31T23:59:59Z',
      createdBy: 'أحمد المدير',
      createdAt: '2024-01-15T10:00:00Z'
    },
    {
      id: 'AC003',
      userId: 'SUP001',
      userName: 'محمد الدعم',
      userRole: 'دعم فني',
      permissions: ['read_tickets', 'write_tickets', 'read_users'],
      lastAccess: '2024-01-19T16:45:00Z',
      accessCount: 456,
      status: 'suspended',
      createdBy: 'أحمد المدير',
      createdAt: '2024-01-10T09:00:00Z'
    }
  ]);

  const [securityPolicies, setSecurityPolicies] = useState<SecurityPolicy[]>([
    {
      id: 'POL001',
      name: 'سياسة كلمات المرور القوية',
      category: 'authentication',
      description: 'تطبيق متطلبات كلمات المرور القوية للمستخدمين',
      isEnabled: true,
      settings: {
        minLength: 8,
        requireUppercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        passwordExpiry: 90
      },
      lastModified: '2024-01-15T10:00:00Z',
      modifiedBy: 'أحمد المدير'
    },
    {
      id: 'POL002',
      name: 'المصادقة الثنائية',
      category: 'authentication',
      description: 'تفعيل المصادقة الثنائية لجميع المستخدمين الإداريين',
      isEnabled: true,
      settings: {
        mandatory: true,
        methods: ['sms', 'email', 'app'],
        sessionTimeout: 30
      },
      lastModified: '2024-01-10T14:30:00Z',
      modifiedBy: 'أحمد المدير'
    },
    {
      id: 'POL003',
      name: 'تشفير البيانات',
      category: 'data_protection',
      description: 'تشفير جميع البيانات الحساسة في قاعدة البيانات',
      isEnabled: true,
      settings: {
        encryptionLevel: 'AES-256',
        encryptPII: true,
        encryptPayments: true,
        keyRotation: 365
      },
      lastModified: '2024-01-05T09:00:00Z',
      modifiedBy: 'فريق الأمان'
    }
  ]);

  const [selectedTab, setSelectedTab] = useState('events');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterSeverity, setFilterSeverity] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'low': return <CheckCircle className="w-4 h-4" />;
      case 'medium': return <AlertTriangle className="w-4 h-4" />;
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <XCircle className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'investigating': return 'bg-yellow-100 text-yellow-800';
      case 'open': return 'bg-red-100 text-red-800';
      case 'false_positive': return 'bg-gray-100 text-gray-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'suspended': return 'bg-yellow-100 text-yellow-800';
      case 'revoked': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'resolved':
      case 'active':
        return <CheckCircle className="w-4 h-4" />;
      case 'investigating':
      case 'suspended':
        return <Clock className="w-4 h-4" />;
      case 'open':
      case 'revoked':
        return <XCircle className="w-4 h-4" />;
      case 'false_positive':
        return <Eye className="w-4 h-4" />;
      default:
        return <Shield className="w-4 h-4" />;
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'login_attempt': return <Key className="w-4 h-4" />;
      case 'password_change': return <Lock className="w-4 h-4" />;
      case 'permission_change': return <UserCheck className="w-4 h-4" />;
      case 'suspicious_activity': return <AlertTriangle className="w-4 h-4" />;
      case 'data_access': return <Eye className="w-4 h-4" />;
      case 'system_breach': return <Shield className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getEventTypeLabel = (type: string) => {
    switch (type) {
      case 'login_attempt': return 'محاولة دخول';
      case 'password_change': return 'تغيير كلمة مرور';
      case 'permission_change': return 'تغيير صلاحيات';
      case 'suspicious_activity': return 'نشاط مشبوه';
      case 'data_access': return 'الوصول للبيانات';
      case 'system_breach': return 'اختراق النظام';
      default: return type;
    }
  };

  const getDeviceIcon = (userAgent: string) => {
    if (userAgent.includes('Mobile') || userAgent.includes('Android') || userAgent.includes('iPhone')) {
      return <Smartphone className="w-4 h-4" />;
    } else if (userAgent.includes('Windows') || userAgent.includes('Mac') || userAgent.includes('Linux')) {
      return <Monitor className="w-4 h-4" />;
    } else {
      return <Globe className="w-4 h-4" />;
    }
  };

  const formatDateTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString('ar-MA'),
      time: date.toLocaleTimeString('ar-MA', { hour12: false })
    };
  };

  const filteredSecurityEvents = securityEvents.filter(event => {
    const matchesSearch = event.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (event.userName && event.userName.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesSeverity = filterSeverity === 'all' || event.severity === filterSeverity;
    const matchesStatus = filterStatus === 'all' || event.status === filterStatus;
    return matchesSearch && matchesSeverity && matchesStatus;
  });

  const togglePolicy = (policyId: string) => {
    setSecurityPolicies(prev => prev.map(policy => 
      policy.id === policyId ? { ...policy, isEnabled: !policy.isEnabled } : policy
    ));
  };

  const suspendUser = (accessId: string) => {
    setAccessControls(prev => prev.map(access => 
      access.id === accessId ? { ...access, status: 'suspended' } : access
    ));
  };

  const activateUser = (accessId: string) => {
    setAccessControls(prev => prev.map(access => 
      access.id === accessId ? { ...access, status: 'active' } : access
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">إدارة الأمان والصلاحيات</h2>
          <p className="text-gray-600 rtl-font">مراقبة الأمان وإدارة الصلاحيات والسياسات الأمنية</p>
        </div>
        
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <RefreshCw className="w-4 h-4" />
            <span className="rtl-font">تحديث</span>
          </button>
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Download className="w-4 h-4" />
            <span className="rtl-font">تصدير</span>
          </button>
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
            <Shield className="w-4 h-4" />
            <span className="rtl-font">فحص أمني</span>
          </button>
        </div>
      </div>

      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">الأحداث الأمنية</p>
              <p className="text-2xl font-bold text-red-600">
                {securityEvents.filter(e => e.status === 'open' || e.status === 'investigating').length}
              </p>
              <div className="flex items-center mt-2">
                <AlertTriangle className="w-4 h-4 text-red-500" />
                <span className="text-sm text-red-500 font-medium rtl-font">يحتاج انتباه</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
              <Shield className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">المستخدمين النشطين</p>
              <p className="text-2xl font-bold text-green-600">
                {accessControls.filter(ac => ac.status === 'active').length}
              </p>
              <div className="flex items-center mt-2">
                <UserCheck className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500 font-medium rtl-font">مصرح</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">السياسات المفعلة</p>
              <p className="text-2xl font-bold text-blue-600">
                {securityPolicies.filter(p => p.isEnabled).length}
              </p>
              <div className="flex items-center mt-2">
                <Lock className="w-4 h-4 text-blue-500" />
                <span className="text-sm text-blue-500 font-medium rtl-font">نشط</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Lock className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">مستوى الأمان</p>
              <p className="text-2xl font-bold text-purple-600">عالي</p>
              <div className="flex items-center mt-2">
                <Zap className="w-4 h-4 text-purple-500" />
                <span className="text-sm text-purple-500 font-medium rtl-font">محمي</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Shield className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityManagement;
