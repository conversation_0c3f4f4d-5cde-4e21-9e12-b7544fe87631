import React, { useState } from 'react';
import { MessageCircle, Phone, Mail, Send, User, MessageSquare, ArrowLeft, MapPin, Clock, Award, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Contact form submitted:', formData);
    alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
    setFormData({ name: '', email: '', phone: '', subject: '', message: '' });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const contactMethods = [
    {
      icon: <Phone className="w-8 h-8" />,
      title: 'اتصل بنا',
      value: '+212 5XX-XXXXXX',
      description: 'متاح 24/7 لخدمتك',
      action: () => window.open('tel:+2125XXXXXXX'),
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: <Mail className="w-8 h-8" />,
      title: 'راسلنا',
      value: '<EMAIL>',
      description: 'رد سريع خلال ساعات',
      action: () => window.open('mailto:<EMAIL>'),
      color: 'from-green-500 to-green-600'
    },
    {
      icon: <MessageCircle className="w-8 h-8" />,
      title: 'دردشة مباشرة',
      value: 'متاح الآن',
      description: 'دعم فوري ومباشر',
      action: () => alert('سيتم فتح الدردشة المباشرة...'),
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: <MapPin className="w-8 h-8" />,
      title: 'زيارة المكتب',
      value: 'الدار البيضاء، المغرب',
      description: 'المقر الرئيسي',
      action: () => alert('سيتم فتح الخريطة...'),
      color: 'from-orange-500 to-orange-600'
    }
  ];

  const faqs = [
    {
      question: "كم يستغرق الرد على الاستفسارات؟",
      answer: "نحن نرد على جميع الاستفسارات خلال 24 ساعة كحد أقصى، وعادة ما نرد خلال ساعات قليلة."
    },
    {
      question: "هل يمكنني تتبع حالة طلبي؟",
      answer: "نعم، يمكنك تتبع طلبك في الوقت الفعلي من خلال التطبيق أو الموقع الإلكتروني."
    },
    {
      question: "ما هي ساعات عمل خدمة العملاء؟",
      answer: "خدمة العملاء متاحة 24/7 لضمان حصولك على المساعدة في أي وقت تحتاجها."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-white shadow-lg sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link 
              to="/" 
              className="flex items-center space-x-3 rtl:space-x-reverse text-gray-700 hover:text-primary transition-colors"
            >
              <ArrowLeft className="w-6 h-6" />
              <span className="font-semibold rtl-font">العودة للرئيسية</span>
            </Link>
            
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center">
                <span className="text-white font-bold text-2xl">و</span>
              </div>
              <div>
                <span className="text-2xl font-bold text-accent">وصلتي</span>
                <div className="text-xs text-gray-500">Wasslti</div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary via-primary/95 to-primary/90 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-72 h-72 bg-white rounded-full blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <MessageCircle className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-5xl font-bold mb-6 rtl-font">تواصل معنا</h1>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto rtl-font">
            نحن هنا لمساعدتك في أي وقت. تواصل معنا عبر أي من الطرق التالية وسنكون سعداء لخدمتك
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold mb-1">24/7</div>
              <div className="text-sm opacity-80 rtl-font">دعم متواصل</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold mb-1">{"< 1h"}</div>
              <div className="text-sm opacity-80 rtl-font">وقت الاستجابة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold mb-1">98%</div>
              <div className="text-sm opacity-80 rtl-font">رضا العملاء</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold mb-1">1M+</div>
              <div className="text-sm opacity-80 rtl-font">استفسار محلول</div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-accent mb-6 rtl-font">طرق التواصل</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto rtl-font">
              اختر الطريقة الأنسب لك للتواصل معنا
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
            {contactMethods.map((method, index) => (
              <div key={index} className="group">
                <button
                  onClick={method.action}
                  className="w-full bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 text-center"
                >
                  <div className={`w-20 h-20 bg-gradient-to-br ${method.color} rounded-3xl flex items-center justify-center mx-auto mb-6 text-white shadow-xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500`}>
                    {method.icon}
                  </div>
                  <h3 className="text-xl font-bold text-accent mb-2 rtl-font">{method.title}</h3>
                  <p className="text-primary font-semibold mb-2">{method.value}</p>
                  <p className="text-gray-600 text-sm rtl-font">{method.description}</p>
                </button>
              </div>
            ))}
          </div>

          {/* Contact Form */}
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
              <div className="grid lg:grid-cols-2">
                {/* Form */}
                <div className="p-8 lg:p-12">
                  <h3 className="text-2xl font-bold text-accent mb-6 rtl-font">أرسل رسالة</h3>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="relative">
                      <User className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        placeholder="الاسم الكامل"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="w-full pr-12 pl-4 py-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors rtl-font"
                        required
                      />
                    </div>

                    <div className="relative">
                      <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="email"
                        placeholder="البريد الإلكتروني"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="w-full pr-12 pl-4 py-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors rtl-font"
                        required
                      />
                    </div>

                    <div className="relative">
                      <Phone className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="tel"
                        placeholder="رقم الهاتف"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="w-full pr-12 pl-4 py-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors rtl-font"
                      />
                    </div>

                    <div className="relative">
                      <MessageSquare className="absolute right-3 top-4 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        placeholder="موضوع الرسالة"
                        value={formData.subject}
                        onChange={(e) => handleInputChange('subject', e.target.value)}
                        className="w-full pr-12 pl-4 py-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors rtl-font"
                        required
                      />
                    </div>

                    <div className="relative">
                      <textarea
                        placeholder="اكتب رسالتك هنا..."
                        value={formData.message}
                        onChange={(e) => handleInputChange('message', e.target.value)}
                        rows={5}
                        className="w-full p-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors resize-none rtl-font"
                        required
                      />
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-primary to-primary/90 text-white py-4 rounded-2xl font-bold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse"
                    >
                      <Send className="w-5 h-5" />
                      <span>إرسال الرسالة</span>
                    </button>
                  </form>
                </div>

                {/* Info */}
                <div className="bg-gradient-to-br from-primary/5 to-primary/10 p-8 lg:p-12">
                  <h3 className="text-2xl font-bold text-accent mb-6 rtl-font">معلومات إضافية</h3>
                  
                  <div className="space-y-6 mb-8">
                    <div className="flex items-start space-x-4 rtl:space-x-reverse">
                      <div className="w-12 h-12 bg-primary/20 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Clock className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-bold text-accent mb-1 rtl-font">ساعات العمل</h4>
                        <p className="text-gray-600 rtl-font">24/7 - نحن متاحون دائماً</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4 rtl:space-x-reverse">
                      <div className="w-12 h-12 bg-primary/20 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Award className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-bold text-accent mb-1 rtl-font">جودة الخدمة</h4>
                        <p className="text-gray-600 rtl-font">فريق محترف ومدرب</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4 rtl:space-x-reverse">
                      <div className="w-12 h-12 bg-primary/20 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Shield className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-bold text-accent mb-1 rtl-font">الخصوصية</h4>
                        <p className="text-gray-600 rtl-font">بياناتك محمية بالكامل</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white/50 rounded-2xl p-6">
                    <h4 className="font-bold text-accent mb-4 rtl-font">الأسئلة الشائعة</h4>
                    <div className="space-y-4">
                      {faqs.map((faq, index) => (
                        <div key={index}>
                          <h5 className="font-semibold text-accent text-sm mb-1 rtl-font">{faq.question}</h5>
                          <p className="text-gray-600 text-xs rtl-font">{faq.answer}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-accent text-white py-12">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center space-x-3 rtl:space-x-reverse mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center">
              <span className="text-white font-bold text-2xl">و</span>
            </div>
            <div>
              <span className="text-2xl font-bold">وصلتي</span>
              <div className="text-sm text-gray-400">Wasslti</div>
            </div>
          </div>
          <p className="text-gray-300 rtl-font">
            &copy; 2024 وصلتي. جميع الحقوق محفوظة.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default ContactPage;