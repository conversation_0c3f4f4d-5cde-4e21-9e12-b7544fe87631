import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/auth_provider.dart';
import 'providers/language_provider.dart';
import 'providers/theme_provider.dart';
import 'screens/themes/theme_settings_screen.dart';

/// Test Themes application
void main() {
  runApp(const TestThemesApp());
}

class TestThemesApp extends StatelessWidget {
  const TestThemesApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Test Themes',
            debugShowCheckedModeBanner: false,
            theme: themeProvider.getThemeData(Brightness.light),
            darkTheme: themeProvider.getThemeData(Brightness.dark),
            themeMode: themeProvider.effectiveThemeMode,
            home: const TestThemesHome(),
          );
        },
      ),
    );
  }

  Widget _buildQuickThemeSwitcher(ThemeProvider themeProvider, LanguageProvider languageProvider, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageProvider.getText('Quick Theme Switch', 'تبديل سريع للمظهر'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: DriverThemeMode.values.map((theme) {
              final isSelected = themeProvider.currentTheme == theme;
              return GestureDetector(
                onTap: () => themeProvider.changeTheme(theme),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF11B96F)
                        : (isDark ? Colors.grey[800] : Colors.grey[200]),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF11B96F)
                          : (isDark ? Colors.grey[600]! : Colors.grey[400]!),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        themeProvider.getThemeIcon(theme),
                        size: 16,
                        color: isSelected
                            ? Colors.white
                            : (isDark ? Colors.grey[400] : Colors.grey[600]),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        themeProvider.getThemeName(theme),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: isSelected
                              ? Colors.white
                              : (isDark ? Colors.grey[400] : Colors.grey[600]),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildUIComponentsPreview(LanguageProvider languageProvider, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageProvider.getText('UI Components Preview', 'معاينة مكونات الواجهة'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          // Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {},
                  child: Text(languageProvider.getText('Primary', 'أساسي')),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton(
                  onPressed: () {},
                  child: Text(languageProvider.getText('Outlined', 'محدد')),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Text Field
          TextField(
            decoration: InputDecoration(
              labelText: languageProvider.getText('Sample Input', 'إدخال تجريبي'),
              hintText: languageProvider.getText('Enter text here', 'أدخل النص هنا'),
              prefixIcon: const Icon(Icons.search),
            ),
          ),

          const SizedBox(height: 16),

          // Switch and Checkbox
          Row(
            children: [
              Switch(
                value: true,
                onChanged: (value) {},
              ),
              const SizedBox(width: 16),
              Checkbox(
                value: true,
                onChanged: (value) {},
              ),
              const SizedBox(width: 16),
              Text(
                languageProvider.getText('Sample Controls', 'عناصر تحكم تجريبية'),
                style: TextStyle(
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const CircleAvatar(
                    backgroundColor: Color(0xFF11B96F),
                    child: Icon(Icons.person, color: Colors.white),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          languageProvider.getText('Sample Card', 'بطاقة تجريبية'),
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          languageProvider.getText('Card description', 'وصف البطاقة'),
                          style: TextStyle(
                            color: isDark ? Colors.grey[400] : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Color(0xFF11B96F),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Progress Indicator
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                languageProvider.getText('Loading Progress', 'تقدم التحميل'),
                style: TextStyle(
                  fontSize: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              const LinearProgressIndicator(
                value: 0.7,
                backgroundColor: Colors.grey,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF11B96F)),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class TestThemesHome extends StatefulWidget {
  const TestThemesHome({super.key});

  @override
  State<TestThemesHome> createState() => _TestThemesHomeState();
}

class _TestThemesHomeState extends State<TestThemesHome> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LanguageProvider>().initializeLanguage();
      context.read<ThemeProvider>().initializeTheme();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();
    final themeProvider = context.watch<ThemeProvider>();

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: Text(
          languageProvider.getText('Theme System Test', 'اختبار نظام المظاهر'),
        ),
        backgroundColor: const Color(0xFF11B96F),
        foregroundColor: Colors.white,
        actions: [
          // Language Toggle
          TextButton(
            onPressed: () {
              final newLang = languageProvider.isEnglish ? 'العربية' : 'English';
              languageProvider.changeLanguage(newLang);
            },
            child: Text(
              languageProvider.isEnglish ? 'العربية' : 'English',
              style: const TextStyle(color: Colors.white),
            ),
          ),
          // Theme Settings
          IconButton(
            icon: const Icon(Icons.palette),
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const ThemeSettingsScreen(),
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        themeProvider.getThemeIcon(themeProvider.currentTheme),
                        size: 40,
                        color: const Color(0xFF11B96F),
                      ),
                      const SizedBox(width: 16),
                      const Icon(
                        Icons.color_lens,
                        size: 40,
                        color: Colors.purple,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    languageProvider.getText(
                      'Advanced Theme System',
                      'نظام المظاهر المتقدم'
                    ),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    languageProvider.getText(
                      'Test all theme features and customizations',
                      'اختبر جميع ميزات المظاهر والتخصيصات'
                    ),
                    style: TextStyle(
                      fontSize: 16,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Current Theme Info
            _buildCurrentThemeInfo(themeProvider, languageProvider, isDark),

            const SizedBox(height: 20),

            // Quick Theme Switcher
            _buildQuickThemeSwitcher(themeProvider, languageProvider, isDark),

            const SizedBox(height: 20),

            // UI Components Preview
            _buildUIComponentsPreview(languageProvider, isDark),

            const SizedBox(height: 20),

            // Theme Settings Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ThemeSettingsScreen(),
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF11B96F),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                icon: const Icon(Icons.settings),
                label: Text(
                  languageProvider.getText('Open Theme Settings', 'فتح إعدادات المظهر'),
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentThemeInfo(ThemeProvider themeProvider, LanguageProvider languageProvider, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF11B96F).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                themeProvider.getThemeIcon(themeProvider.currentTheme),
                color: const Color(0xFF11B96F),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                languageProvider.getText('Current Theme', 'المظهر الحالي'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF11B96F),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageProvider.getText('Name:', 'الاسم:'),
                style: TextStyle(
                  fontSize: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
              Text(
                themeProvider.getThemeName(themeProvider.currentTheme),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageProvider.getText('Description:', 'الوصف:'),
                style: TextStyle(
                  fontSize: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
              Expanded(
                child: Text(
                  themeProvider.getThemeDescription(themeProvider.currentTheme),
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? Colors.grey[300] : Colors.grey[700],
                  ),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageProvider.getText('Mode:', 'الوضع:'),
                style: TextStyle(
                  fontSize: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
              Text(
                isDark ? 'Dark' : 'Light',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickThemeSwitcher(ThemeProvider themeProvider, LanguageProvider languageProvider, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageProvider.getText('Quick Theme Switch', 'تبديل سريع للمظهر'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: DriverThemeMode.values.map((theme) {
              final isSelected = themeProvider.currentTheme == theme;
              return GestureDetector(
                onTap: () => themeProvider.changeTheme(theme),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF11B96F)
                        : (isDark ? Colors.grey[800] : Colors.grey[200]),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF11B96F)
                          : (isDark ? Colors.grey[600]! : Colors.grey[400]!),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        themeProvider.getThemeIcon(theme),
                        size: 14,
                        color: isSelected
                            ? Colors.white
                            : (isDark ? Colors.grey[400] : Colors.grey[600]),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        themeProvider.getThemeName(theme),
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          color: isSelected
                              ? Colors.white
                              : (isDark ? Colors.grey[400] : Colors.grey[600]),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildUIComponentsPreview(LanguageProvider languageProvider, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageProvider.getText('UI Components Preview', 'معاينة مكونات الواجهة'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          // Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {},
                  child: Text(languageProvider.getText('Primary', 'أساسي')),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton(
                  onPressed: () {},
                  child: Text(languageProvider.getText('Outlined', 'محدد')),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Text Field
          TextField(
            decoration: InputDecoration(
              labelText: languageProvider.getText('Sample Input', 'إدخال تجريبي'),
              hintText: languageProvider.getText('Enter text here', 'أدخل النص هنا'),
              prefixIcon: const Icon(Icons.search),
            ),
          ),

          const SizedBox(height: 16),

          // Switch and Checkbox
          Row(
            children: [
              Switch(
                value: true,
                onChanged: (value) {},
              ),
              const SizedBox(width: 16),
              Checkbox(
                value: true,
                onChanged: (value) {},
              ),
              const SizedBox(width: 16),
              Text(
                languageProvider.getText('Sample Controls', 'عناصر تحكم تجريبية'),
                style: TextStyle(
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const CircleAvatar(
                    backgroundColor: Color(0xFF11B96F),
                    child: Icon(Icons.person, color: Colors.white),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          languageProvider.getText('Sample Card', 'بطاقة تجريبية'),
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          languageProvider.getText('Card description', 'وصف البطاقة'),
                          style: TextStyle(
                            color: isDark ? Colors.grey[400] : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Color(0xFF11B96F),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Progress Indicator
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                languageProvider.getText('Loading Progress', 'تقدم التحميل'),
                style: TextStyle(
                  fontSize: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              const LinearProgressIndicator(
                value: 0.7,
                backgroundColor: Colors.grey,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF11B96F)),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
