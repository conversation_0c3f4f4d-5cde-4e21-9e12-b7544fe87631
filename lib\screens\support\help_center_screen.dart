import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';
import 'faq_categories_screen.dart';
import 'ticket_system_screen.dart';
import 'live_chat_screen.dart';

/// Advanced Help Center with categorized support
class HelpCenterScreen extends StatefulWidget {
  const HelpCenterScreen({super.key});

  @override
  State<HelpCenterScreen> createState() => _HelpCenterScreenState();
}

class _HelpCenterScreenState extends State<HelpCenterScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Help Center', 'مركز المساعدة'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header with search
              _buildHeader(isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // Quick Actions
              _buildQuickActions(isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // Help Categories
              _buildHelpCategories(isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // Popular Articles
              _buildPopularArticles(isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // Contact Options
              _buildContactOptions(isDark, languageProvider),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF11B96F),
            const Color(0xFF11B96F).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF11B96F).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.help_center,
            size: 60,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('How can we help you?', 'كيف يمكننا مساعدتك؟'),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Search for answers or browse help topics',
              'ابحث عن الإجابات أو تصفح مواضيع المساعدة'
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              onChanged: (value) => setState(() => _searchQuery = value),
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
              decoration: InputDecoration(
                hintText: languageProvider.getText(
                  'Search help articles...',
                  'ابحث في مقالات المساعدة...'
                ),
                hintStyle: TextStyle(color: Colors.grey[500]),
                prefixIcon: const Icon(Icons.search, color: Color(0xFF11B96F)),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear, color: Colors.grey),
                        onPressed: () {
                          _searchController.clear();
                          setState(() => _searchQuery = '');
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(bool isDark, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageProvider.getText('Quick Actions', 'إجراءات سريعة'),
          style: TextStyle(
            fontSize: DriverTypography.titleMedium,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.chat_bubble_outline,
                title: languageProvider.getText('Live Chat', 'محادثة مباشرة'),
                subtitle: languageProvider.getText('Chat with support', 'تحدث مع الدعم'),
                color: const Color(0xFF11B96F),
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const LiveChatScreen()),
                ),
                isDark: isDark,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                icon: Icons.confirmation_number_outlined,
                title: languageProvider.getText('My Tickets', 'تذاكري'),
                subtitle: languageProvider.getText('Track requests', 'تتبع الطلبات'),
                color: Colors.blue,
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const TicketSystemScreen()),
                ),
                isDark: isDark,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpCategories(bool isDark, LanguageProvider languageProvider) {
    final categories = [
      {
        'icon': Icons.account_circle_outlined,
        'title': languageProvider.getText('Account & Profile', 'الحساب والملف الشخصي'),
        'description': languageProvider.getText('Manage your account settings', 'إدارة إعدادات حسابك'),
        'color': const Color(0xFF11B96F),
        'articles': 12,
      },
      {
        'icon': Icons.directions_car_outlined,
        'title': languageProvider.getText('Driving & Trips', 'القيادة والرحلات'),
        'description': languageProvider.getText('Trip management and driving tips', 'إدارة الرحلات ونصائح القيادة'),
        'color': Colors.blue,
        'articles': 18,
      },
      {
        'icon': Icons.payment_outlined,
        'title': languageProvider.getText('Payments & Earnings', 'المدفوعات والأرباح'),
        'description': languageProvider.getText('Payment methods and earnings', 'طرق الدفع والأرباح'),
        'color': Colors.orange,
        'articles': 8,
      },
      {
        'icon': Icons.description_outlined,
        'title': languageProvider.getText('Documents & Verification', 'الوثائق والتحقق'),
        'description': languageProvider.getText('Document upload and verification', 'رفع الوثائق والتحقق'),
        'color': Colors.purple,
        'articles': 6,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageProvider.getText('Help Categories', 'فئات المساعدة'),
          style: TextStyle(
            fontSize: DriverTypography.titleMedium,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.1,
          ),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return _buildCategoryCard(category, isDark, languageProvider);
          },
        ),
      ],
    );
  }

  Widget _buildCategoryCard(Map<String, dynamic> category, bool isDark, LanguageProvider languageProvider) {
    return GestureDetector(
      onTap: () => Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => FAQCategoriesScreen(
            categoryTitle: category['title'],
            categoryIcon: category['icon'],
            categoryColor: category['color'],
          ),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: (category['color'] as Color).withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: (category['color'] as Color).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                category['icon'],
                color: category['color'],
                size: 20,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              category['title'],
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              category['description'],
              style: TextStyle(
                fontSize: 12,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const Spacer(),
            Row(
              children: [
                Text(
                  '${category['articles']} ${languageProvider.getText('articles', 'مقالة')}',
                  style: TextStyle(
                    fontSize: 11,
                    color: category['color'],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 12,
                  color: category['color'],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularArticles(bool isDark, LanguageProvider languageProvider) {
    final articles = [
      languageProvider.getText('How to update my profile information?', 'كيف أحدث معلومات ملفي الشخصي؟'),
      languageProvider.getText('How to upload required documents?', 'كيف أرفع الوثائق المطلوبة؟'),
      languageProvider.getText('How to track my earnings?', 'كيف أتتبع أرباحي؟'),
      languageProvider.getText('How to contact emergency support?', 'كيف أتواصل مع الدعم الطارئ؟'),
      languageProvider.getText('How to change my password?', 'كيف أغير كلمة المرور؟'),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageProvider.getText('Popular Articles', 'المقالات الشائعة'),
          style: TextStyle(
            fontSize: DriverTypography.titleMedium,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: articles.length,
            separatorBuilder: (context, index) => Divider(
              color: isDark ? Colors.grey[700] : Colors.grey[300],
              height: 1,
            ),
            itemBuilder: (context, index) {
              return ListTile(
                contentPadding: EdgeInsets.zero,
                leading: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF11B96F),
                      ),
                    ),
                  ),
                ),
                title: Text(
                  articles[index],
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
                onTap: () => _showArticleDetail(articles[index]),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContactOptions(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageProvider.getText('Still need help?', 'ما زلت تحتاج مساعدة؟'),
            style: TextStyle(
              fontSize: DriverTypography.titleMedium,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Our support team is available 24/7 to assist you',
              'فريق الدعم متاح على مدار الساعة لمساعدتك'
            ),
            style: TextStyle(
              fontSize: 14,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => Navigator.of(context).push(
                    MaterialPageRoute(builder: (context) => const LiveChatScreen()),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF11B96F),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  icon: const Icon(Icons.chat, size: 18),
                  label: Text(
                    languageProvider.getText('Start Chat', 'بدء المحادثة'),
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _handleCallSupport(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF11B96F),
                    side: const BorderSide(color: Color(0xFF11B96F)),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  icon: const Icon(Icons.phone, size: 18),
                  label: Text(
                    languageProvider.getText('Call Us', 'اتصل بنا'),
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showArticleDetail(String title) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          final isDark = Theme.of(context).brightness == Brightness.dark;
          final languageProvider = context.read<LanguageProvider>();

          return Container(
            decoration: BoxDecoration(
              color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[400],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: isDark ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 20),
                        Text(
                          languageProvider.getText(
                            'This is a detailed explanation of the help article. In a real implementation, this would contain comprehensive information to help users solve their problems.',
                            'هذا شرح مفصل لمقال المساعدة. في التطبيق الحقيقي، سيحتوي هذا على معلومات شاملة لمساعدة المستخدمين في حل مشاكلهم.'
                          ),
                          style: TextStyle(
                            fontSize: 16,
                            height: 1.6,
                            color: isDark ? Colors.grey[300] : Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _handleCallSupport() {
    final languageProvider = context.read<LanguageProvider>();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.getText('Calling support...', 'جاري الاتصال بالدعم...')
        ),
        backgroundColor: const Color(0xFF11B96F),
      ),
    );
  }
}
