import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/notification_card_widget.dart';
import '../widgets/order_details_modal.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  String searchQuery = '';
  String selectedFilter = 'All';

  final List<String> filters = ['All', 'Notifications', 'Activity'];

  final List<NotificationItem> notifications = [
    NotificationItem(
      id: '1',
      title: 'Order #1',
      subtitle: 'Order Accepted to restaurant',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
      time: 'Apr 16, 2023, 2:23',
      type: NotificationType.orderAccepted,
      isRead: false,
      orderDetails: OrderDetails(
        orderId: '#1',
        customerName: '<PERSON>',
        items: ['Sliced lamb', 'Mongolian beef'],
        total: 11.18,
        status: 'Accepted',
      ),
    ),
    NotificationItem(
      id: '2',
      title: 'Order #2',
      subtitle: 'Order Accepted to restaurant',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
      time: 'Apr 16, 2023, 2:23',
      type: NotificationType.orderAccepted,
      isRead: false,
      orderDetails: OrderDetails(
        orderId: '#2',
        customerName: 'Jane Doe',
        items: ['Sliced lamb', 'Mongolian beef'],
        total: 11.18,
        status: 'Accepted',
      ),
    ),
    NotificationItem(
      id: '3',
      title: 'Order #3',
      subtitle: 'Order Accepted to restaurant',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
      time: 'Apr 16, 2023, 2:23',
      type: NotificationType.orderRejected,
      isRead: true,
      orderDetails: OrderDetails(
        orderId: '#3',
        customerName: 'Mike Johnson',
        items: ['Mongolian beef'],
        total: 5.59,
        status: 'Rejected',
      ),
    ),
    NotificationItem(
      id: '4',
      title: 'Order #4',
      subtitle: 'Order Accepted to restaurant',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
      time: 'Apr 18, 2023, 2:23',
      type: NotificationType.orderDelivered,
      isRead: false,
      orderDetails: OrderDetails(
        orderId: '#4',
        customerName: 'Sarah Wilson',
        items: ['Sliced lamb', 'Mongolian beef'],
        total: 11.18,
        status: 'Delivered',
      ),
    ),
  ];

  final List<NotificationItem> yesterdayNotifications = [
    NotificationItem(
      id: '5',
      title: 'Order #1',
      subtitle: 'Order Accepted to restaurant',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
      time: 'Apr 15, 2023, 2:23',
      type: NotificationType.orderAccepted,
      isRead: true,
      orderDetails: OrderDetails(
        orderId: '#1',
        customerName: 'Tom Brown',
        items: ['Sliced lamb', 'Mongolian beef'],
        total: 11.18,
        status: 'Accepted',
      ),
    ),
    NotificationItem(
      id: '6',
      title: 'Order #2',
      subtitle: 'Order Accepted to restaurant',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
      time: 'Apr 15, 2023, 2:23',
      type: NotificationType.orderAccepted,
      isRead: true,
      orderDetails: OrderDetails(
        orderId: '#2',
        customerName: 'Lisa Davis',
        items: ['Sliced lamb'],
        total: 5.59,
        status: 'Accepted',
      ),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: Container(
                    color: AppColors.backgroundLight,
                    child: Column(
                      children: [
                        // Filters
                        _buildFilters(),
                        
                        // Notifications List
                        Expanded(
                          child: _buildNotificationsList(),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Text(
                'Notifications',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 8),
              const Text('😊', style: TextStyle(fontSize: 20)),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Notifications
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.notifications_outlined,
                    color: AppColors.textSecondary,
                    size: 20,
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // User Profile
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primary,
                child: Text(
                  'JS',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Jhon Smith',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'User',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          ...filters.map((filter) {
            final isSelected = selectedFilter == filter;
            return Container(
              margin: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(
                  filter,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? AppColors.white : AppColors.textSecondary,
                  ),
                ),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    selectedFilter = filter;
                  });
                },
                backgroundColor: AppColors.white,
                selectedColor: AppColors.primary,
                checkmarkColor: AppColors.white,
                side: BorderSide(
                  color: isSelected ? AppColors.primary : AppColors.inputBorder,
                ),
              ),
            );
          }),
          
          const Spacer(),
          
          // Today Filter
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: AppColors.inputBorder),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Today',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(width: 4),
                const Icon(
                  Icons.keyboard_arrow_down,
                  color: AppColors.textSecondary,
                  size: 16,
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 8),
          
          // More Options
          IconButton(
            onPressed: () {},
            icon: const Icon(
              Icons.more_horiz,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Today Section
          _buildSectionHeader('Today'),
          const SizedBox(height: 8),
          ...notifications.map((notification) => NotificationCardWidget(
            notification: notification,
            onTap: () => _showOrderDetails(notification.orderDetails),
            onAccept: () => _handleOrderAction(notification, 'accept'),
            onReject: () => _handleOrderAction(notification, 'reject'),
          )),
          
          const SizedBox(height: 24),
          
          // Yesterday Section
          _buildSectionHeader('Yesterday'),
          const SizedBox(height: 8),
          ...yesterdayNotifications.map((notification) => NotificationCardWidget(
            notification: notification,
            onTap: () => _showOrderDetails(notification.orderDetails),
            onAccept: () => _handleOrderAction(notification, 'accept'),
            onReject: () => _handleOrderAction(notification, 'reject'),
          )),
          
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
      ),
    );
  }

  void _showOrderDetails(OrderDetails orderDetails) {
    showDialog(
      context: context,
      builder: (context) => OrderDetailsModal(orderDetails: orderDetails),
    );
  }

  void _handleOrderAction(NotificationItem notification, String action) {
    // Handle order accept/reject logic here
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Order ${notification.orderDetails.orderId} ${action}ed'),
        backgroundColor: action == 'accept' ? AppColors.success : AppColors.error,
      ),
    );
  }
}

enum NotificationType { orderAccepted, orderRejected, orderDelivered, orderPending }

class NotificationItem {
  final String id;
  final String title;
  final String subtitle;
  final String description;
  final String time;
  final NotificationType type;
  final bool isRead;
  final OrderDetails orderDetails;

  NotificationItem({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.time,
    required this.type,
    required this.isRead,
    required this.orderDetails,
  });
}

class OrderDetails {
  final String orderId;
  final String customerName;
  final List<String> items;
  final double total;
  final String status;

  OrderDetails({
    required this.orderId,
    required this.customerName,
    required this.items,
    required this.total,
    required this.status,
  });
}
