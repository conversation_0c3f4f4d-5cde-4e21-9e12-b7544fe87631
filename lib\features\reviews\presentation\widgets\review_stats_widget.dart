import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../screens/reviews_screen.dart';

class ReviewStatsWidget extends StatelessWidget {
  final List<Review> reviews;

  const ReviewStatsWidget({
    super.key,
    required this.reviews,
  });

  @override
  Widget build(BuildContext context) {
    final stats = _calculateStats();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Review Statistics',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Overall Rating
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.backgroundLight,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.inputBorder,
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  'Overall Rating',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      stats.averageRating.toStringAsFixed(1),
                      style: GoogleFonts.inter(
                        fontSize: 32,
                        fontWeight: FontWeight.w700,
                        color: AppColors.warning,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.star,
                      color: AppColors.warning,
                      size: 24,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'Based on ${reviews.length} reviews',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Rating Breakdown
          Text(
            'Rating Breakdown',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 12),
          
          ...List.generate(5, (index) {
            final rating = 5 - index;
            final count = stats.ratingCounts[rating] ?? 0;
            final percentage = reviews.isNotEmpty ? (count / reviews.length) * 100 : 0.0;
            
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Text(
                    '$rating',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.star,
                    color: AppColors.warning,
                    size: 12,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Container(
                      height: 6,
                      decoration: BoxDecoration(
                        color: AppColors.greyLight,
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: percentage / 100,
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.warning,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '$count',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          }),
          
          const SizedBox(height: 20),
          
          // Quick Stats
          Text(
            'Quick Stats',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 12),
          
          _buildStatCard(
            'Total Reviews',
            '${reviews.length}',
            Icons.rate_review,
            AppColors.primary,
          ),
          
          const SizedBox(height: 8),
          
          _buildStatCard(
            'Verified Purchases',
            '${stats.verifiedCount}',
            Icons.verified,
            AppColors.success,
          ),
          
          const SizedBox(height: 8),
          
          _buildStatCard(
            'Replied Reviews',
            '${stats.repliedCount}',
            Icons.reply,
            AppColors.info,
          ),
          
          const SizedBox(height: 8),
          
          _buildStatCard(
            'Avg Response Time',
            '${stats.averageResponseTime}h',
            Icons.schedule,
            AppColors.warning,
          ),
          
          const SizedBox(height: 20),
          
          // Recent Activity
          Text(
            'Recent Activity',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 12),
          
          ...reviews.take(3).map((review) => Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.backgroundLight,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.inputBorder,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        review.customerName,
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                    Row(
                      children: List.generate(review.rating, (index) => Icon(
                        Icons.star,
                        color: AppColors.warning,
                        size: 12,
                      )),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  review.comment,
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.inputBorder,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  ReviewStats _calculateStats() {
    if (reviews.isEmpty) {
      return ReviewStats(
        averageRating: 0.0,
        ratingCounts: {},
        verifiedCount: 0,
        repliedCount: 0,
        averageResponseTime: 0,
      );
    }

    final totalRating = reviews.fold(0, (sum, review) => sum + review.rating);
    final averageRating = totalRating / reviews.length;

    final ratingCounts = <int, int>{};
    for (final review in reviews) {
      ratingCounts[review.rating] = (ratingCounts[review.rating] ?? 0) + 1;
    }

    final verifiedCount = reviews.where((review) => review.isVerifiedPurchase).length;
    final repliedCount = reviews.where((review) => review.restaurantReply != null).length;

    // Calculate average response time (simplified)
    final repliedReviews = reviews.where((review) => review.restaurantReply != null);
    double averageResponseTime = 0;
    if (repliedReviews.isNotEmpty) {
      final totalResponseTime = repliedReviews.fold(0.0, (sum, review) {
        final responseTime = review.restaurantReply!.date.difference(review.date).inHours;
        return sum + responseTime;
      });
      averageResponseTime = totalResponseTime / repliedReviews.length;
    }

    return ReviewStats(
      averageRating: averageRating,
      ratingCounts: ratingCounts,
      verifiedCount: verifiedCount,
      repliedCount: repliedCount,
      averageResponseTime: averageResponseTime.round(),
    );
  }
}

class ReviewStats {
  final double averageRating;
  final Map<int, int> ratingCounts;
  final int verifiedCount;
  final int repliedCount;
  final int averageResponseTime;

  ReviewStats({
    required this.averageRating,
    required this.ratingCounts,
    required this.verifiedCount,
    required this.repliedCount,
    required this.averageResponseTime,
  });
}
