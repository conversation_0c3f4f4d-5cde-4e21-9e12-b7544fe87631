import 'package:flutter/material.dart';
import '../constants/driver_themes.dart';
import '../screens/auth/otp_verification_screen.dart';

/// تطبيق تجريبي لعرض صفحة تأكيد رقم الهاتف (OTP)
/// مع إمكانية التبديل بين الأوضاع المختلفة
class OtpDemoApp extends StatefulWidget {
  const OtpDemoApp({super.key});

  @override
  State<OtpDemoApp> createState() => _OtpDemoAppState();
}

class _OtpDemoAppState extends State<OtpDemoApp> {
  ThemeMode _themeMode = ThemeMode.system;
  bool _isNightDriving = false;
  String _selectedPhoneNumber = '+212 6XXXXXXXX';

  final List<String> _phoneNumbers = [
    '+212 6XXXXXXXX',
    '+966 5XXXXXXXX',
    '+971 5XXXXXXXX',
    '+1 555XXXXXXX',
  ];

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wasslti Partner - OTP Demo',
      debugShowCheckedModeBanner: false,
      
      // استخدام نظام الثيمات المخصص
      theme: DriverThemes.getTheme(ThemeMode.light, isNightDriving: _isNightDriving),
      darkTheme: DriverThemes.getTheme(ThemeMode.dark, isNightDriving: _isNightDriving),
      themeMode: _themeMode,
      
      home: Scaffold(
        appBar: AppBar(
          title: const Text('OTP Verification Demo'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: [
            // اختيار رقم الهاتف
            PopupMenuButton<String>(
              icon: const Icon(Icons.phone),
              onSelected: (value) {
                setState(() {
                  _selectedPhoneNumber = value;
                });
              },
              itemBuilder: (context) => _phoneNumbers.map((phone) {
                return PopupMenuItem(
                  value: phone,
                  child: Row(
                    children: [
                      Icon(
                        Icons.phone,
                        size: 16,
                        color: _selectedPhoneNumber == phone 
                            ? const Color(0xFF11B96F) 
                            : null,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        phone,
                        style: TextStyle(
                          color: _selectedPhoneNumber == phone 
                              ? const Color(0xFF11B96F) 
                              : null,
                          fontWeight: _selectedPhoneNumber == phone 
                              ? FontWeight.bold 
                              : null,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
            
            // زر تبديل الوضع
            PopupMenuButton<String>(
              icon: const Icon(Icons.palette),
              onSelected: (value) {
                setState(() {
                  switch (value) {
                    case 'light':
                      _themeMode = ThemeMode.light;
                      _isNightDriving = false;
                      break;
                    case 'dark':
                      _themeMode = ThemeMode.dark;
                      _isNightDriving = false;
                      break;
                    case 'system':
                      _themeMode = ThemeMode.system;
                      _isNightDriving = false;
                      break;
                    case 'night':
                      _themeMode = ThemeMode.dark;
                      _isNightDriving = true;
                      break;
                  }
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'light',
                  child: Row(
                    children: [
                      Icon(Icons.light_mode, size: 20),
                      SizedBox(width: 8),
                      Text('Light Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'dark',
                  child: Row(
                    children: [
                      Icon(Icons.dark_mode, size: 20),
                      SizedBox(width: 8),
                      Text('Dark Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'system',
                  child: Row(
                    children: [
                      Icon(Icons.settings, size: 20),
                      SizedBox(width: 8),
                      Text('System'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'night',
                  child: Row(
                    children: [
                      Icon(Icons.nights_stay, size: 20),
                      SizedBox(width: 8),
                      Text('Night Driving'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: OtpVerificationScreen(
          phoneNumber: _selectedPhoneNumber,
        ),
        
        // معلومات الوضع الحالي
        bottomNavigationBar: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // معلومات الوضع
              Row(
                children: [
                  Icon(
                    _getCurrentModeIcon(),
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getCurrentModeText(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
              
              // رقم الهاتف المحدد
              Row(
                children: [
                  Icon(
                    Icons.phone,
                    size: 16,
                    color: const Color(0xFF11B96F),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _selectedPhoneNumber,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: const Color(0xFF11B96F),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCurrentModeIcon() {
    if (_isNightDriving) return Icons.nights_stay;
    
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.settings;
    }
  }

  String _getCurrentModeText() {
    if (_isNightDriving) return 'Night Driving Mode';
    
    switch (_themeMode) {
      case ThemeMode.light:
        return 'Light Mode';
      case ThemeMode.dark:
        return 'Dark Mode';
      case ThemeMode.system:
        return 'System Mode';
    }
  }
}

/// دالة لتشغيل التطبيق التجريبي
void runOtpDemo() {
  runApp(const OtpDemoApp());
}

/// مثال على الاستخدام:
/// 
/// ```dart
/// // في main.dart أو أي ملف آخر
/// import 'demo/otp_demo.dart';
/// 
/// void main() {
///   runOtpDemo();
/// }
/// ```
