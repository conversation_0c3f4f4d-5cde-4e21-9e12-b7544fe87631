import 'package:flutter/material.dart';

/// حالات السائق المختلفة
enum DriverStatus {
  /// شغال - متاح لاستقبال الطلبات
  working,
  
  /// يرتاح - في استراحة مؤقتة
  resting,
  
  /// متوقف - غير متاح
  stopped,
}

/// امتداد لحالات السائق لإضافة الخصائص
extension DriverStatusExtension on DriverStatus {
  /// النص المعروض للحالة
  String get displayText {
    switch (this) {
      case DriverStatus.working:
        return 'Working';
      case DriverStatus.resting:
        return 'Resting';
      case DriverStatus.stopped:
        return 'Stopped';
    }
  }
  
  /// النص المعروض للحالة بالإنجليزية
  String get displayTextEn {
    switch (this) {
      case DriverStatus.working:
        return 'Working';
      case DriverStatus.resting:
        return 'Resting';
      case DriverStatus.stopped:
        return 'Stopped';
    }
  }
  
  /// لون الحالة
  Color get color {
    switch (this) {
      case DriverStatus.working:
        return const Color(0xFF11B96F); // أخضر
      case DriverStatus.resting:
        return const Color(0xFFFF9500); // أصفر مائل للبرتقالي
      case DriverStatus.stopped:
        return const Color(0xFFFF3B30); // أحمر
    }
  }
  
  /// لون الخلفية للحالة
  Color get backgroundColor {
    switch (this) {
      case DriverStatus.working:
        return const Color(0xFF11B96F).withValues(alpha: 0.1);
      case DriverStatus.resting:
        return const Color(0xFFFF9500).withValues(alpha: 0.1);
      case DriverStatus.stopped:
        return const Color(0xFFFF3B30).withValues(alpha: 0.1);
    }
  }
  
  /// أيقونة الحالة
  IconData get icon {
    switch (this) {
      case DriverStatus.working:
        return Icons.work;
      case DriverStatus.resting:
        return Icons.pause_circle;
      case DriverStatus.stopped:
        return Icons.stop_circle;
    }
  }
  
  /// الحالة التالية عند النقر
  DriverStatus get nextStatus {
    switch (this) {
      case DriverStatus.working:
        return DriverStatus.resting;
      case DriverStatus.resting:
        return DriverStatus.stopped;
      case DriverStatus.stopped:
        return DriverStatus.working;
    }
  }
}
