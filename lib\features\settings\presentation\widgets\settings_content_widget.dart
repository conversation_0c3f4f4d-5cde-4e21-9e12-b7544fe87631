import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import 'account_settings_widget.dart';
import 'notification_settings_widget.dart';
import 'security_settings_widget.dart';
import 'payment_settings_widget.dart';
import 'help_settings_widget.dart';
import 'privacy_policy_widget.dart';

class SettingsContentWidget extends StatelessWidget {
  final String selectedSection;

  const SettingsContentWidget({
    super.key,
    required this.selectedSection,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildContent(),
    );
  }

  Widget _buildContent() {
    switch (selectedSection) {
      case 'Account':
        return const AccountSettingsWidget();
      case 'Notification':
        return const NotificationSettingsWidget();
      case 'Security':
        return const SecuritySettingsWidget();
      case 'Payment':
        return const PaymentSettingsWidget();
      case 'Help':
        return const HelpSettingsWidget();
      case 'Privacy Policy':
        return const PrivacyPolicyWidget();
      default:
        return const AccountSettingsWidget();
    }
  }
}
