import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LanguageProvider } from './contexts/LanguageContext';
import Header from './components/Header';
import Hero from './components/Hero';
import AboutSection from './components/AboutSection';
import HowItWorks from './components/HowItWorks';
import FeaturedRestaurants from './components/FeaturedRestaurants';
import CitiesSection from './components/CitiesSection';
import Testimonials from './components/Testimonials';
import AppDownload from './components/AppDownload';
import DriversSection from './components/DriversSection';
import LoyaltyRewards from './components/LoyaltyRewards';
import FAQ from './components/FAQ';
import Footer from './components/Footer';
import ContactPage from './pages/ContactPage';
import SitemapPage from './pages/SitemapPage';
import DashboardPage from './pages/DashboardPage';
import AdminLoginPage from './pages/AdminLoginPage';
import ProtectedRoute from './components/ProtectedRoute';

const HomePage = () => (
  <>
    <Header />
    <Hero />
    <AboutSection />
    <HowItWorks />
    <FeaturedRestaurants />
    <CitiesSection />
    <Testimonials />
    <AppDownload />
    <DriversSection />
    <LoyaltyRewards />
    <FAQ />
    <Footer />
  </>
);

function App() {
  return (
    <LanguageProvider>
      <Router>
        <div className="min-h-screen bg-background">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/sitemap" element={<SitemapPage />} />
            <Route path="/admin/login" element={<AdminLoginPage />} />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <DashboardPage />
              </ProtectedRoute>
            } />
          </Routes>
        </div>
      </Router>
    </LanguageProvider>
  );
}

export default App;