import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/auth_provider.dart';
import '../../providers/language_provider.dart';
import 'settings_screen.dart';
import '../profile/edit_profile_screen.dart';
import '../profile/change_password_screen.dart';
import '../profile/manage_documents_screen.dart';
import '../support/support_screen.dart';
import '../legal/legal_screen.dart';
import '../themes/theme_settings_screen.dart';
import '../privacy/privacy_settings_screen.dart';

/// Modern Settings screen for driver app (English version)
class SettingsScreenEN extends StatefulWidget {
  const SettingsScreenEN({super.key});

  @override
  State<SettingsScreenEN> createState() => _SettingsScreenENState();
}

class _SettingsScreenENState extends State<SettingsScreenEN> {
  bool _isOnline = true;
  bool _pushNotifications = true;
  bool _soundVibration = true;
  bool _biometricLogin = false;
  final String _selectedLanguage = 'English';
  final String _selectedTheme = 'Dark';
  String _selectedMapType = 'Google Maps';

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
      appBar: _buildAppBar(isDark),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Account Settings
            _buildSection(
              title: 'Account Settings',
              isDark: isDark,
              children: [
                _buildSettingItem(
                  icon: Icons.person_outline,
                  title: 'Edit Profile',
                  isDark: isDark,
                  onTap: () => _handleEditProfile(),
                ),
                _buildSettingItem(
                  icon: Icons.lock_outline,
                  title: 'Change Password',
                  isDark: isDark,
                  onTap: () => _handleChangePassword(),
                ),
                _buildSettingItem(
                  icon: Icons.description_outlined,
                  title: 'Manage Documents',
                  isDark: isDark,
                  onTap: () => _handleManageDocuments(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Availability
            _buildSection(
              title: 'Availability',
              isDark: isDark,
              children: [
                _buildToggleItem(
                  icon: Icons.power_settings_new,
                  title: 'Online / Offline',
                  value: _isOnline,
                  isDark: isDark,
                  onChanged: (value) => setState(() => _isOnline = value),
                ),
                _buildSettingItem(
                  icon: Icons.location_on_outlined,
                  title: 'Preferred Delivery Zones',
                  isDark: isDark,
                  onTap: () => _handleDeliveryZones(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Notifications
            _buildSection(
              title: 'Notifications',
              isDark: isDark,
              children: [
                _buildToggleItem(
                  icon: Icons.notifications_outlined,
                  title: 'Push Notifications',
                  value: _pushNotifications,
                  isDark: isDark,
                  onChanged: (value) => setState(() => _pushNotifications = value),
                ),
                _buildToggleItem(
                  icon: Icons.volume_up_outlined,
                  title: 'Sound & Vibration',
                  value: _soundVibration,
                  isDark: isDark,
                  onChanged: (value) => setState(() => _soundVibration = value),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Security
            _buildSection(
              title: 'Security',
              isDark: isDark,
              children: [
                _buildToggleItem(
                  icon: Icons.fingerprint,
                  title: 'Biometric Login',
                  value: _biometricLogin,
                  isDark: isDark,
                  onChanged: (value) => setState(() => _biometricLogin = value),
                ),
                _buildSettingItem(
                  icon: Icons.shield_outlined,
                  title: 'Session History',
                  isDark: isDark,
                  onTap: () => _handleSessionHistory(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Payment & Earnings
            _buildSection(
              title: 'Payment & Earnings',
              isDark: isDark,
              children: [
                _buildSettingItem(
                  icon: Icons.attach_money,
                  title: 'Earnings Overview',
                  isDark: isDark,
                  onTap: () => _handleEarningsOverview(),
                ),
                _buildSettingItem(
                  icon: Icons.account_balance_wallet_outlined,
                  title: 'Add Bank Account',
                  isDark: isDark,
                  onTap: () => _handleAddBankAccount(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // App Preferences
            _buildSection(
              title: 'App Preferences',
              isDark: isDark,
              children: [
                _buildSelectionItem(
                  icon: Icons.language,
                  title: 'Language',
                  value: context.watch<LanguageProvider>().currentLanguage,
                  options: ['English', 'العربية'],
                  isDark: isDark,
                  onChanged: (value) async {
                    final languageProvider = context.read<LanguageProvider>();
                    final navigator = Navigator.of(context);

                    await languageProvider.changeLanguage(value);

                    // Navigate to appropriate settings screen
                    if (mounted) {
                      navigator.pushReplacement(
                        MaterialPageRoute(
                          builder: (context) => value == 'العربية'
                              ? const SettingsScreen()
                              : const SettingsScreenEN(),
                        ),
                      );
                    }
                  },
                ),
                _buildSettingItem(
                  icon: Icons.palette,
                  title: 'Theme Settings',
                  subtitle: 'Customize app appearance',
                  isDark: isDark,
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ThemeSettingsScreen(),
                    ),
                  ),
                ),
                _buildSettingItem(
                  icon: Icons.privacy_tip,
                  title: 'Privacy Settings',
                  subtitle: 'Control your data and privacy',
                  isDark: isDark,
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PrivacySettingsScreen(),
                    ),
                  ),
                ),
                _buildSelectionItem(
                  icon: Icons.map_outlined,
                  title: 'Map Type',
                  value: _selectedMapType,
                  options: ['Google Maps', 'Waze'],
                  isDark: isDark,
                  onChanged: (value) => setState(() => _selectedMapType = value),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Support
            _buildSection(
              title: 'Support',
              isDark: isDark,
              children: [
                _buildSettingItem(
                  icon: Icons.help_outline,
                  title: 'Help Center',
                  isDark: isDark,
                  onTap: () => _handleHelpCenter(),
                ),
                _buildSettingItem(
                  icon: Icons.message_outlined,
                  title: 'Contact Support',
                  isDark: isDark,
                  onTap: () => _handleContactSupport(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Legal
            _buildSection(
              title: 'Legal',
              isDark: isDark,
              children: [
                _buildSettingItem(
                  icon: Icons.description_outlined,
                  title: 'Terms & Conditions',
                  isDark: isDark,
                  onTap: () => _handleTermsConditions(),
                ),
                _buildSettingItem(
                  icon: Icons.privacy_tip_outlined,
                  title: 'Privacy Policy',
                  isDark: isDark,
                  onTap: () => _handlePrivacyPolicy(),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Logout Button
            _buildLogoutButton(isDark),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(bool isDark) {
    return AppBar(
      backgroundColor: isDark ? const Color(0xFF0F231A) : Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: isDark ? Colors.white : Colors.black87,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        'Settings',
        style: DriverTypography.getContextualStyle(
          context,
          fontSize: DriverTypography.headlineMedium,
          fontWeight: FontWeight.w600,
          color: isDark ? Colors.white : Colors.black87,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildSection({
    required String title,
    required bool isDark,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: DriverTypography.getContextualStyle(
                context,
                fontSize: DriverTypography.titleMedium,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF11B96F),
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required bool isDark,
    required VoidCallback onTap,
    String? subtitle,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: const Color(0xFF11B96F),
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: DriverTypography.getContextualStyle(
                      context,
                      fontSize: DriverTypography.bodyLarge,
                      fontWeight: FontWeight.w500,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: DriverTypography.getContextualStyle(
                        context,
                        fontSize: DriverTypography.bodySmall,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleItem({
    required IconData icon,
    required String title,
    required bool value,
    required bool isDark,
    required ValueChanged<bool> onChanged,
    String? subtitle,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF11B96F).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF11B96F),
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: DriverTypography.getContextualStyle(
                    context,
                    fontSize: DriverTypography.bodyLarge,
                    fontWeight: FontWeight.w500,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: DriverTypography.getContextualStyle(
                      context,
                      fontSize: DriverTypography.bodySmall,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF11B96F),
            activeTrackColor: const Color(0xFF11B96F).withValues(alpha: 0.3),
            inactiveThumbColor: isDark ? Colors.grey[400] : Colors.grey[300],
            inactiveTrackColor: isDark ? Colors.grey[700] : Colors.grey[200],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionItem({
    required IconData icon,
    required String title,
    required String value,
    required List<String> options,
    required bool isDark,
    required ValueChanged<String> onChanged,
  }) {
    return InkWell(
      onTap: () => _showSelectionDialog(title, value, options, onChanged),
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: const Color(0xFF11B96F),
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: DriverTypography.getContextualStyle(
                      context,
                      fontSize: DriverTypography.bodyLarge,
                      fontWeight: FontWeight.w500,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: DriverTypography.getContextualStyle(
                      context,
                      fontSize: DriverTypography.bodySmall,
                      color: const Color(0xFF11B96F),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton(bool isDark) {
    return Container(
      width: double.infinity,
      height: 56,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ElevatedButton.icon(
        onPressed: _handleLogout,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        icon: const Icon(Icons.logout, size: 20),
        label: Text(
          'Logout',
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyLarge,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  void _showSelectionDialog(
    String title,
    String currentValue,
    List<String> options,
    ValueChanged<String> onChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return AlertDialog(
          backgroundColor: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            title,
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.titleMedium,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: options.map((option) {
              final isSelected = option == currentValue;
              return ListTile(
                title: Text(
                  option,
                  style: DriverTypography.getContextualStyle(
                    context,
                    fontSize: DriverTypography.bodyLarge,
                    color: isSelected
                        ? const Color(0xFF11B96F)
                        : (isDark ? Colors.white : Colors.black87),
                  ),
                ),
                leading: Radio<String>(
                  value: option,
                  groupValue: currentValue,
                  onChanged: (value) {
                    if (value != null) {
                      onChanged(value);
                      Navigator.of(context).pop();
                    }
                  },
                  activeColor: const Color(0xFF11B96F),
                ),
                onTap: () {
                  onChanged(option);
                  Navigator.of(context).pop();
                },
              );
            }).toList(),
          ),
        );
      },
    );
  }

  // Handler functions
  void _handleEditProfile() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const EditProfileScreen(),
      ),
    );
  }

  void _handleChangePassword() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ChangePasswordScreen(),
      ),
    );
  }

  void _handleManageDocuments() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ManageDocumentsScreen(),
      ),
    );
  }

  void _handleDeliveryZones() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Preferred Delivery Zones - Coming Soon')),
    );
  }

  void _handleSessionHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Session History - Coming Soon')),
    );
  }

  void _handleEarningsOverview() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Earnings Overview - Coming Soon')),
    );
  }

  void _handleAddBankAccount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add Bank Account - Coming Soon')),
    );
  }

  void _handleHelpCenter() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Help Center - Coming Soon')),
    );
  }

  void _handleContactSupport() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SupportScreen(),
      ),
    );
  }

  void _handleTermsConditions() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LegalScreen(),
      ),
    );
  }

  void _handlePrivacyPolicy() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Privacy Policy - Coming Soon')),
    );
  }

  void _handleLogout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) {
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return AlertDialog(
          backgroundColor: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Logout',
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.titleMedium,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.bodyLarge,
              color: isDark ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Cancel',
                style: DriverTypography.getContextualStyle(
                  context,
                  fontSize: DriverTypography.bodyLarge,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Logout',
                style: DriverTypography.getContextualStyle(
                  context,
                  fontSize: DriverTypography.bodyLarge,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );

    if (confirmed == true && mounted) {
      final authProvider = context.read<AuthProvider>();
      await authProvider.logout();
      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      }
    }
  }
}
