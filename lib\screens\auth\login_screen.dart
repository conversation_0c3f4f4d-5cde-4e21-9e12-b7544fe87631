import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/auth_provider.dart';

/// شاشة تسجيل الدخول لتطبيق السائق
/// تدعم الوضع الفاتح والمظلم مع تصميم محسن للسائقين
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _isPasswordVisible = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;
    
    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 60),
                
                // شعار التطبيق والعنوان
                _buildHeader(isDark),
                
                const SizedBox(height: 60),
                
                // حقول الإدخال
                _buildInputFields(isDark, colorScheme),
                
                const SizedBox(height: 16),
                
                // رابط نسيان كلمة المرور
                _buildForgotPasswordLink(isDark),
                
                const SizedBox(height: 32),
                
                // زر تسجيل الدخول
                _buildLoginButton(isDark),
                
                const SizedBox(height: 24),
                
                // فاصل "أو المتابعة مع"
                _buildDivider(isDark),
                
                const SizedBox(height: 24),
                
                // زر Google
                _buildGoogleButton(isDark),
                
                const SizedBox(height: 40),
                
                // رابط التواصل مع الإدارة
                _buildContactAdminLink(isDark),
                
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark) {
    return Column(
      children: [
        Text(
          'Welcome to Wasslti Partner',
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.displaySmall,
            fontWeight: DriverTypography.bold,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 12),

        Text(
          'Log in to start delivering orders.',
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyLarge,
            color: isDark ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInputFields(bool isDark, ColorScheme colorScheme) {
    return Column(
      children: [
        // حقل رقم الهاتف
        _buildPhoneField(isDark, colorScheme),
        
        const SizedBox(height: 20),
        
        // حقل كلمة المرور
        _buildPasswordField(isDark, colorScheme),
      ],
    );
  }

  Widget _buildPhoneField(bool isDark, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? const Color(0xFF2E5A3E) : const Color(0xFFE0E0E0),
          width: 1,
        ),
        boxShadow: isDark ? null : [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _phoneController,
        keyboardType: TextInputType.phone,
        style: DriverTypography.getContextualStyle(
          context,
          fontSize: DriverTypography.bodyLarge,
        ),
        decoration: InputDecoration(
          hintText: 'Phone Number',
          hintStyle: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyLarge,
            color: isDark ? const Color(0xFF757575) : const Color(0xFF9E9E9E),
          ),
          prefixIcon: Icon(
            Icons.phone,
            color: isDark ? const Color(0xFF757575) : const Color(0xFF9E9E9E),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter your phone number';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildPasswordField(bool isDark, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? const Color(0xFF2E5A3E) : const Color(0xFFE0E0E0),
          width: 1,
        ),
        boxShadow: isDark ? null : [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _passwordController,
        obscureText: !_isPasswordVisible,
        style: DriverTypography.getContextualStyle(
          context,
          fontSize: DriverTypography.bodyLarge,
        ),
        decoration: InputDecoration(
          hintText: 'Password',
          hintStyle: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyLarge,
            color: isDark ? const Color(0xFF757575) : const Color(0xFF9E9E9E),
          ),
          prefixIcon: Icon(
            Icons.lock,
            color: isDark ? const Color(0xFF757575) : const Color(0xFF9E9E9E),
          ),
          suffixIcon: IconButton(
            icon: Icon(
              _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
              color: isDark ? const Color(0xFF757575) : const Color(0xFF9E9E9E),
            ),
            onPressed: () {
              setState(() {
                _isPasswordVisible = !_isPasswordVisible;
              });
            },
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter your password';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildForgotPasswordLink(bool isDark) {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: () {
          // TODO: Navigate to forgot password screen
        },
        child: Text(
          'Forgot Password?',
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyMedium,
            fontWeight: DriverTypography.medium,
            color: const Color(0xFF11B96F),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginButton(bool isDark) {
    return SizedBox(
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleLogin,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF11B96F),
          foregroundColor: Colors.white,
          elevation: isDark ? 0 : 2,
          shadowColor: Colors.black.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                'Log In',
                style: DriverTypography.bigButtonStyle.copyWith(
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Widget _buildDivider(bool isDark) {
    return Row(
      children: [
        Expanded(
          child: Divider(
            color: isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
            thickness: 1,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Or continue with',
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.bodySmall,
              color: isDark ? const Color(0xFF757575) : const Color(0xFF9E9E9E),
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
            thickness: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildGoogleButton(bool isDark) {
    return SizedBox(
      height: 56,
      child: OutlinedButton.icon(
        onPressed: _handleGoogleLogin,
        style: OutlinedButton.styleFrom(
          backgroundColor: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          side: BorderSide(
            color: isDark ? const Color(0xFF2E5A3E) : const Color(0xFFE0E0E0),
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        icon: Image.asset(
          'assets/images/google_logo.png', // يجب إضافة شعار Google
          height: 24,
          width: 24,
          errorBuilder: (context, error, stackTrace) {
            return Icon(
              Icons.g_mobiledata,
              size: 24,
              color: isDark ? Colors.white : Colors.black87,
            );
          },
        ),
        label: Text(
          'Continue with Google',
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyLarge,
            fontWeight: DriverTypography.medium,
          ),
        ),
      ),
    );
  }

  Widget _buildContactAdminLink(bool isDark) {
    return Center(
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          text: "Don't have an account? ",
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyMedium,
            color: isDark ? const Color(0xFF9E9E9E) : const Color(0xFF757575),
          ),
          children: [
            WidgetSpan(
              child: GestureDetector(
                onTap: _handleContactAdmin,
                child: Text(
                  'Contact admin.',
                  style: DriverTypography.getContextualStyle(
                    context,
                    fontSize: DriverTypography.bodyMedium,
                    fontWeight: DriverTypography.medium,
                    color: const Color(0xFF11B96F),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleLogin() async {
    if (_formKey.currentState!.validate()) {
      if (!mounted) return;

      setState(() {
        _isLoading = true;
      });

      final authProvider = context.read<AuthProvider>();

      // محاولة تسجيل الدخول
      final success = await authProvider.login(
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (success) {
        // AuthWrapper سيتولى التنقل تلقائياً لشاشة OTP
        if (kDebugMode) {
          print('Login successful - AuthWrapper will handle navigation');
        }
      } else {
        // عرض رسالة الخطأ
        if (mounted && authProvider.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                authProvider.errorMessage!,
                style: DriverTypography.getContextualStyle(
                  context,
                  fontSize: DriverTypography.bodyMedium,
                  color: Colors.white,
                ),
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    }
  }

  void _handleGoogleLogin() {
    // TODO: Implement Google login
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Google login will be implemented',
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyMedium,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF11B96F),
      ),
    );
  }

  void _handleContactAdmin() {
    // TODO: Implement contact admin functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Contact admin functionality will be implemented',
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyMedium,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF11B96F),
      ),
    );
  }
}
