import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../core/theme/app_colors.dart';

class SidebarWidget extends StatefulWidget {
  const SidebarWidget({super.key});

  @override
  State<SidebarWidget> createState() => _SidebarWidgetState();
}

class _SidebarWidgetState extends State<SidebarWidget> {
  int selectedIndex = 0;

  final List<SidebarItem> menuItems = [
    SidebarItem(
      icon: Icons.home_outlined,
      selectedIcon: Icons.home,
      label: 'Home',
    ),
    SidebarItem(
      icon: Icons.restaurant_menu_outlined,
      selectedIcon: Icons.restaurant_menu,
      label: 'Food Order',
    ),
    SidebarItem(
      icon: Icons.favorite_outline,
      selectedIcon: Icons.favorite,
      label: 'Favorite Menu',
    ),
    SidebarItem(
      icon: Icons.message_outlined,
      selectedIcon: Icons.message,
      label: 'Message',
    ),
    SidebarItem(
      icon: Icons.history_outlined,
      selectedIcon: Icons.history,
      label: 'Order History',
    ),
    SidebarItem(
      icon: Icons.notifications_outlined,
      selectedIcon: Icons.notifications,
      label: 'Notification',
    ),
    SidebarItem(
      icon: Icons.receipt_long_outlined,
      selectedIcon: Icons.receipt_long,
      label: 'Bill',
    ),
    SidebarItem(
      icon: Icons.restaurant_outlined,
      selectedIcon: Icons.restaurant,
      label: 'Restaurant',
      hasSubmenu: true,
      submenuItems: [
        SidebarItem(
          icon: Icons.restaurant,
          label: 'Restaurant',
        ),
        SidebarItem(
          icon: Icons.menu_book,
          label: 'Menu',
        ),
        SidebarItem(
          icon: Icons.shopping_cart,
          label: 'Orders',
        ),
        SidebarItem(
          icon: Icons.star,
          label: 'Reviews',
        ),
      ],
    ),
    SidebarItem(
      icon: Icons.people_outline,
      selectedIcon: Icons.people,
      label: 'Drivers',
    ),
    SidebarItem(
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings,
      label: 'Setting',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          right: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Logo Section
          Container(
            height: 70,
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              children: [
                Text(
                  'Wssalti_POS',
                  style: GoogleFonts.inter(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1, color: AppColors.inputBorder),
          
          // Menu Items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 16),
              itemCount: menuItems.length,
              itemBuilder: (context, index) {
                final item = menuItems[index];
                final isSelected = selectedIndex == index;
                
                return _buildMenuItem(item, index, isSelected);
              },
            ),
          ),
          
          // Bottom Section with Image and Upload Button
          Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Food Image
                Container(
                  width: 120,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primary.withValues(alpha: 0.8),
                        AppColors.primary,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.fastfood,
                      color: AppColors.white,
                      size: 40,
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Text
                Text(
                  'Share your own',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  'recipes',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Upload Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {},
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Upload Now',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(SidebarItem item, int index, bool isSelected) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListTile(
            leading: Icon(
              isSelected ? (item.selectedIcon ?? item.icon) : item.icon,
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
              size: 20,
            ),
            title: Text(
              item.label,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
            ),
            trailing: item.hasSubmenu
                ? Icon(
                    Icons.keyboard_arrow_down,
                    color: isSelected ? AppColors.primary : AppColors.textSecondary,
                    size: 16,
                  )
                : null,
            onTap: () {
              setState(() {
                selectedIndex = index;
              });
            },
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            dense: true,
          ),
        ),
        
        // Submenu items (if expanded)
        if (item.hasSubmenu && isSelected && item.submenuItems != null)
          ...item.submenuItems!.map((subItem) => Container(
                margin: const EdgeInsets.only(left: 32, right: 16, bottom: 2),
                child: ListTile(
                  leading: Icon(
                    subItem.icon,
                    color: AppColors.textSecondary,
                    size: 18,
                  ),
                  title: Text(
                    subItem.label,
                    style: GoogleFonts.inter(
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  onTap: () {
                    // Handle submenu item tap
                  },
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
                  dense: true,
                ),
              )),
      ],
    );
  }
}

class SidebarItem {
  final IconData icon;
  final IconData? selectedIcon;
  final String label;
  final bool hasSubmenu;
  final List<SidebarItem>? submenuItems;

  SidebarItem({
    required this.icon,
    this.selectedIcon,
    required this.label,
    this.hasSubmenu = false,
    this.submenuItems,
  });
}
