import React, { useState } from 'react';
import { Star, Clock, MapPin, Heart, Filter, ChefHat, Award, Zap } from 'lucide-react';
import { useCart } from '../hooks/useCart';

const FeaturedRestaurants = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [favorites, setFavorites] = useState<number[]>([]);
  const cart = useCart();

  const filters = [
    { id: 'all', label: 'الكل', labelEn: 'All' },
    { id: 'moroccan', label: 'مغربي', labelEn: 'Moroccan' },
    { id: 'fast-food', label: 'وجبات سريعة', labelEn: 'Fast Food' },
    { id: 'desserts', label: 'حلويات', labelEn: 'Desserts' },
    { id: 'healthy', label: 'صحي', labelEn: 'Healthy' }
  ];

  const restaurants = [
    {
      id: 1,
      name: "رياض الطبخ الأصيل",
      nameEn: "Riad Authentic Cuisine",
      image: "https://images.pexels.com/photos/1581384/pexels-photo-1581384.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&fit=crop",
      rating: 4.9,
      reviewCount: 3200,
      deliveryTime: "20-30 دقيقة",
      deliveryFee: "مجاني",
      cuisine: "مغربي تقليدي",
      cuisineEn: "Traditional Moroccan",
      category: 'moroccan',
      featured: true,
      badges: ['أفضل تقييم', 'توصيل مجاني'],
      specialOffer: "خصم 25% على الطلب الأول",
      priceRange: "$$"
    },
    {
      id: 2,
      name: "مشاوي مراكش",
      nameEn: "Marrakech Grill",
      image: "https://images.pexels.com/photos/1109197/pexels-photo-1109197.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&fit=crop",
      rating: 4.8,
      reviewCount: 2100,
      deliveryTime: "25-35 دقيقة",
      deliveryFee: "15 درهم",
      cuisine: "مشاوي ولحوم",
      cuisineEn: "Grilled Specialties",
      category: 'moroccan',
      featured: false,
      badges: ['الأكثر طلباً'],
      specialOffer: null,
      priceRange: "$$$"
    },
    {
      id: 3,
      name: "حلويات الدار البيضاء",
      nameEn: "Casablanca Sweets",
      image: "https://images.pexels.com/photos/1055272/pexels-photo-1055272.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&fit=crop",
      rating: 4.9,
      reviewCount: 4500,
      deliveryTime: "15-25 دقيقة",
      deliveryFee: "مجاني",
      cuisine: "حلويات ومعجنات",
      cuisineEn: "Desserts & Pastries",
      category: 'desserts',
      featured: true,
      badges: ['الأسرع', 'حلويات طازجة'],
      specialOffer: "اشتر 2 واحصل على 1 مجاناً",
      priceRange: "$$"
    },
    {
      id: 4,
      name: "بيسترو الأطلس",
      nameEn: "Atlas Bistro",
      image: "https://images.pexels.com/photos/1438672/pexels-photo-1438672.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&fit=crop",
      rating: 4.7,
      reviewCount: 1800,
      deliveryTime: "30-40 دقيقة",
      deliveryFee: "20 درهم",
      cuisine: "متوسطي عصري",
      cuisineEn: "Modern Mediterranean",
      category: 'healthy',
      featured: false,
      badges: ['طعام صحي'],
      specialOffer: null,
      priceRange: "$$$$"
    },
    {
      id: 5,
      name: "مطبخ فاس العريق",
      nameEn: "Fez Heritage Kitchen",
      image: "https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&fit=crop",
      rating: 4.8,
      reviewCount: 2800,
      deliveryTime: "25-35 دقيقة",
      deliveryFee: "مجاني",
      cuisine: "مغربي أصيل",
      cuisineEn: "Authentic Moroccan",
      category: 'moroccan',
      featured: true,
      badges: ['تراث أصيل', 'طبخ منزلي'],
      specialOffer: "وجبة مجانية مع كل 5 طلبات",
      priceRange: "$$$"
    },
    {
      id: 6,
      name: "برجر ستيشن",
      nameEn: "Burger Station",
      image: "https://images.pexels.com/photos/1435904/pexels-photo-1435904.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&fit=crop",
      rating: 4.6,
      reviewCount: 3500,
      deliveryTime: "15-20 دقيقة",
      deliveryFee: "10 درهم",
      cuisine: "وجبات سريعة",
      cuisineEn: "Fast Food",
      category: 'fast-food',
      featured: false,
      badges: ['الأسرع', 'وجبات سريعة'],
      specialOffer: "كومبو مع مشروب مجاني",
      priceRange: "$"
    }
  ];

  const filteredRestaurants = activeFilter === 'all' 
    ? restaurants 
    : restaurants.filter(restaurant => restaurant.category === activeFilter);

  const toggleFavorite = (id: number) => {
    setFavorites(prev => 
      prev.includes(id) 
        ? prev.filter(fav => fav !== id)
        : [...prev, id]
    );
  };

  const handleOrderFromRestaurant = (restaurant: typeof restaurants[0]) => {
    // Add sample items to cart
    const sampleItems = [
      {
        id: restaurant.id * 100 + 1,
        name: 'طبق مميز من ' + restaurant.name,
        price: 85,
        restaurant: restaurant.name,
        image: restaurant.image
      },
      {
        id: restaurant.id * 100 + 2,
        name: 'وجبة خاصة',
        price: 120,
        restaurant: restaurant.name,
        image: restaurant.image
      }
    ];
    
    sampleItems.forEach(item => cart.addItem(item));
    cart.setIsOpen(true);
  };
  return (
    <section id="restaurants" className="py-24 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-orange-400/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <ChefHat className="w-4 h-4" />
            <span className="rtl-font">مطاعم مميزة</span>
          </div>
          <h2 className="text-5xl font-bold text-accent mb-6 rtl-font">
            أفضل المطاعم
            <br />
            <span className="text-primary">في منطقتك</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed rtl-font">
            اكتشف أفضل المطاعم في المغرب، من الأطباق التقليدية المغربية إلى النكهات العالمية المتنوعة.
          </p>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {filters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 ${
                activeFilter === filter.id
                  ? 'bg-primary text-white shadow-lg scale-105'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105'
              }`}
            >
              <span className="rtl-font">{filter.label}</span>
            </button>
          ))}
        </div>

        {/* Restaurants Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {filteredRestaurants.map((restaurant) => (
            <div key={restaurant.id} className="group bg-white rounded-3xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
              <div className="relative overflow-hidden">
                <img 
                  src={restaurant.image} 
                  alt={restaurant.name}
                  className="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-700"
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                {/* Favorite Button */}
                <button 
                  onClick={() => toggleFavorite(restaurant.id)}
                  className="absolute top-4 left-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-all duration-300 hover:scale-110"
                >
                  <Heart className={`w-5 h-5 transition-colors ${
                    favorites.includes(restaurant.id) 
                      ? 'text-red-500 fill-current' 
                      : 'text-gray-600 hover:text-red-500'
                  }`} />
                </button>
                
                {/* Badges */}
                <div className="absolute top-4 right-4 flex flex-col gap-2">
                  {restaurant.featured && (
                    <div className="bg-gradient-to-r from-primary to-primary/90 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center space-x-1 rtl:space-x-reverse">
                      <Award className="w-3 h-3" />
                      <span>مميز</span>
                    </div>
                  )}
                  {restaurant.badges.map((badge, index) => (
                    <div key={index} className="bg-black/70 text-white px-3 py-1 rounded-full text-xs font-medium">
                      {badge}
                    </div>
                  ))}
                </div>

                {/* Special Offer */}
                {restaurant.specialOffer && (
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="bg-orange-500 text-white px-3 py-2 rounded-xl text-sm font-bold text-center">
                      <Zap className="w-4 h-4 inline mr-1" />
                      {restaurant.specialOffer}
                    </div>
                  </div>
                )}
              </div>
              
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-xl font-bold text-accent mb-1 rtl-font group-hover:text-primary transition-colors">
                      {restaurant.name}
                    </h3>
                    <p className="text-gray-600 text-sm rtl-font">{restaurant.cuisine}</p>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1 rtl:space-x-reverse mb-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-bold">{restaurant.rating}</span>
                    </div>
                    <span className="text-xs text-gray-500">({restaurant.reviewCount})</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Clock className="w-4 h-4 text-primary" />
                    <span className="rtl-font">{restaurant.deliveryTime}</span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <MapPin className="w-4 h-4 text-primary" />
                    <span className="rtl-font">{restaurant.deliveryFee}</span>
                  </div>
                  <div className="text-primary font-bold">
                    {restaurant.priceRange}
                  </div>
                </div>

                <button 
                  onClick={() => handleOrderFromRestaurant(restaurant)}
                  className="w-full bg-gradient-to-r from-primary to-primary/90 text-white py-3 rounded-2xl font-semibold hover:shadow-lg hover:scale-105 transition-all duration-300"
                >
                  اطلب الآن
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center">
          <button 
            onClick={() => alert('سيتم تحميل المزيد من المطاعم...')}
            className="bg-gradient-to-r from-primary to-primary/90 text-white px-10 py-4 rounded-2xl font-bold text-lg hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center space-x-2 rtl:space-x-reverse mx-auto"
          >
            <span>عرض المزيد من المطاعم</span>
            <Filter className="w-5 h-5" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedRestaurants;