import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Driver Agreement screen with detailed partnership agreement
class DriverAgreementScreen extends StatefulWidget {
  const DriverAgreementScreen({super.key});

  @override
  State<DriverAgreementScreen> createState() => _DriverAgreementScreenState();
}

class _DriverAgreementScreenState extends State<DriverAgreementScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Driver Agreement', 'اتفاقية السائق'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Header
                _buildHeader(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Agreement Content
                _buildAgreementContent(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Signature Section
                _buildSignatureSection(isDark, languageProvider),
                
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.orange[800]!,
            Colors.orange[600]!,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.handshake,
            size: 60,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('Driver Partnership Agreement', 'اتفاقية شراكة السائق'),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Official partnership agreement between driver and Wasslti',
              'اتفاقية الشراكة الرسمية بين السائق ووصلتي'
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              languageProvider.getText('Effective Date: January 1, 2024', 'تاريخ السريان: 1 يناير 2024'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAgreementContent(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSection(
            title: languageProvider.getText('Article 1: Partnership Nature', 'المادة 1: طبيعة الشراكة'),
            content: languageProvider.getText(
              'This agreement establishes an independent contractor relationship between the Driver and Wasslti Transportation Services. The Driver operates as an independent business entity and is not an employee of Wasslti.',
              'تنشئ هذه الاتفاقية علاقة مقاول مستقل بين السائق وخدمات النقل وصلتي. يعمل السائق ككيان تجاري مستقل وليس موظفاً لدى وصلتي.'
            ),
            isDark: isDark,
          ),
          
          _buildSection(
            title: languageProvider.getText('Article 2: Driver Obligations', 'المادة 2: التزامات السائق'),
            content: languageProvider.getText(
              'The Driver agrees to: (a) Provide safe and professional transportation services, (b) Maintain valid licenses and insurance, (c) Keep vehicle in good condition, (d) Follow all traffic laws and regulations, (e) Treat passengers with respect and courtesy.',
              'يوافق السائق على: (أ) تقديم خدمات نقل آمنة ومهنية، (ب) الحفاظ على تراخيص وتأمين ساري، (ج) الحفاظ على المركبة في حالة جيدة، (د) اتباع جميع قوانين ولوائح المرور، (هـ) معاملة الركاب باحترام وأدب.'
            ),
            isDark: isDark,
          ),
          
          _buildSection(
            title: languageProvider.getText('Article 3: Wasslti Obligations', 'المادة 3: التزامات وصلتي'),
            content: languageProvider.getText(
              'Wasslti agrees to: (a) Provide technology platform for ride requests, (b) Process payments and handle billing, (c) Provide customer support services, (d) Maintain platform security and reliability, (e) Offer training and support resources.',
              'توافق وصلتي على: (أ) توفير منصة تقنية لطلبات الرحلات، (ب) معالجة المدفوعات والفوترة، (ج) تقديم خدمات دعم العملاء، (د) الحفاظ على أمان وموثوقية المنصة، (هـ) تقديم موارد التدريب والدعم.'
            ),
            isDark: isDark,
          ),
          
          _buildSection(
            title: languageProvider.getText('Article 4: Revenue Sharing', 'المادة 4: تقاسم الإيرادات'),
            content: languageProvider.getText(
              'Revenue sharing structure: Driver receives 80% of the fare amount, Wasslti retains 20% as service fee. Additional fees may apply for premium services. Payments are processed weekly every Tuesday. All applicable taxes are the responsibility of the Driver.',
              'هيكل تقاسم الإيرادات: يحصل السائق على 80% من مبلغ الأجرة، تحتفظ وصلتي بـ 20% كرسوم خدمة. قد تطبق رسوم إضافية للخدمات المميزة. تتم معالجة المدفوعات أسبوعياً كل يوم ثلاثاء. جميع الضرائب المعمول بها هي مسؤولية السائق.'
            ),
            isDark: isDark,
          ),
          
          _buildSection(
            title: languageProvider.getText('Article 5: Vehicle Requirements', 'المادة 5: متطلبات المركبة'),
            content: languageProvider.getText(
              'Vehicle must meet the following standards: (a) Model year 2015 or newer, (b) Valid registration and insurance, (c) Regular safety inspections, (d) Clean interior and exterior, (e) Working air conditioning and heating, (f) GPS navigation capability.',
              'يجب أن تلبي المركبة المعايير التالية: (أ) موديل 2015 أو أحدث، (ب) تسجيل وتأمين ساري، (ج) فحوصات سلامة منتظمة، (د) داخلية وخارجية نظيفة، (هـ) تكييف وتدفئة يعملان، (و) قدرة ملاحة GPS.'
            ),
            isDark: isDark,
          ),
          
          _buildSection(
            title: languageProvider.getText('Article 6: Performance Standards', 'المادة 6: معايير الأداء'),
            content: languageProvider.getText(
              'Driver must maintain: (a) Minimum 4.5 star rating, (b) 90% acceptance rate for ride requests, (c) Less than 5% cancellation rate, (d) Timely arrival within 5 minutes of estimated time, (e) Professional appearance and behavior.',
              'يجب على السائق الحفاظ على: (أ) تقييم 4.5 نجوم كحد أدنى، (ب) معدل قبول 90% لطلبات الرحلات، (ج) أقل من 5% معدل إلغاء، (د) وصول في الوقت المحدد خلال 5 دقائق من الوقت المقدر، (هـ) مظهر وسلوك مهني.'
            ),
            isDark: isDark,
          ),
          
          _buildSection(
            title: languageProvider.getText('Article 7: Termination', 'المادة 7: الإنهاء'),
            content: languageProvider.getText(
              'Either party may terminate this agreement with 30 days written notice. Immediate termination may occur for: (a) Violation of safety standards, (b) Criminal activity, (c) Consistent poor performance, (d) Breach of agreement terms. Upon termination, all outstanding payments will be processed within 14 days.',
              'يجوز لأي من الطرفين إنهاء هذه الاتفاقية بإشعار كتابي مدته 30 يوماً. قد يحدث إنهاء فوري لـ: (أ) انتهاك معايير السلامة، (ب) نشاط إجرامي، (ج) أداء ضعيف مستمر، (د) خرق شروط الاتفاقية. عند الإنهاء، ستتم معالجة جميع المدفوعات المستحقة خلال 14 يوماً.'
            ),
            isDark: isDark,
          ),
          
          _buildSection(
            title: languageProvider.getText('Article 8: Dispute Resolution', 'المادة 8: حل النزاعات'),
            content: languageProvider.getText(
              'Any disputes arising from this agreement shall be resolved through: (1) Direct negotiation between parties, (2) Mediation by neutral third party, (3) Arbitration under Saudi Arabian law. All proceedings shall be conducted in Arabic and English.',
              'أي نزاعات تنشأ من هذه الاتفاقية يجب حلها من خلال: (1) التفاوض المباشر بين الأطراف، (2) الوساطة من طرف ثالث محايد، (3) التحكيم تحت القانون السعودي. جميع الإجراءات ستجرى باللغتين العربية والإنجليزية.'
            ),
            isDark: isDark,
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    required bool isDark,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 14,
              height: 1.6,
              color: isDark ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSignatureSection(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.verified,
                color: Colors.orange[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                languageProvider.getText('Agreement Acknowledgment', 'إقرار الاتفاقية'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Text(
            languageProvider.getText(
              'By using the Wasslti Partner application, you acknowledge that you have read, understood, and agree to be bound by all terms and conditions outlined in this Driver Partnership Agreement.',
              'باستخدام تطبيق وصلتي شريك، فإنك تقر بأنك قد قرأت وفهمت ووافقت على الالتزام بجميع الشروط والأحكام المذكورة في اتفاقية شراكة السائق هذه.'
            ),
            style: TextStyle(
              fontSize: 14,
              height: 1.5,
              color: isDark ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.orange.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageProvider.getText('Digital Signature', 'التوقيع الرقمي'),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  languageProvider.getText(
                    'Your registration and use of this application constitutes your digital signature and acceptance of this agreement.',
                    'تسجيلك واستخدامك لهذا التطبيق يشكل توقيعك الرقمي وقبولك لهذه الاتفاقية.'
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.orange[600],
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.today,
                      color: Colors.orange[600],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      languageProvider.getText(
                        'Signed on: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                        'تم التوقيع في: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}'
                      ),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.orange[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
