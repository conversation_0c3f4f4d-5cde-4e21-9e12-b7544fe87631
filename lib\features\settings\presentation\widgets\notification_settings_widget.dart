import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';

class NotificationSettingsWidget extends StatelessWidget {
  const NotificationSettingsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final notifications = [
      NotificationItem(
        icon: Icons.schedule,
        title: 'Expecting a slight delay',
        time: 'now',
        isNew: true,
      ),
      NotificationItem(
        icon: Icons.check_circle,
        title: 'Your Order has been picked-up by',
        time: '1 min ago',
        isNew: false,
      ),
      NotificationItem(
        icon: Icons.schedule,
        title: 'Your will arrive at 5:00 - 6:00pm today',
        time: '1 hour ago',
        isNew: false,
      ),
      NotificationItem(
        icon: Icons.schedule,
        title: 'Your will arrive at 5:00 - 7:00pm today',
        time: '3 hour ago',
        isNew: false,
      ),
    ];

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Notification',
            style: GoogleFonts.inter(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Notifications List
          Expanded(
            child: ListView.builder(
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.inputBorder,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // Icon
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: notification.isNew 
                              ? AppColors.primary.withValues(alpha: 0.1)
                              : AppColors.greyLight,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          notification.icon,
                          color: notification.isNew 
                              ? AppColors.primary 
                              : AppColors.textSecondary,
                          size: 20,
                        ),
                      ),
                      
                      const SizedBox(width: 16),
                      
                      // Content
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              notification.title,
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              notification.time,
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // New Indicator
                      if (notification.isNew)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class NotificationItem {
  final IconData icon;
  final String title;
  final String time;
  final bool isNew;

  NotificationItem({
    required this.icon,
    required this.title,
    required this.time,
    required this.isNew,
  });
}
