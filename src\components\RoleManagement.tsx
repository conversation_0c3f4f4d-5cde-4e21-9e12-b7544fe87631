import React, { useState } from 'react';
import { 
  Shield, 
  Plus, 
  Edit, 
  Trash2, 
  <PERSON>, 
  Eye, 
  Settings,
  Crown,
  UserCheck,
  MessageCircle,
  ShoppingBag,
  Car,
  BarChart3,
  Database,
  Mail,
  Bell,
  DollarSign,
  FileText,
  Lock,
  Unlock,
  Check,
  X
} from 'lucide-react';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ReactNode;
}

interface Role {
  id: string;
  name: string;
  nameEn: string;
  description: string;
  permissions: string[];
  userCount: number;
  color: string;
  icon: React.ReactNode;
  isSystem: boolean;
}

const RoleManagement = () => {
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // All available permissions
  const permissions: Permission[] = [
    // User Management
    { id: 'users.view', name: 'عرض المستخدمين', description: 'عرض قائمة المستخدمين', category: 'إدارة المستخدمين', icon: <Users className="w-4 h-4" /> },
    { id: 'users.create', name: 'إنشاء مستخدمين', description: 'إضافة مستخدمين جدد', category: 'إدارة المستخدمين', icon: <Users className="w-4 h-4" /> },
    { id: 'users.edit', name: 'تعديل المستخدمين', description: 'تعديل بيانات المستخدمين', category: 'إدارة المستخدمين', icon: <Users className="w-4 h-4" /> },
    { id: 'users.delete', name: 'حذف المستخدمين', description: 'حذف المستخدمين من النظام', category: 'إدارة المستخدمين', icon: <Users className="w-4 h-4" /> },
    
    // Role Management
    { id: 'roles.view', name: 'عرض الأدوار', description: 'عرض قائمة الأدوار', category: 'إدارة الأدوار', icon: <Shield className="w-4 h-4" /> },
    { id: 'roles.create', name: 'إنشاء أدوار', description: 'إنشاء أدوار جديدة', category: 'إدارة الأدوار', icon: <Shield className="w-4 h-4" /> },
    { id: 'roles.edit', name: 'تعديل الأدوار', description: 'تعديل الأدوار الموجودة', category: 'إدارة الأدوار', icon: <Shield className="w-4 h-4" /> },
    { id: 'roles.delete', name: 'حذف الأدوار', description: 'حذف الأدوار من النظام', category: 'إدارة الأدوار', icon: <Shield className="w-4 h-4" /> },
    
    // Restaurant Management
    { id: 'restaurants.view', name: 'عرض المطاعم', description: 'عرض قائمة المطاعم', category: 'إدارة المطاعم', icon: <Users className="w-4 h-4" /> },
    { id: 'restaurants.approve', name: 'الموافقة على المطاعم', description: 'الموافقة على طلبات المطاعم', category: 'إدارة المطاعم', icon: <UserCheck className="w-4 h-4" /> },
    { id: 'restaurants.edit', name: 'تعديل المطاعم', description: 'تعديل بيانات المطاعم', category: 'إدارة المطاعم', icon: <Edit className="w-4 h-4" /> },
    { id: 'restaurants.revenue', name: 'أرباح المطاعم', description: 'عرض أرباح المطاعم', category: 'إدارة المطاعم', icon: <DollarSign className="w-4 h-4" /> },
    
    // Driver Management
    { id: 'drivers.view', name: 'عرض السائقين', description: 'عرض قائمة السائقين', category: 'إدارة السائقين', icon: <Car className="w-4 h-4" /> },
    { id: 'drivers.approve', name: 'الموافقة على السائقين', description: 'الموافقة على طلبات السائقين', category: 'إدارة السائقين', icon: <UserCheck className="w-4 h-4" /> },
    { id: 'drivers.suspend', name: 'توقيف السائقين', description: 'توقيف السائقين مؤقتاً', category: 'إدارة السائقين', icon: <Lock className="w-4 h-4" /> },
    { id: 'drivers.revenue', name: 'أرباح السائقين', description: 'عرض أرباح السائقين', category: 'إدارة السائقين', icon: <DollarSign className="w-4 h-4" /> },
    
    // Order Management
    { id: 'orders.view', name: 'عرض الطلبات', description: 'عرض جميع الطلبات', category: 'إدارة الطلبات', icon: <ShoppingBag className="w-4 h-4" /> },
    { id: 'orders.track', name: 'تتبع الطلبات', description: 'تتبع حالة الطلبات', category: 'إدارة الطلبات', icon: <Eye className="w-4 h-4" /> },
    { id: 'orders.cancel', name: 'إلغاء الطلبات', description: 'إلغاء الطلبات المتأخرة', category: 'إدارة الطلبات', icon: <X className="w-4 h-4" /> },
    { id: 'orders.refund', name: 'استرداد الطلبات', description: 'معالجة استرداد الأموال', category: 'إدارة الطلبات', icon: <DollarSign className="w-4 h-4" /> },
    
    // Support Management
    { id: 'support.view', name: 'عرض الدعم', description: 'عرض رسائل الدعم', category: 'إدارة الدعم', icon: <MessageCircle className="w-4 h-4" /> },
    { id: 'support.respond', name: 'الرد على الدعم', description: 'الرد على رسائل العملاء', category: 'إدارة الدعم', icon: <Mail className="w-4 h-4" /> },
    { id: 'support.escalate', name: 'تصعيد الدعم', description: 'تصعيد المشاكل المعقدة', category: 'إدارة الدعم', icon: <Bell className="w-4 h-4" /> },
    
    // Analytics & Reports
    { id: 'analytics.view', name: 'عرض التحليلات', description: 'عرض التحليلات والإحصائيات', category: 'التحليلات والتقارير', icon: <BarChart3 className="w-4 h-4" /> },
    { id: 'reports.generate', name: 'إنشاء التقارير', description: 'إنشاء التقارير المفصلة', category: 'التحليلات والتقارير', icon: <FileText className="w-4 h-4" /> },
    { id: 'reports.export', name: 'تصدير التقارير', description: 'تصدير التقارير بصيغ مختلفة', category: 'التحليلات والتقارير', icon: <FileText className="w-4 h-4" /> },
    
    // System Settings
    { id: 'settings.view', name: 'عرض الإعدادات', description: 'عرض إعدادات النظام', category: 'إعدادات النظام', icon: <Settings className="w-4 h-4" /> },
    { id: 'settings.edit', name: 'تعديل الإعدادات', description: 'تعديل إعدادات النظام', category: 'إعدادات النظام', icon: <Settings className="w-4 h-4" /> },
    { id: 'settings.backup', name: 'النسخ الاحتياطي', description: 'إنشاء واستعادة النسخ الاحتياطية', category: 'إعدادات النظام', icon: <Database className="w-4 h-4" /> }
  ];

  // Predefined roles
  const roles: Role[] = [
    {
      id: 'super-admin',
      name: 'السوبر أدمن',
      nameEn: 'Super Admin',
      description: 'صلاحيات كاملة على كل شيء داخل النظام',
      permissions: permissions.map(p => p.id), // All permissions
      userCount: 1,
      color: 'from-red-500 to-red-600',
      icon: <Crown className="w-6 h-6" />,
      isSystem: true
    },
    {
      id: 'restaurant-manager',
      name: 'مدير المطاعم',
      nameEn: 'Restaurant Manager',
      description: 'يدير كل ما يخص المطاعم المسجلة في المنصة',
      permissions: [
        'restaurants.view', 'restaurants.approve', 'restaurants.edit', 'restaurants.revenue',
        'analytics.view', 'reports.generate'
      ],
      userCount: 3,
      color: 'from-blue-500 to-blue-600',
      icon: <Users className="w-6 h-6" />,
      isSystem: true
    },
    {
      id: 'drivers-manager',
      name: 'مدير السائقين',
      nameEn: 'Drivers Manager',
      description: 'متخصص في متابعة وتوظيف السائقين',
      permissions: [
        'drivers.view', 'drivers.approve', 'drivers.suspend', 'drivers.revenue',
        'analytics.view'
      ],
      userCount: 2,
      color: 'from-green-500 to-green-600',
      icon: <Car className="w-6 h-6" />,
      isSystem: true
    },
    {
      id: 'support-manager',
      name: 'مدير الدعم',
      nameEn: 'Support Manager',
      description: 'يرد على شكاوى واستفسارات المستخدمين',
      permissions: [
        'support.view', 'support.respond', 'support.escalate',
        'users.view'
      ],
      userCount: 5,
      color: 'from-purple-500 to-purple-600',
      icon: <MessageCircle className="w-6 h-6" />,
      isSystem: true
    },
    {
      id: 'orders-manager',
      name: 'مدير الطلبات',
      nameEn: 'Orders Manager',
      description: 'يراقب حركة الطلبات وجودة الخدمة',
      permissions: [
        'orders.view', 'orders.track', 'orders.cancel', 'orders.refund',
        'analytics.view'
      ],
      userCount: 4,
      color: 'from-orange-500 to-orange-600',
      icon: <ShoppingBag className="w-6 h-6" />,
      isSystem: true
    }
  ];

  const groupedPermissions = permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  const handleCreateRole = () => {
    setIsCreateModalOpen(true);
  };

  const handleEditRole = (roleId: string) => {
    setSelectedRole(roleId);
    setIsEditModalOpen(true);
  };

  const handleDeleteRole = (roleId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الدور؟')) {
      // Handle delete logic
      console.log('Deleting role:', roleId);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white rtl-font">
            إدارة الأدوار والصلاحيات
          </h1>
          <p className="text-gray-600 dark:text-gray-400 rtl-font">
            إنشاء وتعديل أدوار المستخدمين وتحديد صلاحياتهم
          </p>
        </div>
        <button 
          onClick={handleCreateRole}
          className="bg-primary text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary/90 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
        >
          <Plus className="w-5 h-5" />
          <span>إنشاء دور جديد</span>
        </button>
      </div>

      {/* Roles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {roles.map((role) => (
          <div key={role.id} className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
            {/* Role Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className={`w-12 h-12 bg-gradient-to-br ${role.color} rounded-xl flex items-center justify-center text-white`}>
                  {role.icon}
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
                    {role.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {role.nameEn}
                  </p>
                </div>
              </div>
              
              {!role.isSystem && (
                <div className="flex space-x-1 rtl:space-x-reverse">
                  <button 
                    onClick={() => handleEditRole(role.id)}
                    className="w-8 h-8 flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button 
                    onClick={() => handleDeleteRole(role.id)}
                    className="w-8 h-8 flex items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>

            {/* Role Description */}
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 rtl-font">
              {role.description}
            </p>

            {/* Role Stats */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                <Users className="w-4 h-4" />
                <span>{role.userCount} مستخدم</span>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                <Shield className="w-4 h-4" />
                <span>{role.permissions.length} صلاحية</span>
              </div>
            </div>

            {/* System Badge */}
            {role.isSystem && (
              <div className="inline-flex items-center space-x-1 rtl:space-x-reverse bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-lg text-xs font-medium mb-4">
                <Lock className="w-3 h-3" />
                <span>دور النظام</span>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-2 rtl:space-x-reverse">
              <button className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse">
                <Eye className="w-4 h-4" />
                <span className="text-sm font-medium">عرض التفاصيل</span>
              </button>
              {!role.isSystem && (
                <button 
                  onClick={() => handleEditRole(role.id)}
                  className="flex-1 bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse"
                >
                  <Edit className="w-4 h-4" />
                  <span className="text-sm font-medium">تعديل</span>
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Permissions Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 rtl-font">
          نظرة عامة على الصلاحيات
        </h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
            <div key={category} className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white rtl-font">
                {category}
              </h3>
              <div className="space-y-2">
                {categoryPermissions.map((permission) => (
                  <div key={permission.id} className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-gray-600 dark:text-gray-400">
                      {permission.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-800 dark:text-white rtl-font">
                        {permission.name}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                        {permission.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Create Role Modal */}
      {isCreateModalOpen && (
        <CreateRoleModal 
          permissions={permissions}
          groupedPermissions={groupedPermissions}
          onClose={() => setIsCreateModalOpen(false)}
        />
      )}

      {/* Edit Role Modal */}
      {isEditModalOpen && selectedRole && (
        <EditRoleModal 
          role={roles.find(r => r.id === selectedRole)!}
          permissions={permissions}
          groupedPermissions={groupedPermissions}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedRole(null);
          }}
        />
      )}
    </div>
  );
};

// Create Role Modal Component
const CreateRoleModal = ({ permissions, groupedPermissions, onClose }: {
  permissions: Permission[];
  groupedPermissions: Record<string, Permission[]>;
  onClose: () => void;
}) => {
  const [formData, setFormData] = useState({
    name: '',
    nameEn: '',
    description: '',
    color: 'from-blue-500 to-blue-600',
    permissions: [] as string[]
  });

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId]
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Creating role:', formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />
      
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-4xl bg-white dark:bg-gray-800 rounded-3xl shadow-2xl">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">
              إنشاء دور جديد
            </h3>
          </div>
          
          <form onSubmit={handleSubmit} className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                  اسم الدور (عربي)
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  اسم الدور (إنجليزي)
                </label>
                <input
                  type="text"
                  value={formData.nameEn}
                  onChange={(e) => setFormData(prev => ({ ...prev, nameEn: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                  required
                />
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                وصف الدور
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                required
              />
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4 rtl-font">
                الصلاحيات
              </label>
              
              <div className="space-y-6">
                {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                  <div key={category} className="border border-gray-200 dark:border-gray-600 rounded-xl p-4">
                    <h4 className="font-semibold text-gray-800 dark:text-white mb-3 rtl-font">
                      {category}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {categoryPermissions.map((permission) => (
                        <label key={permission.id} className="flex items-center space-x-3 rtl:space-x-reverse cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.permissions.includes(permission.id)}
                            onChange={() => handlePermissionToggle(permission.id)}
                            className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                          />
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <div className="text-gray-600 dark:text-gray-400">
                              {permission.icon}
                            </div>
                            <div>
                              <span className="text-sm font-medium text-gray-800 dark:text-white rtl-font">
                                {permission.name}
                              </span>
                              <p className="text-xs text-gray-600 dark:text-gray-400 rtl-font">
                                {permission.description}
                              </p>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-4 rtl:space-x-reverse">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary/90 transition-colors"
              >
                إنشاء الدور
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Edit Role Modal Component (similar to Create but with pre-filled data)
const EditRoleModal = ({ role, permissions, groupedPermissions, onClose }: {
  role: Role;
  permissions: Permission[];
  groupedPermissions: Record<string, Permission[]>;
  onClose: () => void;
}) => {
  const [formData, setFormData] = useState({
    name: role.name,
    nameEn: role.nameEn,
    description: role.description,
    color: role.color,
    permissions: [...role.permissions]
  });

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId]
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Updating role:', formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />
      
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-4xl bg-white dark:bg-gray-800 rounded-3xl shadow-2xl">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">
              تعديل الدور: {role.name}
            </h3>
          </div>
          
          <form onSubmit={handleSubmit} className="p-6">
            {/* Same form structure as CreateRoleModal but with pre-filled values */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                  اسم الدور (عربي)
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  اسم الدور (إنجليزي)
                </label>
                <input
                  type="text"
                  value={formData.nameEn}
                  onChange={(e) => setFormData(prev => ({ ...prev, nameEn: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                  required
                />
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                وصف الدور
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                required
              />
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4 rtl-font">
                الصلاحيات
              </label>
              
              <div className="space-y-6">
                {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                  <div key={category} className="border border-gray-200 dark:border-gray-600 rounded-xl p-4">
                    <h4 className="font-semibold text-gray-800 dark:text-white mb-3 rtl-font">
                      {category}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {categoryPermissions.map((permission) => (
                        <label key={permission.id} className="flex items-center space-x-3 rtl:space-x-reverse cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.permissions.includes(permission.id)}
                            onChange={() => handlePermissionToggle(permission.id)}
                            className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                          />
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <div className="text-gray-600 dark:text-gray-400">
                              {permission.icon}
                            </div>
                            <div>
                              <span className="text-sm font-medium text-gray-800 dark:text-white rtl-font">
                                {permission.name}
                              </span>
                              <p className="text-xs text-gray-600 dark:text-gray-400 rtl-font">
                                {permission.description}
                              </p>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-4 rtl:space-x-reverse">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary/90 transition-colors"
              >
                حفظ التغييرات
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RoleManagement;