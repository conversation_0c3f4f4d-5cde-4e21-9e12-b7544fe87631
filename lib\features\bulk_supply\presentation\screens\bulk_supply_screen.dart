import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/product_grid_widget.dart';
import '../widgets/cart_panel_widget.dart';
import '../widgets/order_history_widget.dart';

class BulkSupplyScreen extends StatefulWidget {
  const BulkSupplyScreen({super.key});

  @override
  State<BulkSupplyScreen> createState() => _BulkSupplyScreenState();
}

class _BulkSupplyScreenState extends State<BulkSupplyScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String searchQuery = '';
  String selectedCategory = 'All';
  List<CartItem> cartItems = [];

  final List<String> categories = [
    'All',
    'Vegetables',
    'Meat',
    'Packaging',
    'Drinks',
    'Kitchen Tools'
  ];

  final List<BulkProduct> products = [
    BulkProduct(
      id: '1',
      name: 'Fresh Tomatoes',
      category: 'Vegetables',
      quantity: '10kg',
      price: 25.99,
      image: 'assets/images/tomatoes.png',
      description: 'Fresh organic tomatoes',
      inStock: true,
    ),
    BulkProduct(
      id: '2',
      name: 'Premium Beef',
      category: 'Meat',
      quantity: '5kg',
      price: 89.99,
      image: 'assets/images/beef.png',
      description: 'Premium quality beef cuts',
      inStock: true,
    ),
    BulkProduct(
      id: '3',
      name: 'Food Containers',
      category: 'Packaging',
      quantity: '100 pieces',
      price: 15.99,
      image: 'assets/images/containers.png',
      description: 'Disposable food containers',
      inStock: true,
    ),
    BulkProduct(
      id: '4',
      name: 'Soft Drinks',
      category: 'Drinks',
      quantity: '24 cans',
      price: 12.99,
      image: 'assets/images/drinks.png',
      description: 'Assorted soft drinks',
      inStock: false,
    ),
    BulkProduct(
      id: '5',
      name: 'Chef Knives Set',
      category: 'Kitchen Tools',
      quantity: '5 pieces',
      price: 45.99,
      image: 'assets/images/knives.png',
      description: 'Professional chef knives',
      inStock: true,
    ),
    BulkProduct(
      id: '6',
      name: 'Fresh Onions',
      category: 'Vegetables',
      quantity: '8kg',
      price: 18.99,
      image: 'assets/images/onions.png',
      description: 'Fresh white onions',
      inStock: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F231A),
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Tab Bar
                _buildTabBar(),
                
                // Content
                Expanded(
                  child: Row(
                    children: [
                      // Main Content Area
                      Expanded(
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            // Products Tab
                            Column(
                              children: [
                                // Search and Filters
                                _buildSearchAndFilters(),
                                
                                // Products Grid
                                Expanded(
                                  child: ProductGridWidget(
                                    products: _getFilteredProducts(),
                                    onAddToCart: _addToCart,
                                  ),
                                ),
                              ],
                            ),
                            
                            // Order History Tab
                            const OrderHistoryWidget(),
                          ],
                        ),
                      ),
                      
                      // Cart Panel
                      if (cartItems.isNotEmpty)
                        CartPanelWidget(
                          cartItems: cartItems,
                          onUpdateQuantity: _updateCartQuantity,
                          onRemoveItem: _removeFromCart,
                          onConfirmOrder: _confirmOrder,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: Color(0xFF1B3B2E),
        border: Border(
          bottom: BorderSide(
            color: Color(0xFF11B96F),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Icon(
                Icons.inventory_2_outlined,
                color: const Color(0xFF11B96F),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Bulk Supply Ordering',
                style: GoogleFonts.tajawal(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          
          const Spacer(),
          
          // Cart Summary
          if (cartItems.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: const Color(0xFF11B96F).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: const Color(0xFF11B96F),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.shopping_cart,
                    color: const Color(0xFF11B96F),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${cartItems.length} items - \$${_getTotalPrice().toStringAsFixed(2)}',
                    style: GoogleFonts.tajawal(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: const Color(0xFF1B3B2E),
      child: TabBar(
        controller: _tabController,
        indicatorColor: const Color(0xFF11B96F),
        indicatorWeight: 3,
        labelColor: const Color(0xFF11B96F),
        unselectedLabelColor: Colors.grey[400],
        labelStyle: GoogleFonts.tajawal(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.tajawal(
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
        tabs: const [
          Tab(
            icon: Icon(Icons.inventory),
            text: 'Browse Products',
          ),
          Tab(
            icon: Icon(Icons.history),
            text: 'Order History',
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(24),
      color: const Color(0xFF0F231A),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF1B3B2E),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF11B96F).withValues(alpha: 0.3),
              ),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              style: GoogleFonts.tajawal(
                color: Colors.white,
                fontSize: 16,
              ),
              decoration: InputDecoration(
                hintText: 'Search products...',
                hintStyle: GoogleFonts.tajawal(
                  color: Colors.grey[400],
                  fontSize: 16,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: const Color(0xFF11B96F),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Category Filters
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: categories.map((category) {
                final isSelected = selectedCategory == category;
                return Container(
                  margin: const EdgeInsets.only(right: 12),
                  child: FilterChip(
                    label: Text(
                      category,
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isSelected ? const Color(0xFF0F231A) : Colors.white,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        selectedCategory = category;
                      });
                    },
                    backgroundColor: const Color(0xFF1B3B2E),
                    selectedColor: const Color(0xFF11B96F),
                    checkmarkColor: const Color(0xFF0F231A),
                    side: BorderSide(
                      color: isSelected 
                          ? const Color(0xFF11B96F) 
                          : const Color(0xFF11B96F).withValues(alpha: 0.3),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  List<BulkProduct> _getFilteredProducts() {
    List<BulkProduct> filtered = products;

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((product) =>
          product.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          product.description.toLowerCase().contains(searchQuery.toLowerCase())).toList();
    }

    // Apply category filter
    if (selectedCategory != 'All') {
      filtered = filtered.where((product) => product.category == selectedCategory).toList();
    }

    return filtered;
  }

  void _addToCart(BulkProduct product) {
    setState(() {
      final existingIndex = cartItems.indexWhere((item) => item.product.id == product.id);
      if (existingIndex >= 0) {
        cartItems[existingIndex].quantity++;
      } else {
        cartItems.add(CartItem(product: product, quantity: 1));
      }
    });
  }

  void _updateCartQuantity(String productId, int quantity) {
    setState(() {
      final index = cartItems.indexWhere((item) => item.product.id == productId);
      if (index >= 0) {
        if (quantity <= 0) {
          cartItems.removeAt(index);
        } else {
          cartItems[index].quantity = quantity;
        }
      }
    });
  }

  void _removeFromCart(String productId) {
    setState(() {
      cartItems.removeWhere((item) => item.product.id == productId);
    });
  }

  void _confirmOrder() {
    // Handle order confirmation
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1B3B2E),
        title: Text(
          'Order Confirmed',
          style: GoogleFonts.tajawal(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Your bulk supply order has been confirmed and will be processed shortly.',
          style: GoogleFonts.tajawal(
            color: Colors.grey[300],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                cartItems.clear();
              });
            },
            child: Text(
              'OK',
              style: GoogleFonts.tajawal(
                color: const Color(0xFF11B96F),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getTotalPrice() {
    return cartItems.fold(0.0, (total, item) => total + (item.product.price * item.quantity));
  }
}

class BulkProduct {
  final String id;
  final String name;
  final String category;
  final String quantity;
  final double price;
  final String image;
  final String description;
  final bool inStock;

  BulkProduct({
    required this.id,
    required this.name,
    required this.category,
    required this.quantity,
    required this.price,
    required this.image,
    required this.description,
    required this.inStock,
  });
}

class CartItem {
  final BulkProduct product;
  int quantity;

  CartItem({
    required this.product,
    required this.quantity,
  });
}
