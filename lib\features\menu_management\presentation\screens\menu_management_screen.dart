import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/menu_item_card.dart';
import '../widgets/add_menu_item_dialog.dart';
import '../widgets/category_management_widget.dart';

class MenuManagementScreen extends StatefulWidget {
  const MenuManagementScreen({super.key});

  @override
  State<MenuManagementScreen> createState() => _MenuManagementScreenState();
}

class _MenuManagementScreenState extends State<MenuManagementScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String searchQuery = '';
  String selectedCategory = 'All';

  final List<String> categories = [
    'All',
    'Appetizers',
    'Main Course',
    'Desserts',
    'Beverages',
    'Salads',
    'Pizza',
    'Burgers'
  ];

  List<MenuItemModel> menuItems = [
    MenuItemModel(
      id: '1',
      name: 'Grilled Chicken Breast',
      category: 'Main Course',
      price: 18.99,
      description: 'Tender grilled chicken breast with herbs and spices',
      isAvailable: true,
      preparationTime: 15,
      ingredients: ['Chicken', 'Herbs', 'Olive Oil', 'Garlic'],
      calories: 250,
    ),
    MenuItemModel(
      id: '2',
      name: 'Caesar Salad',
      category: 'Salads',
      price: 12.99,
      description: 'Fresh romaine lettuce with caesar dressing and croutons',
      isAvailable: true,
      preparationTime: 8,
      ingredients: ['Romaine Lettuce', 'Parmesan', 'Croutons', 'Caesar Dressing'],
      calories: 180,
    ),
    MenuItemModel(
      id: '3',
      name: 'Margherita Pizza',
      category: 'Pizza',
      price: 16.99,
      description: 'Classic pizza with tomato sauce, mozzarella and basil',
      isAvailable: true,
      preparationTime: 20,
      ingredients: ['Pizza Dough', 'Tomato Sauce', 'Mozzarella', 'Basil'],
      calories: 320,
    ),
    MenuItemModel(
      id: '4',
      name: 'Fresh Orange Juice',
      category: 'Beverages',
      price: 4.99,
      description: 'Freshly squeezed orange juice',
      isAvailable: true,
      preparationTime: 3,
      ingredients: ['Fresh Oranges'],
      calories: 110,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Tab Bar
                _buildTabBar(),
                
                // Content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // Menu Items Tab
                      _buildMenuItemsTab(),
                      
                      // Categories Tab
                      CategoryManagementWidget(
                        categories: categories,
                        onCategoryAdded: _addCategory,
                        onCategoryRemoved: _removeCategory,
                        onCategoryRenamed: _renameCategory,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _tabController.index == 0 ? FloatingActionButton.extended(
        onPressed: _showAddMenuItemDialog,
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        icon: const Icon(Icons.add),
        label: Text(
          'Add Menu Item',
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ) : null,
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Icon(
                Icons.menu_book,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Menu Management',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          if (_tabController.index == 0)
            Container(
              width: 300,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.greyLight,
                borderRadius: BorderRadius.circular(20),
              ),
              child: TextField(
                onChanged: (value) {
                  setState(() {
                    searchQuery = value;
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Search menu items...',
                  hintStyle: GoogleFonts.inter(
                    color: AppColors.textHint,
                    fontSize: 14,
                  ),
                  prefixIcon: const Icon(
                    Icons.search,
                    color: AppColors.textHint,
                    size: 20,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
              ),
            ),
          
          const SizedBox(width: 16),
          
          // Stats
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppColors.primary,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.restaurant_menu,
                  color: AppColors.primary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${menuItems.length} Items',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.white,
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
        onTap: (index) {
          setState(() {}); // Refresh to update FAB visibility
        },
        tabs: const [
          Tab(
            icon: Icon(Icons.restaurant_menu),
            text: 'Menu Items',
          ),
          Tab(
            icon: Icon(Icons.category),
            text: 'Categories',
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItemsTab() {
    return Column(
      children: [
        // Category Filters
        _buildCategoryFilters(),
        
        // Menu Items Grid
        Expanded(
          child: _buildMenuItemsGrid(),
        ),
      ],
    );
  }

  Widget _buildCategoryFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: categories.map((category) {
            final isSelected = selectedCategory == category;
            return Container(
              margin: const EdgeInsets.only(right: 12),
              child: FilterChip(
                label: Text(
                  category,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? AppColors.white : AppColors.textSecondary,
                  ),
                ),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    selectedCategory = category;
                  });
                },
                backgroundColor: AppColors.white,
                selectedColor: AppColors.primary,
                checkmarkColor: AppColors.white,
                side: BorderSide(
                  color: isSelected ? AppColors.primary : AppColors.inputBorder,
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildMenuItemsGrid() {
    final filteredItems = _getFilteredMenuItems();
    
    if (filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.restaurant_menu,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No menu items found',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add your first menu item to get started',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }
    
    return Container(
      color: AppColors.backgroundLight,
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 0.75,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: filteredItems.length,
        itemBuilder: (context, index) {
          final item = filteredItems[index];
          return MenuItemCard(
            menuItem: item,
            onEdit: () => _editMenuItem(item),
            onDelete: () => _deleteMenuItem(item.id),
            onToggleAvailability: () => _toggleAvailability(item.id),
          );
        },
      ),
    );
  }

  List<MenuItemModel> _getFilteredMenuItems() {
    List<MenuItemModel> filtered = menuItems;

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((item) =>
          item.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().contains(searchQuery.toLowerCase())).toList();
    }

    // Apply category filter
    if (selectedCategory != 'All') {
      filtered = filtered.where((item) => item.category == selectedCategory).toList();
    }

    return filtered;
  }

  void _showAddMenuItemDialog() {
    showDialog(
      context: context,
      builder: (context) => AddMenuItemDialog(
        categories: categories.where((c) => c != 'All').toList(),
        onItemAdded: _addMenuItem,
      ),
    );
  }

  void _addMenuItem(MenuItemModel item) {
    setState(() {
      menuItems.add(item);
    });
  }

  void _editMenuItem(MenuItemModel item) {
    showDialog(
      context: context,
      builder: (context) => AddMenuItemDialog(
        categories: categories.where((c) => c != 'All').toList(),
        menuItem: item,
        onItemAdded: (updatedItem) {
          setState(() {
            final index = menuItems.indexWhere((i) => i.id == item.id);
            if (index >= 0) {
              menuItems[index] = updatedItem;
            }
          });
        },
      ),
    );
  }

  void _deleteMenuItem(String itemId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Menu Item',
          style: GoogleFonts.inter(fontWeight: FontWeight.w600),
        ),
        content: Text(
          'Are you sure you want to delete this menu item?',
          style: GoogleFonts.inter(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.inter(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                menuItems.removeWhere((item) => item.id == itemId);
              });
              Navigator.of(context).pop();
            },
            child: Text(
              'Delete',
              style: GoogleFonts.inter(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleAvailability(String itemId) {
    setState(() {
      final index = menuItems.indexWhere((item) => item.id == itemId);
      if (index >= 0) {
        menuItems[index] = menuItems[index].copyWith(
          isAvailable: !menuItems[index].isAvailable,
        );
      }
    });
  }

  void _addCategory(String category) {
    setState(() {
      categories.add(category);
    });
  }

  void _removeCategory(String category) {
    setState(() {
      categories.remove(category);
      // Update menu items that use this category
      for (int i = 0; i < menuItems.length; i++) {
        if (menuItems[i].category == category) {
          menuItems[i] = menuItems[i].copyWith(category: 'Main Course');
        }
      }
    });
  }

  void _renameCategory(String oldName, String newName) {
    setState(() {
      final index = categories.indexOf(oldName);
      if (index >= 0) {
        categories[index] = newName;
        // Update menu items that use this category
        for (int i = 0; i < menuItems.length; i++) {
          if (menuItems[i].category == oldName) {
            menuItems[i] = menuItems[i].copyWith(category: newName);
          }
        }
      }
    });
  }
}

class MenuItemModel {
  final String id;
  final String name;
  final String category;
  final double price;
  final String description;
  final bool isAvailable;
  final int preparationTime;
  final List<String> ingredients;
  final int calories;

  MenuItemModel({
    required this.id,
    required this.name,
    required this.category,
    required this.price,
    required this.description,
    required this.isAvailable,
    required this.preparationTime,
    required this.ingredients,
    required this.calories,
  });

  MenuItemModel copyWith({
    String? id,
    String? name,
    String? category,
    double? price,
    String? description,
    bool? isAvailable,
    int? preparationTime,
    List<String>? ingredients,
    int? calories,
  }) {
    return MenuItemModel(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      price: price ?? this.price,
      description: description ?? this.description,
      isAvailable: isAvailable ?? this.isAvailable,
      preparationTime: preparationTime ?? this.preparationTime,
      ingredients: ingredients ?? this.ingredients,
      calories: calories ?? this.calories,
    );
  }
}
