import React, { useState } from 'react';
import { 
  FileText, 
  Image, 
  Video, 
  Edit, 
  Trash2, 
  Plus, 
  Eye, 
  Upload,
  Save,
  X,
  Globe,
  Smartphone,
  Monitor,
  Megaphone,
  Star,
  Calendar,
  Tag,
  Search,
  Filter,
  MoreVertical,
  Copy,
  Share2,
  Download
} from 'lucide-react';

interface ContentItem {
  id: string;
  title: string;
  type: 'banner' | 'promotion' | 'blog' | 'notification' | 'page';
  status: 'published' | 'draft' | 'scheduled';
  content: string;
  imageUrl?: string;
  publishDate: string;
  author: string;
  views: number;
  platform: 'web' | 'mobile' | 'both';
  tags: string[];
}

const ContentManagement = () => {
  const [contents, setContents] = useState<ContentItem[]>([
    {
      id: '1',
      title: 'عرض خاص - خصم 30% على جميع المطاعم',
      type: 'banner',
      status: 'published',
      content: 'استمتع بخصم 30% على جميع طلباتك من المطاعم المشاركة',
      imageUrl: 'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg',
      publishDate: '2024-01-15',
      author: 'فريق التسويق',
      views: 15420,
      platform: 'both',
      tags: ['عرض', 'خصم', 'مطاعم']
    },
    {
      id: '2',
      title: 'كيفية استخدام تطبيق وصلتي',
      type: 'blog',
      status: 'published',
      content: 'دليل شامل لاستخدام تطبيق وصلتي وطلب الطعام بسهولة',
      publishDate: '2024-01-10',
      author: 'فريق المحتوى',
      views: 8750,
      platform: 'web',
      tags: ['دليل', 'تطبيق', 'مساعدة']
    },
    {
      id: '3',
      title: 'إشعار: تحديث جديد للتطبيق',
      type: 'notification',
      status: 'scheduled',
      content: 'تحديث جديد متاح الآن مع ميزات محسنة وواجهة أفضل',
      publishDate: '2024-01-25',
      author: 'فريق التطوير',
      views: 0,
      platform: 'mobile',
      tags: ['تحديث', 'ميزات', 'تطبيق']
    }
  ]);

  const [selectedContent, setSelectedContent] = useState<ContentItem | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'banner': return <Image className="w-4 h-4" />;
      case 'promotion': return <Megaphone className="w-4 h-4" />;
      case 'blog': return <FileText className="w-4 h-4" />;
      case 'notification': return <Star className="w-4 h-4" />;
      case 'page': return <Globe className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'banner': return 'بانر';
      case 'promotion': return 'عرض ترويجي';
      case 'blog': return 'مقال';
      case 'notification': return 'إشعار';
      case 'page': return 'صفحة';
      default: return type;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'published': return 'منشور';
      case 'draft': return 'مسودة';
      case 'scheduled': return 'مجدول';
      default: return status;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'web': return <Monitor className="w-4 h-4" />;
      case 'mobile': return <Smartphone className="w-4 h-4" />;
      case 'both': return <Globe className="w-4 h-4" />;
      default: return <Globe className="w-4 h-4" />;
    }
  };

  const filteredContents = contents.filter(content => {
    const matchesType = filterType === 'all' || content.type === filterType;
    const matchesStatus = filterStatus === 'all' || content.status === filterStatus;
    const matchesSearch = content.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         content.content.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesType && matchesStatus && matchesSearch;
  });

  const deleteContent = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المحتوى؟')) {
      setContents(prev => prev.filter(content => content.id !== id));
    }
  };

  const duplicateContent = (content: ContentItem) => {
    const newContent = {
      ...content,
      id: Date.now().toString(),
      title: `نسخة من ${content.title}`,
      status: 'draft' as const,
      views: 0,
      publishDate: new Date().toISOString().split('T')[0]
    };
    setContents(prev => [newContent, ...prev]);
  };

  const toggleStatus = (id: string) => {
    setContents(prev => prev.map(content => 
      content.id === id 
        ? { 
            ...content, 
            status: content.status === 'published' ? 'draft' : 'published'
          }
        : content
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">إدارة المحتوى</h2>
          <p className="text-gray-600 rtl-font">إدارة محتوى الموقع والتطبيق والإشعارات</p>
        </div>
        
        <button
          onClick={() => setShowEditor(true)}
          className="flex items-center space-x-2 rtl:space-x-reverse bg-primary text-white px-6 py-3 rounded-xl hover:bg-primary/90 transition-colors"
        >
          <Plus className="w-5 h-5" />
          <span className="font-medium rtl-font">إضافة محتوى جديد</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">المحتوى المنشور</p>
              <p className="text-2xl font-bold text-green-600">
                {contents.filter(c => c.status === 'published').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <Eye className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">المسودات</p>
              <p className="text-2xl font-bold text-gray-600">
                {contents.filter(c => c.status === 'draft').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
              <Edit className="w-6 h-6 text-gray-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">المجدول</p>
              <p className="text-2xl font-bold text-blue-600">
                {contents.filter(c => c.status === 'scheduled').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">إجمالي المشاهدات</p>
              <p className="text-2xl font-bold text-purple-600">
                {contents.reduce((sum, c) => sum + c.views, 0).toLocaleString()}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Eye className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في المحتوى..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
              />
            </div>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
            >
              <option value="all">جميع الأنواع</option>
              <option value="banner">بانر</option>
              <option value="promotion">عرض ترويجي</option>
              <option value="blog">مقال</option>
              <option value="notification">إشعار</option>
              <option value="page">صفحة</option>
            </select>
            
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
            >
              <option value="all">جميع الحالات</option>
              <option value="published">منشور</option>
              <option value="draft">مسودة</option>
              <option value="scheduled">مجدول</option>
            </select>
          </div>
          
          <div className="text-sm text-gray-600 rtl-font">
            عرض {filteredContents.length} من {contents.length} عنصر
          </div>
        </div>
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredContents.map((content) => (
          <div key={content.id} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
            {/* Image */}
            {content.imageUrl && (
              <div className="h-48 bg-gray-200 overflow-hidden">
                <img 
                  src={content.imageUrl} 
                  alt={content.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            
            {/* Content */}
            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  {getTypeIcon(content.type)}
                  <span className="text-sm text-gray-600 rtl-font">{getTypeLabel(content.type)}</span>
                </div>
                
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  {getPlatformIcon(content.platform)}
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(content.status)}`}>
                    {getStatusLabel(content.status)}
                  </span>
                </div>
              </div>
              
              <h3 className="font-semibold text-gray-900 mb-2 rtl-font line-clamp-2">
                {content.title}
              </h3>
              
              <p className="text-gray-600 text-sm mb-4 rtl-font line-clamp-3">
                {content.content}
              </p>
              
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span className="rtl-font">{content.author}</span>
                <span>{new Date(content.publishDate).toLocaleDateString('ar-MA')}</span>
              </div>
              
              {content.views > 0 && (
                <div className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
                  <Eye className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-500">{content.views.toLocaleString()} مشاهدة</span>
                </div>
              )}
              
              {/* Tags */}
              {content.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {content.tags.map((tag, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full rtl-font">
                      {tag}
                    </span>
                  ))}
                </div>
              )}
              
              {/* Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <button
                    onClick={() => setSelectedContent(content)}
                    className="text-blue-600 hover:text-blue-800"
                    title="عرض"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <button
                    className="text-green-600 hover:text-green-800"
                    title="تعديل"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => duplicateContent(content)}
                    className="text-purple-600 hover:text-purple-800"
                    title="نسخ"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => deleteContent(content.id)}
                    className="text-red-600 hover:text-red-800"
                    title="حذف"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
                
                <button
                  onClick={() => toggleStatus(content.id)}
                  className={`px-3 py-1 rounded-lg text-xs font-medium transition-colors ${
                    content.status === 'published' 
                      ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                      : 'bg-green-100 text-green-700 hover:bg-green-200'
                  }`}
                >
                  {content.status === 'published' ? 'إلغاء النشر' : 'نشر'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Content Details Modal */}
      {selectedContent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800 rtl-font">تفاصيل المحتوى</h3>
              <button
                onClick={() => setSelectedContent(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="space-y-6">
              {selectedContent.imageUrl && (
                <div className="w-full h-64 bg-gray-200 rounded-xl overflow-hidden">
                  <img 
                    src={selectedContent.imageUrl} 
                    alt={selectedContent.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">العنوان</label>
                  <p className="text-gray-900 rtl-font">{selectedContent.title}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">النوع</label>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    {getTypeIcon(selectedContent.type)}
                    <span className="text-gray-900 rtl-font">{getTypeLabel(selectedContent.type)}</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">الحالة</label>
                  <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedContent.status)}`}>
                    {getStatusLabel(selectedContent.status)}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">المنصة</label>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    {getPlatformIcon(selectedContent.platform)}
                    <span className="text-gray-900">
                      {selectedContent.platform === 'web' ? 'الموقع' : 
                       selectedContent.platform === 'mobile' ? 'التطبيق' : 'الكل'}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">المؤلف</label>
                  <p className="text-gray-900 rtl-font">{selectedContent.author}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">تاريخ النشر</label>
                  <p className="text-gray-900">{new Date(selectedContent.publishDate).toLocaleDateString('ar-MA')}</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">المحتوى</label>
                <div className="bg-gray-50 p-4 rounded-xl">
                  <p className="text-gray-900 rtl-font whitespace-pre-wrap">{selectedContent.content}</p>
                </div>
              </div>
              
              {selectedContent.tags.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 rtl-font">العلامات</label>
                  <div className="flex flex-wrap gap-2">
                    {selectedContent.tags.map((tag, index) => (
                      <span key={index} className="px-3 py-1 bg-primary/10 text-primary text-sm rounded-full rtl-font">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              {selectedContent.views > 0 && (
                <div className="bg-blue-50 p-4 rounded-xl">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Eye className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-800 rtl-font">
                      {selectedContent.views.toLocaleString()} مشاهدة
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentManagement;
