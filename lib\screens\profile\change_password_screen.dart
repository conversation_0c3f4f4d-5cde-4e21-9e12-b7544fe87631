import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Change Password screen for driver
class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _isLoading = false;
  bool _showCurrentPassword = false;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.changePassword,
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const SizedBox(height: 20),
              
              // Security Icon
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const Icon(
                  Icons.security,
                  size: 50,
                  color: Color(0xFF11B96F),
                ),
              ),
              
              const SizedBox(height: 20),
              
              Text(
                languageProvider.getText(
                  'Update your password to keep your account secure',
                  'قم بتحديث كلمة المرور للحفاظ على أمان حسابك'
                ),
                style: DriverTypography.getContextualStyle(
                  context,
                  fontSize: DriverTypography.bodyLarge,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 40),
              
              // Password Fields
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageProvider.getText('Password Information', 'معلومات كلمة المرور'),
                      style: DriverTypography.getContextualStyle(
                        context,
                        fontSize: DriverTypography.titleMedium,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF11B96F),
                      ),
                    ),
                    const SizedBox(height: 20),
                    
                    // Current Password
                    _buildPasswordField(
                      controller: _currentPasswordController,
                      label: languageProvider.getText('Current Password', 'كلمة المرور الحالية'),
                      isVisible: _showCurrentPassword,
                      onToggleVisibility: () => setState(() => _showCurrentPassword = !_showCurrentPassword),
                      isDark: isDark,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // New Password
                    _buildPasswordField(
                      controller: _newPasswordController,
                      label: languageProvider.getText('New Password', 'كلمة المرور الجديدة'),
                      isVisible: _showNewPassword,
                      onToggleVisibility: () => setState(() => _showNewPassword = !_showNewPassword),
                      isDark: isDark,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Confirm Password
                    _buildPasswordField(
                      controller: _confirmPasswordController,
                      label: languageProvider.getText('Confirm New Password', 'تأكيد كلمة المرور الجديدة'),
                      isVisible: _showConfirmPassword,
                      onToggleVisibility: () => setState(() => _showConfirmPassword = !_showConfirmPassword),
                      isDark: isDark,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Change Password Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _handleChangePassword,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF11B96F),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          languageProvider.getText('Change Password', 'تغيير كلمة المرور'),
                          style: DriverTypography.getContextualStyle(
                            context,
                            fontSize: DriverTypography.bodyLarge,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required bool isVisible,
    required VoidCallback onToggleVisibility,
    required bool isDark,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: !isVisible,
      style: DriverTypography.getContextualStyle(
        context,
        fontSize: DriverTypography.bodyLarge,
        color: isDark ? Colors.white : Colors.black87,
      ),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: const Icon(Icons.lock_outline, color: Color(0xFF11B96F)),
        suffixIcon: IconButton(
          icon: Icon(
            isVisible ? Icons.visibility_off : Icons.visibility,
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
          onPressed: onToggleVisibility,
        ),
        labelStyle: TextStyle(color: isDark ? Colors.grey[400] : Colors.grey[600]),
        filled: true,
        fillColor: isDark ? const Color(0xFF0F231A) : Colors.grey[50],
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
        ),
      ),
    );
  }

  void _handleChangePassword() async {
    setState(() => _isLoading = true);
    await Future.delayed(const Duration(seconds: 2));
    
    if (mounted) {
      setState(() => _isLoading = false);
      final languageProvider = context.read<LanguageProvider>();
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark 
              ? const Color(0xFF1B3B2E) 
              : Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              const Icon(Icons.check_circle, color: Color(0xFF11B96F), size: 24),
              const SizedBox(width: 8),
              Text(languageProvider.getText('Success', 'نجح')),
            ],
          ),
          content: Text(
            languageProvider.getText(
              'Your password has been changed successfully!',
              'تم تغيير كلمة المرور بنجاح!'
            )
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: Text(
                languageProvider.ok,
                style: const TextStyle(color: Color(0xFF11B96F), fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      );
    }
  }
}
