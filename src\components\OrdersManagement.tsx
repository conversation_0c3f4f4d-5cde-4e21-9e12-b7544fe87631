import React, { useState } from 'react';
import { 
  ShoppingBag, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Truck, 
  MapPin, 
  Phone, 
  DollarSign,
  Calendar,
  RefreshCw,
  MoreVertical,
  AlertTriangle,
  Package,
  User,
  ChefHat
} from 'lucide-react';

interface Order {
  id: string;
  orderNumber: string;
  customer: {
    name: string;
    phone: string;
    address: string;
  };
  restaurant: {
    name: string;
    phone: string;
  };
  driver?: {
    name: string;
    phone: string;
  };
  items: {
    name: string;
    quantity: number;
    price: number;
  }[];
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'picked_up' | 'delivered' | 'cancelled';
  totalAmount: number;
  deliveryFee: number;
  createdAt: string;
  estimatedDelivery: string;
  paymentMethod: string;
  paymentStatus: 'pending' | 'paid' | 'failed';
}

const OrdersManagement = () => {
  const [orders, setOrders] = useState<Order[]>([
    {
      id: '1',
      orderNumber: 'ORD-2024-001',
      customer: {
        name: 'أحمد محمد',
        phone: '+212 6XX-XXXXXX',
        address: 'شارع الحسن الثاني، الدار البيضاء'
      },
      restaurant: {
        name: 'مطعم الأصالة',
        phone: '+212 5XX-XXXXXX'
      },
      driver: {
        name: 'يوسف السائق',
        phone: '+212 6XX-XXXXXX'
      },
      items: [
        { name: 'طاجين الدجاج', quantity: 2, price: 85 },
        { name: 'كسكس باللحم', quantity: 1, price: 120 }
      ],
      status: 'delivered',
      totalAmount: 290,
      deliveryFee: 15,
      createdAt: '2024-01-20 14:30',
      estimatedDelivery: '2024-01-20 15:15',
      paymentMethod: 'بطاقة ائتمان',
      paymentStatus: 'paid'
    },
    {
      id: '2',
      orderNumber: 'ORD-2024-002',
      customer: {
        name: 'فاطمة الزهراء',
        phone: '+212 6XX-XXXXXX',
        address: 'حي الرياض، الرباط'
      },
      restaurant: {
        name: 'برجر ستيشن',
        phone: '+212 5XX-XXXXXX'
      },
      items: [
        { name: 'برجر كلاسيك', quantity: 3, price: 45 },
        { name: 'بطاطس مقلية', quantity: 2, price: 25 }
      ],
      status: 'preparing',
      totalAmount: 185,
      deliveryFee: 10,
      createdAt: '2024-01-20 16:45',
      estimatedDelivery: '2024-01-20 17:30',
      paymentMethod: 'نقداً عند التسليم',
      paymentStatus: 'pending'
    },
    {
      id: '3',
      orderNumber: 'ORD-2024-003',
      customer: {
        name: 'محمد العلوي',
        phone: '+212 6XX-XXXXXX',
        address: 'جليز، مراكش'
      },
      restaurant: {
        name: 'حلويات الدار البيضاء',
        phone: '+212 5XX-XXXXXX'
      },
      items: [
        { name: 'شباكية', quantity: 1, price: 60 },
        { name: 'غريبة', quantity: 2, price: 40 }
      ],
      status: 'cancelled',
      totalAmount: 140,
      deliveryFee: 20,
      createdAt: '2024-01-20 12:15',
      estimatedDelivery: '2024-01-20 13:00',
      paymentMethod: 'بطاقة ائتمان',
      paymentStatus: 'failed'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('all');
  const [dateFilter, setDateFilter] = useState('today');

  const statuses = [
    { id: 'all', name: 'جميع الحالات', color: 'text-gray-600' },
    { id: 'pending', name: 'في الانتظار', color: 'text-yellow-600' },
    { id: 'confirmed', name: 'مؤكد', color: 'text-blue-600' },
    { id: 'preparing', name: 'قيد التحضير', color: 'text-orange-600' },
    { id: 'ready', name: 'جاهز', color: 'text-purple-600' },
    { id: 'picked_up', name: 'تم الاستلام', color: 'text-indigo-600' },
    { id: 'delivered', name: 'تم التوصيل', color: 'text-green-600' },
    { id: 'cancelled', name: 'ملغي', color: 'text-red-600' }
  ];

  const paymentStatuses = [
    { id: 'all', name: 'جميع حالات الدفع' },
    { id: 'pending', name: 'في الانتظار' },
    { id: 'paid', name: 'مدفوع' },
    { id: 'failed', name: 'فشل الدفع' }
  ];

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.restaurant.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || order.status === selectedStatus;
    const matchesPaymentStatus = selectedPaymentStatus === 'all' || order.paymentStatus === selectedPaymentStatus;
    
    return matchesSearch && matchesStatus && matchesPaymentStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'confirmed': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'preparing': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'ready': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'picked_up': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      case 'delivered': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'cancelled': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    const statusObj = statuses.find(s => s.id === status);
    return statusObj ? statusObj.name : status;
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'paid': return 'مدفوع';
      case 'failed': return 'فشل الدفع';
      default: return status;
    }
  };

  const handleUpdateOrderStatus = (orderId: string, newStatus: string) => {
    setOrders(prev => prev.map(order => 
      order.id === orderId ? { ...order, status: newStatus as any } : order
    ));
  };

  const handleViewOrderDetails = (order: Order) => {
    // Open order details modal
    console.log('View order details:', order);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white rtl-font">
            إدارة الطلبات
          </h1>
          <p className="text-gray-600 dark:text-gray-400 rtl-font">
            متابعة وإدارة جميع الطلبات في النظام
          </p>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
            <Download className="w-4 h-4" />
            <span>تصدير</span>
          </button>
          <button className="bg-primary text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary/90 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
            <RefreshCw className="w-5 h-5" />
            <span>تحديث</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إجمالي الطلبات</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">{orders.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
              <ShoppingBag className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">الطلبات المكتملة</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {orders.filter(o => o.status === 'delivered').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">قيد التحضير</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {orders.filter(o => ['preparing', 'ready', 'picked_up'].includes(o.status)).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">الطلبات الملغية</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {orders.filter(o => o.status === 'cancelled').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center">
              <XCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="البحث عن طلب..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
            />
          </div>

          {/* Status Filter */}
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {statuses.map(status => (
              <option key={status.id} value={status.id}>{status.name}</option>
            ))}
          </select>

          {/* Payment Status Filter */}
          <select
            value={selectedPaymentStatus}
            onChange={(e) => setSelectedPaymentStatus(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {paymentStatuses.map(status => (
              <option key={status.id} value={status.id}>{status.name}</option>
            ))}
          </select>

          {/* Date Filter */}
          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="all">جميع التواريخ</option>
          </select>

          {/* Refresh Button */}
          <button className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse">
            <RefreshCw className="w-4 h-4" />
            <span>تحديث</span>
          </button>
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  رقم الطلب
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  العميل
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  المطعم
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  المبلغ
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  الحالة
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  الدفع
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  الوقت
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
              {filteredOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="px-6 py-4">
                    <div className="font-semibold text-gray-800 dark:text-white">
                      {order.orderNumber}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <h4 className="font-semibold text-gray-800 dark:text-white rtl-font">
                        {order.customer.name}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {order.customer.phone}
                      </p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <ChefHat className="w-4 h-4 text-gray-400" />
                      <span className="text-sm font-medium text-gray-800 dark:text-white rtl-font">
                        {order.restaurant.name}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="font-semibold text-gray-800 dark:text-white">
                      {order.totalAmount} درهم
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      + {order.deliveryFee} توصيل
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {getStatusText(order.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(order.paymentStatus)}`}>
                      {getPaymentStatusText(order.paymentStatus)}
                    </span>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1 rtl-font">
                      {order.paymentMethod}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {order.createdAt}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button 
                        onClick={() => handleViewOrderDetails(order)}
                        className="w-8 h-8 flex items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors"
                        title="عرض التفاصيل"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      {order.status !== 'delivered' && order.status !== 'cancelled' && (
                        <select
                          value={order.status}
                          onChange={(e) => handleUpdateOrderStatus(order.id, e.target.value)}
                          className="text-xs border border-gray-200 dark:border-gray-600 rounded-lg px-2 py-1 bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                        >
                          {statuses.filter(s => s.id !== 'all').map(status => (
                            <option key={status.id} value={status.id}>{status.name}</option>
                          ))}
                        </select>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredOrders.length === 0 && (
          <div className="text-center py-12">
            <ShoppingBag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2 rtl-font">
              لا توجد طلبات
            </h3>
            <p className="text-gray-500 dark:text-gray-500 rtl-font">
              لم يتم العثور على طلبات تطابق معايير البحث
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrdersManagement;