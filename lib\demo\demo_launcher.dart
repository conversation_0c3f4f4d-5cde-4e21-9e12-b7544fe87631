import 'package:flutter/material.dart';
import '../constants/driver_themes.dart';
import 'theme_showcase.dart';
import 'login_demo.dart';
import 'login_comparison.dart';
import 'otp_demo.dart';
import 'home_demo.dart';
import 'full_flow_demo.dart';

/// شاشة إطلاق العروض التجريبية
/// تتيح الوصول إلى جميع العروض التجريبية المتاحة
class DemoLauncherScreen extends StatelessWidget {
  const DemoLauncherScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wasslti Partner - Demo Launcher',
      debugShowCheckedModeBanner: false,
      theme: DriverThemes.lightTheme,
      darkTheme: DriverThemes.darkTheme,
      themeMode: ThemeMode.system,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Wasslti Partner Demos'),
          centerTitle: true,
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // مقدمة
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.local_shipping,
                        size: 48,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Wasslti Partner',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Driver Application Design System',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // قائمة العروض التجريبية
              Expanded(
                child: ListView(
                  children: [
                    _buildDemoCard(
                      context,
                      title: 'Full Flow Demo',
                      subtitle: 'Complete user journey: Login → OTP → Home',
                      icon: Icons.play_circle_filled,
                      color: Colors.red,
                      onTap: () => _navigateToDemo(context, const FullFlowDemoApp()),
                    ),

                    const SizedBox(height: 12),

                    _buildDemoCard(
                      context,
                      title: 'Login Screen Demo',
                      subtitle: 'Interactive login screen with theme switching',
                      icon: Icons.login,
                      color: Colors.blue,
                      onTap: () => _navigateToDemo(context, const LoginDemoApp()),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    _buildDemoCard(
                      context,
                      title: 'Login Comparison',
                      subtitle: 'Side-by-side comparison of light and dark modes',
                      icon: Icons.compare,
                      color: Colors.purple,
                      onTap: () => _navigateToDemo(context, const LoginComparisonScreen()),
                    ),

                    const SizedBox(height: 12),

                    _buildDemoCard(
                      context,
                      title: 'OTP Verification Demo',
                      subtitle: 'Interactive OTP verification screen with theme switching',
                      icon: Icons.security,
                      color: Colors.orange,
                      onTap: () => _navigateToDemo(context, const OtpDemoApp()),
                    ),

                    const SizedBox(height: 12),

                    _buildDemoCard(
                      context,
                      title: 'Driver Home Demo',
                      subtitle: 'Fullscreen map interface with floating controls',
                      icon: Icons.home,
                      color: Colors.teal,
                      onTap: () => _navigateToDemo(context, const HomeDemoApp()),
                    ),

                    const SizedBox(height: 12),

                    _buildDemoCard(
                      context,
                      title: 'Theme Showcase',
                      subtitle: 'Complete design system showcase',
                      icon: Icons.palette,
                      color: Colors.green,
                      onTap: () => _navigateToDemo(context, const ThemeShowcaseScreen()),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // معلومات إضافية
                    Card(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 20,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Features',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            _buildFeatureItem(context, '✅ Light & Dark themes'),
                            _buildFeatureItem(context, '✅ Night driving mode'),
                            _buildFeatureItem(context, '✅ Arabic & English support'),
                            _buildFeatureItem(context, '✅ Driver-optimized typography'),
                            _buildFeatureItem(context, '✅ Responsive design'),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDemoCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall,
      ),
    );
  }

  void _navigateToDemo(BuildContext context, Widget demo) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => demo,
      ),
    );
  }
}

/// دالة لتشغيل شاشة إطلاق العروض التجريبية
void runDemoLauncher() {
  runApp(const DemoLauncherScreen());
}

/// مثال على الاستخدام:
/// 
/// ```dart
/// // في main.dart
/// import 'demo/demo_launcher.dart';
/// 
/// void main() {
///   runDemoLauncher();
/// }
/// ```
