import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'constants/driver_themes.dart';
import 'providers/auth_provider.dart';
import 'providers/language_provider.dart';
import 'screens/profile/edit_profile_screen.dart';
import 'screens/profile/change_password_screen.dart';
import 'screens/profile/manage_documents_screen.dart';

/// Simple test for profile screens
void main() {
  runApp(const TestSimpleProfileApp());
}

class TestSimpleProfileApp extends StatelessWidget {
  const TestSimpleProfileApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
      ],
      child: MaterialApp(
        title: 'Test Profile Screens',
        debugShowCheckedModeBanner: false,
        theme: DriverThemes.lightTheme,
        darkTheme: DriverThemes.darkTheme,
        themeMode: ThemeMode.system,
        home: const TestSimpleProfileHome(),
      ),
    );
  }
}

class TestSimpleProfileHome extends StatefulWidget {
  const TestSimpleProfileHome({super.key});

  @override
  State<TestSimpleProfileHome> createState() => _TestSimpleProfileHomeState();
}

class _TestSimpleProfileHomeState extends State<TestSimpleProfileHome> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LanguageProvider>().initializeLanguage();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: Text(
          languageProvider.getText('Profile Screens', 'شاشات الملف الشخصي'),
        ),
        backgroundColor: const Color(0xFF11B96F),
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: () {
              final newLang = languageProvider.isEnglish ? 'العربية' : 'English';
              languageProvider.changeLanguage(newLang);
            },
            child: Text(
              languageProvider.isEnglish ? 'العربية' : 'English',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.account_circle,
                size: 80,
                color: Color(0xFF11B96F),
              ),
              const SizedBox(height: 20),
              Text(
                languageProvider.getText(
                  'Profile Management',
                  'إدارة الملف الشخصي'
                ),
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              
              // Edit Profile Button
              SizedBox(
                width: double.infinity,
                height: 60,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const EditProfileScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF11B96F),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: const Icon(Icons.person_outline),
                  label: Text(
                    languageProvider.editProfile,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Change Password Button
              SizedBox(
                width: double.infinity,
                height: 60,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ChangePasswordScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: const Icon(Icons.lock_outline),
                  label: Text(
                    languageProvider.changePassword,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Manage Documents Button
              SizedBox(
                width: double.infinity,
                height: 60,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ManageDocumentsScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: const Icon(Icons.description_outlined),
                  label: Text(
                    languageProvider.manageDocuments,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF1B3B2E) : Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      languageProvider.getText(
                        'All screens support:',
                        'جميع الشاشات تدعم:'
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      languageProvider.getText(
                        '• Bilingual interface\n• Form validation\n• Dark/Light themes\n• Responsive design',
                        '• واجهة ثنائية اللغة\n• التحقق من البيانات\n• الوضع المظلم والفاتح\n• تصميم متجاوب'
                      ),
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
