# 🚚 Wasslti Partner - Driver App

تطبيق السائق لمنصة وصلتي للتوصيل - مصمم خصيصاً للسائقين مع واجهة محسنة للاستخدام أثناء القيادة.

## 🎨 نظام التصميم

### ✨ الميزات الرئيسية
- 🌓 **دعم الوضع الفاتح والمظلم** مع وضع ليلي خاص للقيادة
- 🔤 **خطوط محسنة** للقراءة أثناء القيادة (أحجام أكبر)
- 🎯 **ألوان عالية التباين** للوضوح التام
- 🌍 **دعم العربية والإنجليزية** مع خطوط مخصصة
- 📱 **تصميم متجاوب** لجميع أحجام الشاشات

### 🎨 نظام الألوان
```dart
// الألوان الأساسية  
primaryGreen: #00C853      // الأخضر الرئيسي
primaryGreenDark: #00A843  // أخضر داكن
primaryGreenLight: #5DFC8D // أخضر فاتح

// ألوان خاصة بالتطبيق
loginButtonGreen: #11B96F  // زر تسجيل الدخول
```

### 📝 نظام الخطوط
- **الإنجليزية**: Roboto (واضح ومقروء)
- **العربية**: Cairo (محسن للعربية)
- **الأرقام**: Roboto Mono (للبيانات والمعرفات)

## 🚀 البدء السريع

### المتطلبات
- Flutter SDK 3.7.0+
- Dart 3.0+
- Android Studio / VS Code
- **Google Maps API Key** (للخريطة) - [راجع دليل الإعداد](GOOGLE_MAPS_SETUP.md)

### التثبيت
```bash
# استنساخ المشروع
git clone [repository-url]
cd wasslti_partner

# تثبيت التبعيات
flutter pub get

# إعداد Google Maps API Key (مطلوب للخريطة)
# راجع ملف GOOGLE_MAPS_SETUP.md للتفاصيل

# تشغيل التطبيق
flutter run
```

### ⚠️ ملاحظة مهمة
التطبيق يعمل حالياً بشاشة رئيسية مؤقتة. لتفعيل الخريطة الحقيقية، يجب إعداد Google Maps API Key. راجع [دليل الإعداد](GOOGLE_MAPS_SETUP.md).

## 📱 الشاشات المتاحة

### 🔐 شاشة تسجيل الدخول
- تصميم حديث مع دعم الوضع الفاتح والمظلم
- حقول إدخال محسنة مع التحقق من صحة البيانات
- زر تسجيل الدخول بـ Google
- رابط التواصل مع الإدارة
- الانتقال التلقائي إلى صفحة تأكيد رقم الهاتف

### 📱 شاشة تأكيد رقم الهاتف (OTP)
- تصميم أنيق مع 6 حقول منفصلة للأرقام
- التنقل التلقائي بين الحقول
- عداد تنازلي لإعادة الإرسال (30 ثانية)
- تنسيق رقم الهاتف مع إخفاء جزئي
- دعم كامل للوضع الفاتح والمظلم
- الانتقال التلقائي إلى الشاشة الرئيسية

### 🏠 الشاشة الرئيسية للسائق
- خريطة Google Maps كاملة الشاشة
- شريط علوي عائم مع الملف الشخصي والأزرار السريعة
- نافذة الطلبات الواردة مع رسوم متحركة
- زر الوصول السريع القابل للتوسع (FAB)
- رسوم متحركة نابضة لموقع السائق
- دعم كامل للوضع الفاتح والمظلم

**الملفات:**
- `lib/screens/auth/login_screen.dart`
- `lib/screens/auth/otp_verification_screen.dart`
- `lib/screens/home/<USER>
- `lib/widgets/floating_top_bar.dart`
- `lib/widgets/incoming_order_popup.dart`
- `lib/widgets/quick_access_fab.dart`
- `lib/screens/auth/README.md`
- `lib/screens/home/<USER>

## 🧪 العروض التجريبية

### تشغيل العروض التجريبية
```dart
// في main.dart
import 'demo/demo_launcher.dart';

void main() {
  runDemoLauncher(); // شاشة إطلاق جميع العروض
}
```

### العروض المتاحة:
1. **Full Flow Demo** - التدفق الكامل: تسجيل الدخول → OTP → الشاشة الرئيسية
2. **Login Demo** - عرض تفاعلي لصفحة تسجيل الدخول
3. **Login Comparison** - مقارنة بين الوضع الفاتح والمظلم
4. **OTP Demo** - عرض تفاعلي لصفحة تأكيد رقم الهاتف
5. **Home Demo** - عرض تفاعلي للشاشة الرئيسية مع الخريطة
6. **Theme Showcase** - عرض شامل لنظام التصميم

## 🏗️ هيكل المشروع

```
lib/
├── constants/           # ثوابت التصميم
│   ├── driver_colors.dart      # نظام الألوان
│   ├── driver_typography.dart  # نظام الخطوط
│   └── driver_themes.dart      # الثيمات المتكاملة
├── screens/            # شاشات التطبيق
│   ├── auth/
│   │   ├── login_screen.dart   # شاشة تسجيل الدخول
│   │   ├── otp_verification_screen.dart # شاشة تأكيد OTP
│   │   └── README.md
│   └── home/
│       ├── driver_home_screen.dart # الشاشة الرئيسية
│       └── README.md
├── widgets/            # المكونات المشتركة
│   ├── floating_top_bar.dart   # الشريط العلوي العائم
│   ├── incoming_order_popup.dart # نافذة الطلبات الواردة
│   └── quick_access_fab.dart   # زر الوصول السريع
├── demo/               # العروض التجريبية
│   ├── demo_launcher.dart      # شاشة إطلاق العروض
│   ├── full_flow_demo.dart     # العرض الشامل
│   ├── login_demo.dart         # عرض صفحة تسجيل الدخول
│   ├── login_comparison.dart   # مقارنة الأوضاع
│   ├── otp_demo.dart          # عرض صفحة OTP
│   ├── home_demo.dart         # عرض الشاشة الرئيسية
│   └── theme_showcase.dart     # عرض نظام التصميم
└── main.dart           # نقطة البداية
```

## 🎯 الاستخدام

### تطبيق نظام الثيمات
```dart
import 'constants/driver_themes.dart';

MaterialApp(
  theme: DriverThemes.lightTheme,
  darkTheme: DriverThemes.darkTheme,
  themeMode: ThemeMode.system,
  // أو للوضع الليلي:
  // theme: DriverThemes.nightDrivingTheme,
)
```

### استخدام الألوان
```dart
import 'constants/driver_colors.dart';

Container(
  color: DriverColors.primaryGreen,
  child: Text(
    'مرحباً',
    style: TextStyle(color: DriverColors.textOnPrimary),
  ),
)
```

### استخدام الخطوط
```dart
import 'constants/driver_typography.dart';

Text(
  'عنوان كبير',
  style: DriverTypography.getContextualStyle(
    context,
    fontSize: DriverTypography.headlineLarge,
    fontWeight: DriverTypography.bold,
  ),
)
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/
```

### اختبار صفحة تسجيل الدخول
```dart
testWidgets('Login screen loads correctly', (WidgetTester tester) async {
  await tester.pumpWidget(const WassltiPartnerApp());

  expect(find.text('Welcome to Wasslti Partner'), findsOneWidget);
  expect(find.text('Log In'), findsOneWidget);
});
```

## 📋 المهام المستقبلية

### 🔐 المصادقة
- [x] ✅ **صفحة تسجيل الدخول** - مكتملة
- [x] ✅ **صفحة تأكيد رقم الهاتف (OTP)** - مكتملة
- [ ] تنفيذ API تسجيل الدخول
- [ ] تكامل Firebase Auth
- [ ] تسجيل الدخول بـ Google
- [ ] صفحة نسيان كلمة المرور
- [ ] التحقق الفعلي من OTP

### 🏠 الشاشة الرئيسية
- [x] ✅ **الشاشة الرئيسية للسائق** - مكتملة
- [x] ✅ **خريطة Google Maps كاملة الشاشة** - مكتملة
- [x] ✅ **الشريط العلوي العائم** - مكتمل
- [x] ✅ **نافذة الطلبات الواردة** - مكتملة
- [x] ✅ **زر الوصول السريع (FAB)** - مكتمل
- [ ] تكامل مع خدمات الموقع الحقيقية
- [ ] تتبع موقع السائق في الوقت الفعلي
- [ ] تكامل مع API الطلبات الحقيقي

### 🏠 الشاشات الرئيسية
- [ ] الشاشة الرئيسية للسائق
- [ ] شاشة الطلبات المتاحة
- [ ] شاشة تفاصيل الطلب
- [ ] شاشة الملاحة
- [ ] شاشة الإحصائيات

### ⚙️ الإعدادات
- [ ] صفحة الملف الشخصي
- [ ] إعدادات التطبيق
- [ ] إعدادات الإشعارات
- [ ] تبديل اللغة

### 🔔 الإشعارات
- [ ] إشعارات الطلبات الجديدة
- [ ] إشعارات حالة الطلب
- [ ] إشعارات النظام

## 🛠️ التطوير

### إضافة شاشة جديدة
```dart
// 1. إنشاء ملف الشاشة
lib/screens/new_screen/new_screen.dart

// 2. استخدام نظام التصميم
import '../../constants/driver_themes.dart';

class NewScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('شاشة جديدة')),
      body: // محتوى الشاشة
    );
  }
}
```

### إضافة ألوان جديدة
```dart
// في lib/constants/driver_colors.dart
static const Color newColor = Color(0xFF123456);
```

### إضافة خطوط جديدة
```dart
// في lib/constants/driver_typography.dart
static const double newSize = 18.0;
```

## 📚 الموارد

### التوثيق
- [Flutter Documentation](https://docs.flutter.dev/)
- [Material Design 3](https://m3.material.io/)
- [Google Fonts](https://fonts.google.com/)

### الأدوات المستخدمة
- **Flutter**: إطار العمل الأساسي
- **Material 3**: نظام التصميم
- **Google Fonts**: الخطوط
- **VS Code**: بيئة التطوير

## 🤝 المساهمة

### قواعد الكود
1. استخدم نظام التصميم المحدد
2. اتبع معايير Dart/Flutter
3. أضف تعليقات باللغة العربية
4. اكتب اختبارات للميزات الجديدة

### خطوات المساهمة
1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التغييرات
4. كتابة الاختبارات
5. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE).

## 📞 التواصل

للاستفسارات والدعم:
- Email: <EMAIL>
- Phone: +966 XX XXX XXXX

---

**تم التطوير بـ ❤️ لسائقي وصلتي**
```
