# 🏠 الشاشة الرئيسية للسائق (Driver Home Screen)

## 🎨 التصميم

الشاشة الرئيسية مصممة كخريطة كاملة الشاشة مع عناصر تحكم عائمة لتجربة قيادة مثلى:

### 🗺️ **الخريطة الرئيسية:**
- خريطة Google Maps كاملة الشاشة
- موقع السائق الحالي مع رسوم متحركة نابضة
- علامات المطاعم (نقاط الاستلام)
- علامات العملاء (نقاط التسليم)
- أزرار التكبير والتصغير

### 🔘 **الشريط العلوي العائم:**
- **اليسار**: صورة الملف الشخصي (دائرية مع الأحرف الأولى)
- **الوسط**: رسالة ترحيب "Welcome back, [اسم السائق] 👋"
- **اليمين**: أزرار سريعة (المحفظة، التوصيلات، الدعم)

### 📦 **نافذة الطلب الوارد:**
- تظهر في أسفل الشاشة عند وصول طلب جديد
- معلومات المطعم مع الأيقونة
- مسار الاستلام والتسليم
- الوقت المتوقع، المسافة، والأجرة
- أزرار القبول (أخضر) والرفض (رمادي)
- عداد تنازلي (30 ثانية)

### 🛞 **زر الوصول السريع (FAB):**
- زر عائم في أسفل اليمين
- يتوسع لإظهار خيارات: الإعدادات، التوصيلات، الاتصال بالدعم
- رسوم متحركة سلسة للتوسع والدوران

## 🔧 المكونات

### الملفات الرئيسية:
- `driver_home_screen.dart` - الشاشة الرئيسية
- `floating_top_bar.dart` - الشريط العلوي العائم
- `incoming_order_popup.dart` - نافذة الطلب الوارد
- `quick_access_fab.dart` - زر الوصول السريع

### الميزات التفاعلية:
- ✅ **خريطة تفاعلية** مع Google Maps
- ✅ **رسوم متحركة نابضة** لموقع السائق
- ✅ **نافذة طلبات منزلقة** مع رسوم متحركة
- ✅ **زر FAB قابل للتوسع** مع دوران
- ✅ **أزرار عائمة** مع ظلال ناعمة
- ✅ **دعم الوضع الفاتح والمظلم**

## 🎯 الاستخدام

```dart
import 'package:flutter/material.dart';
import 'screens/home/<USER>';

// الانتقال إلى الشاشة الرئيسية
Navigator.of(context).pushAndRemoveUntil(
  MaterialPageRoute(
    builder: (context) => const DriverHomeScreen(
      driverName: 'Yassine',
    ),
  ),
  (route) => false,
);
```

## 🔗 التكامل

### من صفحة OTP:
```dart
// في otp_verification_screen.dart
Navigator.of(context).pushAndRemoveUntil(
  MaterialPageRoute(
    builder: (context) => const DriverHomeScreen(
      driverName: 'Yassine',
    ),
  ),
  (route) => false,
);
```

### التبعيات المطلوبة:
```yaml
dependencies:
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  permission_handler: ^11.1.0
```

## 🎨 نظام الألوان

### الألوان المستخدمة:
- **الأخضر الرئيسي**: #11B96F (الأزرار والتأكيدات)
- **الخلفية الفاتحة**: #FFFFFF (الكروت والأزرار)
- **الخلفية المظلمة**: #1B3B2E (الكروت في الوضع المظلم)
- **النص الأساسي**: #0F231A / #FFFFFF (حسب الوضع)

### الظلال والتأثيرات:
- ظلال ناعمة للعناصر العائمة
- حدود خضراء للعناصر النشطة
- رسوم متحركة سلسة للانتقالات

## 📱 التوافق

- ✅ **Android & iOS**
- ✅ **الوضع الفاتح والمظلم**
- ✅ **أحجام شاشات مختلفة**
- ✅ **دعم RTL** (جاهز للعربية)
- ✅ **Google Maps** مدمجة

## 🧪 العرض التجريبي

```dart
import 'demo/home_demo.dart';

void main() {
  runHomeDemo();
}
```

### ميزات العرض التجريبي:
- تبديل بين أسماء السائقين المختلفة
- تبديل بين الأوضاع (فاتح/مظلم/ليلي)
- محاكاة طلب وارد بعد 3 ثوان
- جميع الأزرار تعمل مع رسائل تأكيد

## 📝 المهام المستقبلية

### 🗺️ **تحسينات الخريطة:**
- [ ] تكامل مع خدمات الموقع الحقيقية
- [ ] تتبع موقع السائق في الوقت الفعلي
- [ ] عرض الطرق والاتجاهات
- [ ] تخصيص أنماط الخريطة

### 📦 **إدارة الطلبات:**
- [ ] تكامل مع API الطلبات الحقيقي
- [ ] إشعارات الطلبات الجديدة
- [ ] تتبع حالة الطلب
- [ ] تاريخ الطلبات

### ⚙️ **الميزات الإضافية:**
- [ ] وضع التنقل أثناء التوصيل
- [ ] إحصائيات السائق اليومية
- [ ] نظام التقييمات
- [ ] الدردشة مع العملاء

### 🔧 **التحسينات التقنية:**
- [ ] تحسين أداء الخريطة
- [ ] إدارة ذاكرة أفضل
- [ ] دعم الوضع غير المتصل
- [ ] تحسين استهلاك البطارية

## 🎯 نصائح للتطوير

### الأداء:
- استخدم `GoogleMapController` بحذر
- تجنب إعادة بناء الخريطة غير الضرورية
- استخدم `setState` بحكمة للرسوم المتحركة

### التصميم:
- حافظ على العناصر العائمة بسيطة
- استخدم الظلال بشكل متسق
- اختبر على أحجام شاشات مختلفة

### الاختبار:
- اختبر على أجهزة حقيقية للخريطة
- تأكد من أذونات الموقع
- اختبر في ظروف شبكة مختلفة
