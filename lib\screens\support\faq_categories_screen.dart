import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// FAQ Categories screen with detailed articles
class FAQCategoriesScreen extends StatefulWidget {
  final String categoryTitle;
  final IconData categoryIcon;
  final Color categoryColor;

  const FAQCategoriesScreen({
    super.key,
    required this.categoryTitle,
    required this.categoryIcon,
    required this.categoryColor,
  });

  @override
  State<FAQCategoriesScreen> createState() => _FAQCategoriesScreenState();
}

class _FAQCategoriesScreenState extends State<FAQCategoriesScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  List<Map<String, String>> _filteredFAQs = [];

  @override
  void initState() {
    super.initState();
    _filteredFAQs = _getFAQsForCategory();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<Map<String, String>> _getFAQsForCategory() {
    final languageProvider = context.read<LanguageProvider>();
    
    // Sample FAQs - in real app, this would come from API
    return [
      {
        'question': languageProvider.getText(
          'How do I update my profile picture?',
          'كيف أحدث صورة ملفي الشخصي؟'
        ),
        'answer': languageProvider.getText(
          'Go to Settings > Account > Edit Profile, then tap on your profile picture and select a new image from your gallery or camera.',
          'اذهب إلى الإعدادات > الحساب > تعديل الملف الشخصي، ثم انقر على صورة ملفك الشخصي واختر صورة جديدة من المعرض أو الكاميرا.'
        ),
        'category': 'account',
      },
      {
        'question': languageProvider.getText(
          'How do I change my phone number?',
          'كيف أغير رقم هاتفي؟'
        ),
        'answer': languageProvider.getText(
          'You can update your phone number in Settings > Account > Edit Profile. Note that you may need to verify the new number.',
          'يمكنك تحديث رقم هاتفك في الإعدادات > الحساب > تعديل الملف الشخصي. لاحظ أنك قد تحتاج إلى التحقق من الرقم الجديد.'
        ),
        'category': 'account',
      },
      {
        'question': languageProvider.getText(
          'How do I start accepting trips?',
          'كيف أبدأ في قبول الرحلات؟'
        ),
        'answer': languageProvider.getText(
          'Make sure you are online by toggling your status to "Working" in the main screen. You will then receive trip requests.',
          'تأكد من أنك متصل بتغيير حالتك إلى "يعمل" في الشاشة الرئيسية. ستتلقى بعدها طلبات الرحلات.'
        ),
        'category': 'driving',
      },
      {
        'question': languageProvider.getText(
          'What should I do if a customer cancels?',
          'ماذا أفعل إذا ألغى العميل؟'
        ),
        'answer': languageProvider.getText(
          'If a customer cancels, you may be eligible for a cancellation fee depending on the timing. The app will automatically handle this.',
          'إذا ألغى العميل، قد تكون مؤهلاً لرسوم الإلغاء حسب التوقيت. التطبيق سيتعامل مع هذا تلقائياً.'
        ),
        'category': 'driving',
      },
      {
        'question': languageProvider.getText(
          'How do I track my daily earnings?',
          'كيف أتتبع أرباحي اليومية؟'
        ),
        'answer': languageProvider.getText(
          'You can view your earnings in the Earnings section of the app, which shows daily, weekly, and monthly summaries.',
          'يمكنك عرض أرباحك في قسم الأرباح في التطبيق، والذي يظهر ملخصات يومية وأسبوعية وشهرية.'
        ),
        'category': 'payments',
      },
      {
        'question': languageProvider.getText(
          'When do I get paid?',
          'متى أحصل على راتبي؟'
        ),
        'answer': languageProvider.getText(
          'Payments are processed weekly on Tuesdays. You can set up direct deposit or choose other payment methods in Settings.',
          'تتم معالجة المدفوعات أسبوعياً يوم الثلاثاء. يمكنك إعداد الإيداع المباشر أو اختيار طرق دفع أخرى في الإعدادات.'
        ),
        'category': 'payments',
      },
    ];
  }

  void _filterFAQs(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredFAQs = _getFAQsForCategory();
      } else {
        _filteredFAQs = _getFAQsForCategory().where((faq) {
          return faq['question']!.toLowerCase().contains(query.toLowerCase()) ||
                 faq['answer']!.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            widget.categoryTitle,
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: Column(
          children: [
            // Search Bar
            Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                onChanged: _filterFAQs,
                style: TextStyle(
                  fontSize: 16,
                  color: isDark ? Colors.white : Colors.black87,
                ),
                decoration: InputDecoration(
                  hintText: languageProvider.getText(
                    'Search in this category...',
                    'ابحث في هذه الفئة...'
                  ),
                  hintStyle: TextStyle(color: isDark ? Colors.grey[400] : Colors.grey[500]),
                  prefixIcon: Icon(Icons.search, color: widget.categoryColor),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.grey),
                          onPressed: () {
                            _searchController.clear();
                            _filterFAQs('');
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                ),
              ),
            ),

            // Results Count
            if (_searchQuery.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Text(
                      languageProvider.getText(
                        '${_filteredFAQs.length} results found',
                        'تم العثور على ${_filteredFAQs.length} نتيجة'
                      ),
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 16),

            // FAQ List
            Expanded(
              child: _filteredFAQs.isEmpty
                  ? _buildEmptyState(isDark, languageProvider)
                  : ListView.separated(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _filteredFAQs.length,
                      separatorBuilder: (context, index) => const SizedBox(height: 12),
                      itemBuilder: (context, index) {
                        final faq = _filteredFAQs[index];
                        return _buildFAQCard(faq, isDark, languageProvider);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isDark, LanguageProvider languageProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: isDark ? Colors.grey[600] : Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('No results found', 'لم يتم العثور على نتائج'),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Try adjusting your search terms',
              'حاول تعديل مصطلحات البحث'
            ),
            style: TextStyle(
              fontSize: 14,
              color: isDark ? Colors.grey[500] : Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFAQCard(Map<String, String> faq, bool isDark, LanguageProvider languageProvider) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: widget.categoryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            widget.categoryIcon,
            color: widget.categoryColor,
            size: 20,
          ),
        ),
        title: Text(
          faq['question']!,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        iconColor: widget.categoryColor,
        collapsedIconColor: widget.categoryColor,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.categoryColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: widget.categoryColor,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      languageProvider.getText('Answer', 'الإجابة'),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: widget.categoryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  faq['answer']!,
                  style: TextStyle(
                    fontSize: 15,
                    height: 1.5,
                    color: isDark ? Colors.grey[300] : Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Text(
                      languageProvider.getText('Was this helpful?', 'هل كان هذا مفيداً؟'),
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 12),
                    GestureDetector(
                      onTap: () => _handleFeedback(true),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: const Color(0xFF11B96F).withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.thumb_up_outlined,
                              size: 14,
                              color: Color(0xFF11B96F),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              languageProvider.getText('Yes', 'نعم'),
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF11B96F),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () => _handleFeedback(false),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.red.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.thumb_down_outlined,
                              size: 14,
                              color: Colors.red,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              languageProvider.getText('No', 'لا'),
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleFeedback(bool isHelpful) {
    final languageProvider = context.read<LanguageProvider>();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isHelpful
              ? languageProvider.getText('Thank you for your feedback!', 'شكراً لك على ملاحظاتك!')
              : languageProvider.getText('We\'ll improve this answer', 'سنحسن هذه الإجابة')
        ),
        backgroundColor: isHelpful ? const Color(0xFF11B96F) : Colors.orange,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}