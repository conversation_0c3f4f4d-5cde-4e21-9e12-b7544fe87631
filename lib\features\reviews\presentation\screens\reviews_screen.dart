import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/review_card.dart';
import '../widgets/review_stats_widget.dart';
import '../widgets/review_filters_widget.dart';

class ReviewsScreen extends StatefulWidget {
  const ReviewsScreen({super.key});

  @override
  State<ReviewsScreen> createState() => _ReviewsScreenState();
}

class _ReviewsScreenState extends State<ReviewsScreen> {
  String searchQuery = '';
  int selectedRating = 0; // 0 means all ratings
  String selectedSortBy = 'newest';

  final List<Review> reviews = [
    Review(
      id: '1',
      customerName: '<PERSON>',
      customerAvatar: 'assets/images/avatar1.png',
      rating: 5,
      comment: 'Amazing food and excellent service! The grilled chicken was perfectly cooked and the staff was very friendly. Will definitely come back again.',
      date: DateTime.now().subtract(const Duration(hours: 2)),
      orderItems: ['Grilled Chicken Breast', 'Caesar Salad'],
      isVerifiedPurchase: true,
      restaurantReply: null,
    ),
    Review(
      id: '2',
      customerName: '<PERSON>',
      customerAvatar: 'assets/images/avatar2.png',
      rating: 4,
      comment: 'Good food overall, but the delivery took a bit longer than expected. The pizza was still hot and delicious though.',
      date: DateTime.now().subtract(const Duration(days: 1)),
      orderItems: ['Margherita Pizza', 'Fresh Orange Juice'],
      isVerifiedPurchase: true,
      restaurantReply: RestaurantReply(
        message: 'Thank you for your feedback! We apologize for the delay and are working to improve our delivery times.',
        date: DateTime.now().subtract(const Duration(days: 1, hours: 2)),
      ),
    ),
    Review(
      id: '3',
      customerName: 'Mike Wilson',
      customerAvatar: 'assets/images/avatar3.png',
      rating: 3,
      comment: 'The food was okay, but nothing special. The burger was a bit dry and could use more seasoning.',
      date: DateTime.now().subtract(const Duration(days: 2)),
      orderItems: ['Beef Burger'],
      isVerifiedPurchase: true,
      restaurantReply: null,
    ),
    Review(
      id: '4',
      customerName: 'Emily Davis',
      customerAvatar: 'assets/images/avatar4.png',
      rating: 5,
      comment: 'Absolutely loved the dessert! The chocolate cake was heavenly and the presentation was beautiful.',
      date: DateTime.now().subtract(const Duration(days: 3)),
      orderItems: ['Chocolate Cake', 'Coffee'],
      isVerifiedPurchase: true,
      restaurantReply: RestaurantReply(
        message: 'We\'re so happy you enjoyed our chocolate cake! Thank you for choosing us.',
        date: DateTime.now().subtract(const Duration(days: 3, hours: 1)),
      ),
    ),
    Review(
      id: '5',
      customerName: 'David Brown',
      customerAvatar: 'assets/images/avatar5.png',
      rating: 2,
      comment: 'Very disappointed with the service. The order was wrong and the food was cold when it arrived.',
      date: DateTime.now().subtract(const Duration(days: 5)),
      orderItems: ['Caesar Salad', 'Grilled Chicken Breast'],
      isVerifiedPurchase: true,
      restaurantReply: null,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: Row(
                    children: [
                      // Reviews List
                      Expanded(
                        flex: 3,
                        child: Column(
                          children: [
                            // Filters
                            ReviewFiltersWidget(
                              selectedRating: selectedRating,
                              selectedSortBy: selectedSortBy,
                              onRatingChanged: (rating) {
                                setState(() {
                                  selectedRating = rating;
                                });
                              },
                              onSortChanged: (sortBy) {
                                setState(() {
                                  selectedSortBy = sortBy;
                                });
                              },
                            ),
                            
                            // Reviews List
                            Expanded(
                              child: _buildReviewsList(),
                            ),
                          ],
                        ),
                      ),
                      
                      // Stats Panel
                      Container(
                        width: 350,
                        decoration: const BoxDecoration(
                          color: AppColors.white,
                          border: Border(
                            left: BorderSide(
                              color: AppColors.inputBorder,
                              width: 1,
                            ),
                          ),
                        ),
                        child: ReviewStatsWidget(reviews: reviews),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Icon(
                Icons.star,
                color: AppColors.warning,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Customer Reviews',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search reviews...',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Average Rating
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppColors.warning,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.star,
                  color: AppColors.warning,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${_calculateAverageRating().toStringAsFixed(1)} (${reviews.length})',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.warning,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsList() {
    final filteredReviews = _getFilteredReviews();
    
    if (filteredReviews.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.rate_review_outlined,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No reviews found',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Customer reviews will appear here',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }
    
    return Container(
      color: AppColors.backgroundLight,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredReviews.length,
        itemBuilder: (context, index) {
          final review = filteredReviews[index];
          return ReviewCard(
            review: review,
            onReply: (reviewId, message) => _replyToReview(reviewId, message),
          );
        },
      ),
    );
  }

  List<Review> _getFilteredReviews() {
    List<Review> filtered = reviews;

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((review) =>
          review.customerName.toLowerCase().contains(searchQuery.toLowerCase()) ||
          review.comment.toLowerCase().contains(searchQuery.toLowerCase())).toList();
    }

    // Apply rating filter
    if (selectedRating > 0) {
      filtered = filtered.where((review) => review.rating == selectedRating).toList();
    }

    // Apply sorting
    switch (selectedSortBy) {
      case 'newest':
        filtered.sort((a, b) => b.date.compareTo(a.date));
        break;
      case 'oldest':
        filtered.sort((a, b) => a.date.compareTo(b.date));
        break;
      case 'highest':
        filtered.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'lowest':
        filtered.sort((a, b) => a.rating.compareTo(b.rating));
        break;
    }

    return filtered;
  }

  double _calculateAverageRating() {
    if (reviews.isEmpty) return 0.0;
    final total = reviews.fold(0, (sum, review) => sum + review.rating);
    return total / reviews.length;
  }

  void _replyToReview(String reviewId, String message) {
    setState(() {
      final index = reviews.indexWhere((review) => review.id == reviewId);
      if (index >= 0) {
        reviews[index] = reviews[index].copyWith(
          restaurantReply: RestaurantReply(
            message: message,
            date: DateTime.now(),
          ),
        );
      }
    });
  }
}

class Review {
  final String id;
  final String customerName;
  final String customerAvatar;
  final int rating;
  final String comment;
  final DateTime date;
  final List<String> orderItems;
  final bool isVerifiedPurchase;
  final RestaurantReply? restaurantReply;

  Review({
    required this.id,
    required this.customerName,
    required this.customerAvatar,
    required this.rating,
    required this.comment,
    required this.date,
    required this.orderItems,
    required this.isVerifiedPurchase,
    this.restaurantReply,
  });

  Review copyWith({
    String? id,
    String? customerName,
    String? customerAvatar,
    int? rating,
    String? comment,
    DateTime? date,
    List<String>? orderItems,
    bool? isVerifiedPurchase,
    RestaurantReply? restaurantReply,
  }) {
    return Review(
      id: id ?? this.id,
      customerName: customerName ?? this.customerName,
      customerAvatar: customerAvatar ?? this.customerAvatar,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      date: date ?? this.date,
      orderItems: orderItems ?? this.orderItems,
      isVerifiedPurchase: isVerifiedPurchase ?? this.isVerifiedPurchase,
      restaurantReply: restaurantReply ?? this.restaurantReply,
    );
  }
}

class RestaurantReply {
  final String message;
  final DateTime date;

  RestaurantReply({
    required this.message,
    required this.date,
  });
}
