# Dashboard Feature

This feature contains the main dashboard interface for the Wssalti POS system.

## Structure

```
dashboard/
├── presentation/
│   ├── screens/
│   │   └── dashboard_screen.dart      # Main dashboard screen
│   └── widgets/
│       ├── sidebar_widget.dart        # Navigation sidebar
│       ├── stats_card_widget.dart     # Statistics cards
│       ├── chart_widget.dart          # Order rate line chart
│       ├── activity_chart_widget.dart # Activity bar chart
│       ├── popular_food_widget.dart   # Popular food pie chart
│       ├── performance_widget.dart    # Performance circular progress
│       └── widgets.dart               # Widget exports
```

## Features

### Dashboard Screen
- **Top Bar**: Restaurant title, search bar, notifications, user profile
- **Sidebar**: Navigation menu with restaurant management options
- **Stats Cards**: Activity, Total Sales, Total Expense, Total Orders
- **Charts**: 
  - Order Rate (Line chart with dual lines)
  - Activity (Bar chart with monthly data)
  - Popular Food (Pie chart with categories)
  - Performance (Circular progress with metrics)

### Sidebar Navigation
- Home
- Food Order
- Favorite Menu
- Message
- Order History
- Notification
- Bill
- Restaurant (with submenu)
  - Restaurant
  - Menu
  - Orders
  - Reviews
- Drivers
- Settings

### Design Features
- Clean, modern interface
- Responsive layout
- Card-based design with shadows
- Color-coded statistics
- Interactive charts using fl_chart
- Professional typography with Google Fonts

## Usage

To use the dashboard:

```dart
import 'package:wssalti_pos/features/dashboard/presentation/screens/dashboard_screen.dart';

// Navigate to dashboard
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const DashboardScreen()),
);
```

## Dependencies

- `fl_chart`: For charts and graphs
- `google_fonts`: For typography
- `font_awesome_flutter`: For icons

## Color Scheme

The dashboard uses the app's color scheme defined in `AppColors`:
- Primary: Green accent color
- Success: Green for positive metrics
- Error: Red for negative metrics
- Background: Light grey
- Cards: White with subtle shadows
