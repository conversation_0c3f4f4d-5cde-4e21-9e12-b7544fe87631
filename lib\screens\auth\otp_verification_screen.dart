import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../../constants/driver_typography.dart';
import '../../providers/auth_provider.dart';

/// شاشة تأكيد رقم الهاتف (OTP) لتطبيق السائق
/// تدعم الوضع الفاتح والمظلم مع تصميم محسن للسائقين
class OtpVerificationScreen extends StatefulWidget {
  final String phoneNumber;
  
  const OtpVerificationScreen({
    super.key,
    required this.phoneNumber,
  });

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final List<TextEditingController> _controllers = List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());
  
  bool _isLoading = false;
  int _resendCountdown = 30;
  Timer? _timer;
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    _startCountdown();
    
    // التركيز على أول حقل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNodes[0].requestFocus();
    });
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    _timer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _canResend = false;
    _resendCountdown = 30;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_resendCountdown > 0) {
          _resendCountdown--;
        } else {
          _canResend = true;
          timer.cancel();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: isDark ? Colors.white : Colors.black87,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                         MediaQuery.of(context).padding.top -
                         kToolbarHeight - 48, // 48 for safe area bottom
            ),
            child: Column(
              children: [
                const SizedBox(height: 32),

                // الشعار
                _buildLogo(isDark),

                const SizedBox(height: 32),

                // العنوان والوصف
                _buildHeader(isDark),

                const SizedBox(height: 32),

                // حقول OTP
                _buildOtpFields(isDark),

                const SizedBox(height: 20),

                // رابط إعادة الإرسال
                _buildResendLink(isDark),

                const SizedBox(height: 32),

                // زر التأكيد
                _buildVerifyButton(isDark),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo(bool isDark) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDark ? const Color(0xFF11B96F) : const Color(0xFF0F231A),
          width: 2,
        ),
      ),
      child: Icon(
        Icons.local_shipping,
        size: 40,
        color: isDark ? const Color(0xFF11B96F) : const Color(0xFF0F231A),
      ),
    );
  }

  Widget _buildHeader(bool isDark) {
    return Column(
      children: [
        Text(
          'Verify Your Number',
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.displaySmall,
            fontWeight: DriverTypography.bold,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 12),
        
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            text: 'Enter the 6-digit code we sent to\n',
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.bodyLarge,
              color: isDark ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
            ),
            children: [
              TextSpan(
                text: _formatPhoneNumber(widget.phoneNumber),
                style: DriverTypography.getContextualStyle(
                  context,
                  fontSize: DriverTypography.bodyLarge,
                  fontWeight: DriverTypography.semiBold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOtpFields(bool isDark) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(6, (index) {
        return Container(
          width: 45,
          height: 55,
          decoration: BoxDecoration(
            color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _controllers[index].text.isNotEmpty
                  ? const Color(0xFF11B96F)
                  : (isDark ? const Color(0xFF2E5A3E) : const Color(0xFFE0E0E0)),
              width: _focusNodes[index].hasFocus ? 2 : 1,
            ),
            boxShadow: isDark ? null : [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: _controllers[index],
            focusNode: _focusNodes[index],
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            maxLength: 1,
            style: DriverTypography.getNumberStyle(
              context,
              fontSize: 20,
              fontWeight: DriverTypography.bold,
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              counterText: '',
              contentPadding: EdgeInsets.zero,
            ),
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            onChanged: (value) {
              if (value.isNotEmpty) {
                // الانتقال للحقل التالي
                if (index < 5) {
                  _focusNodes[index + 1].requestFocus();
                } else {
                  // إخفاء لوحة المفاتيح عند الانتهاء
                  FocusScope.of(context).unfocus();
                }
              } else {
                // الرجوع للحقل السابق عند الحذف
                if (index > 0) {
                  _focusNodes[index - 1].requestFocus();
                }
              }
              setState(() {});
            },
            onTap: () {
              // تحديد النص عند النقر
              _controllers[index].selection = TextSelection.fromPosition(
                TextPosition(offset: _controllers[index].text.length),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildResendLink(bool isDark) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Didn't receive a code? ",
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodyMedium,
            color: isDark ? const Color(0xFF9E9E9E) : const Color(0xFF757575),
          ),
        ),
        GestureDetector(
          onTap: _canResend ? _handleResendCode : null,
          child: Text(
            _canResend ? 'Resend' : 'Resend in ${_resendCountdown}s',
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.bodyMedium,
              fontWeight: DriverTypography.semiBold,
              color: _canResend 
                  ? const Color(0xFF11B96F)
                  : (isDark ? const Color(0xFF757575) : const Color(0xFF9E9E9E)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVerifyButton(bool isDark) {
    final isComplete = _controllers.every((controller) => controller.text.isNotEmpty);
    
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: (isComplete && !_isLoading) ? _handleVerifyOtp : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF11B96F),
          foregroundColor: Colors.white,
          elevation: isDark ? 0 : 2,
          shadowColor: Colors.black.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          disabledBackgroundColor: isDark 
              ? const Color(0xFF2E5A3E) 
              : const Color(0xFFE0E0E0),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                'Verify',
                style: DriverTypography.bigButtonStyle.copyWith(
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  String _formatPhoneNumber(String phoneNumber) {
    // تنسيق رقم الهاتف لإخفاء جزء منه
    if (phoneNumber.length >= 10) {
      final countryCode = phoneNumber.substring(0, 4); // +212
      final lastDigits = phoneNumber.substring(phoneNumber.length - 3);
      return '$countryCode XXXXXX$lastDigits';
    }
    return phoneNumber;
  }

  void _handleVerifyOtp() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    final authProvider = context.read<AuthProvider>();
    final otpCode = _controllers.map((controller) => controller.text).join();

    // محاولة تأكيد OTP
    final success = await authProvider.verifyOtp(
      phone: widget.phoneNumber,
      otp: otpCode,
    );

    if (!mounted) return;

    setState(() {
      _isLoading = false;
    });

    if (success) {
      // تم تسجيل الدخول بنجاح - سيتم التنقل تلقائياً عبر AuthWrapper
      if (mounted) {
        // العودة إلى الشاشة الرئيسية (AuthWrapper سيتولى التنقل)
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/',
          (route) => false,
        );
      }
    } else {
      // عرض رسالة الخطأ
      if (mounted && authProvider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              authProvider.errorMessage!,
              style: DriverTypography.getContextualStyle(
                context,
                fontSize: DriverTypography.bodyMedium,
                color: Colors.white,
              ),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );

        // مسح الحقول عند الخطأ
        if (mounted) {
          for (var controller in _controllers) {
            controller.clear();
          }
          _focusNodes[0].requestFocus();
        }
      }
    }
  }

  void _handleResendCode() {
    if (_canResend) {
      // TODO: Implement resend OTP logic
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Resend OTP functionality will be implemented',
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.bodyMedium,
              color: Colors.white,
            ),
          ),
          backgroundColor: const Color(0xFF11B96F),
        ),
      );
      
      // إعادة تشغيل العد التنازلي
      _startCountdown();
    }
  }
}
