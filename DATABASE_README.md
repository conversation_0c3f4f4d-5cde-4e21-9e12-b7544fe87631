# قاعدة بيانات وصلتي - Wasslti Database Schema

## نظرة عامة
قاعدة بيانات شاملة لمنصة توصيل الطعام "وصلتي" تدعم جميع العمليات المطلوبة للعملاء والسائقين والمطاعم ولوحة التحكم الإدارية.

## الملفات
- `supabase_wasslti_schema.sql` - الملف الرئيسي لقاعدة البيانات
- `test_schema.sql` - ملف اختبار قاعدة البيانات
- `DATABASE_README.md` - هذا الملف

## المتطلبات
- PostgreSQL 12+
- PostGIS extension
- UUID extension

## التثبيت

### 1. إنشاء قاعدة البيانات
```sql
CREATE DATABASE wasslti_db;
\c wasslti_db;
```

### 2. تشغيل الملف الرئيسي
```bash
psql -d wasslti_db -f supabase_wasslti_schema.sql
```

### 3. اختبار قاعدة البيانات
```bash
psql -d wasslti_db -f test_schema.sql
```

## هيكل قاعدة البيانات

### الجداول الأساسية
- **users** - المستخدمين (العملاء والإداريين)
- **drivers** - السائقين
- **restaurants** - المطاعم
- **meals** - الوجبات
- **orders** - الطلبات
- **payments** - المدفوعات

### الجداول المساعدة
- **categories** - فئات الطعام
- **meal_options** - خيارات الوجبات
- **order_tracking** - تتبع الطلبات
- **wallets** - المحافظ الإلكترونية
- **wallet_transactions** - معاملات المحفظة

### جداول التسويق والعروض
- **promotions** - العروض والخصومات
- **promotion_usage** - استخدام العروض

### جداول التواصل
- **notifications** - الإشعارات
- **reviews** - التقييمات والمراجعات
- **support_tickets** - تذاكر الدعم الفني
- **support_messages** - رسائل الدعم

### الجداول الإدارية
- **admin_users** - المستخدمين الإداريين
- **audit_logs** - سجل العمليات
- **system_settings** - إعدادات النظام
- **content_management** - إدارة المحتوى

### جداول التحليلات والتقارير
- **analytics_events** - أحداث التحليلات
- **financial_reports** - التقارير المالية

### جداول المخزون
- **inventory_items** - عناصر المخزون
- **inventory_transactions** - معاملات المخزون

### جداول التوصيل
- **delivery_zones** - مناطق التوصيل
- **delivery_routes** - مسارات التوصيل

## المميزات الرئيسية

### 1. الأمان
- Row Level Security (RLS) مفعل
- تشفير كلمات المرور
- سجل العمليات الشامل
- صلاحيات متدرجة

### 2. الأداء
- فهارس محسنة للاستعلامات السريعة
- فهارس جغرافية للمواقع
- عروض (Views) للتقارير

### 3. الأتمتة
- توليد أرقام الطلبات تلقائياً
- إنشاء المحافظ تلقائياً
- تحديث التقييمات تلقائياً
- تحديث إحصائيات السائقين

### 4. التتبع
- تتبع جميع التغييرات
- سجل العمليات المفصل
- تتبع المواقع الجغرافية

## الدوال المهمة

### `calculate_distance(lat1, lon1, lat2, lon2)`
حساب المسافة بين نقطتين جغرافيتين

### `generate_order_number()`
توليد رقم طلب فريد

### `update_restaurant_rating()`
تحديث تقييم المطعم تلقائياً

### `update_driver_rating()`
تحديث تقييم السائق تلقائياً

### `update_wallet_balance()`
تحديث رصيد المحفظة

## العروض (Views)

### `order_statistics`
إحصائيات الطلبات اليومية

### `restaurant_performance`
أداء المطاعم

### `driver_performance`
أداء السائقين

## أمثلة الاستخدام

### إنشاء طلب جديد
```sql
INSERT INTO orders (user_id, restaurant_id, subtotal, delivery_fee, total_price, delivery_address, payment_method)
VALUES (
    'user-uuid',
    'restaurant-uuid', 
    85.00,
    15.00,
    100.00,
    'عنوان التوصيل',
    'cash'
);
```

### البحث عن مطاعم قريبة
```sql
SELECT r.*, 
       ST_Distance(r.location, ST_GeogFromText('POINT(-7.5898 33.5731)')) / 1000 as distance_km
FROM restaurants r
WHERE ST_DWithin(r.location, ST_GeogFromText('POINT(-7.5898 33.5731)'), 10000)
AND r.is_active = true
ORDER BY distance_km;
```

### تحديث حالة الطلب
```sql
UPDATE orders 
SET status = 'delivered', 
    actual_delivery_time = NOW()
WHERE id = 'order-uuid';
```

### إضافة تقييم
```sql
INSERT INTO reviews (order_id, user_id, restaurant_id, driver_id, rating, comment, food_rating, delivery_rating)
VALUES (
    'order-uuid',
    'user-uuid',
    'restaurant-uuid',
    'driver-uuid',
    5,
    'خدمة ممتازة',
    5,
    5
);
```

## الصيانة

### تنظيف البيانات القديمة
```sql
-- حذف الإشعارات القديمة (أكثر من 30 يوم)
DELETE FROM notifications 
WHERE created_at < NOW() - INTERVAL '30 days' 
AND is_read = true;

-- حذف أحداث التحليلات القديمة (أكثر من 90 يوم)
DELETE FROM analytics_events 
WHERE created_at < NOW() - INTERVAL '90 days';
```

### إعادة بناء الفهارس
```sql
REINDEX DATABASE wasslti_db;
```

### تحديث الإحصائيات
```sql
ANALYZE;
```

## النسخ الاحتياطي

### إنشاء نسخة احتياطية
```bash
pg_dump wasslti_db > wasslti_backup_$(date +%Y%m%d).sql
```

### استعادة النسخة الاحتياطية
```bash
psql wasslti_db < wasslti_backup_20240120.sql
```

## المراقبة

### مراقبة الأداء
```sql
-- الاستعلامات البطيئة
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- حجم الجداول
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## الدعم الفني
للمساعدة أو الاستفسارات، يرجى التواصل مع فريق التطوير.

## الترخيص
هذا المشروع مملوك لشركة وصلتي ومحمي بحقوق الطبع والنشر.
