import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'providers/auth_provider.dart';
import 'providers/language_provider.dart';
import 'providers/theme_provider.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/otp_verification_screen.dart';
import 'screens/home/<USER>';

void main() {
  runApp(const WassltiPartnerApp());
}

class WassltiPartnerApp extends StatelessWidget {
  const WassltiPartnerApp({super.key});

  @override
  Widget build(BuildContext context) {
    // تعيين شريط الحالة
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
    );

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Wasslti Partner',
            debugShowCheckedModeBanner: false,

            // استخدام نظام الثيمات المتقدم
            theme: themeProvider.getThemeData(Brightness.light),
            darkTheme: themeProvider.getThemeData(Brightness.dark),
            themeMode: themeProvider.effectiveThemeMode,

        // الصفحة الرئيسية مع فحص الجلسة
        home: const AuthWrapper(),

        // إعدادات إضافية
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(
              textScaler: const TextScaler.linear(1.0), // منع تكبير النص من إعدادات النظام
            ),
            child: child!,
          );
        },
          );
        },
      ),
    );
  }
}

/// واجهة فحص المصادقة لتحديد الشاشة المناسبة
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    // تهيئة مزود المصادقة واللغة والثيمات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthProvider>().initialize();
      context.read<LanguageProvider>().initializeLanguage();
      context.read<ThemeProvider>().initializeTheme();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // أثناء التحميل
        if (authProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: Color(0xFF11B96F),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Loading...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // إذا كان مسجل الدخول، اذهب للشاشة الرئيسية
        if (authProvider.isLoggedIn) {
          return DriverHomeScreenTemp(
            driverName: authProvider.driverName,
          );
        }

        // إذا كان في انتظار OTP، اذهب لشاشة OTP
        if (authProvider.isWaitingForOtp && authProvider.lastPhone != null) {
          return OtpVerificationScreen(
            phoneNumber: authProvider.lastPhone!,
          );
        }

        // إذا لم يكن مسجل الدخول، اذهب لشاشة تسجيل الدخول
        return const LoginScreen();
      },
    );
  }
}


