import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import '../../../../core/theme/app_colors.dart';

class ProfileCardWidget extends StatelessWidget {
  const ProfileCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Profile',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textSecondary,
                ),
              ),
              Icon(
                Icons.more_horiz,
                color: AppColors.textSecondary,
                size: 20,
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Profile Content
          Expanded(
            child: Column(
              children: [
                // Profile Picture with Status Indicators
                Stack(
                  alignment: Alignment.center,
                  children: [
                    // Outer circle with colored dots
                    SizedBox(
                      width: 80,
                      height: 80,
                      child: CustomPaint(
                        painter: StatusDotsPainter(),
                      ),
                    ),
                    // Profile picture
                    CircleAvatar(
                      radius: 25,
                      backgroundColor: AppColors.primary,
                      backgroundImage: AssetImage('assets/images/driver_avatar.png'),
                      onBackgroundImageError: (_, __) {},
                      child: Text(
                        'JN',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Name
                Text(
                  'Jordan Nico',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                
                // Rating
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '5.0',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '1k+ Reviews',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class StatusDotsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    
    // Define dot colors and positions
    final dots = [
      {'color': AppColors.error, 'angle': 0.0},
      {'color': Colors.orange, 'angle': 1.0},
      {'color': AppColors.success, 'angle': 2.0},
      {'color': Colors.blue, 'angle': 3.0},
      {'color': Colors.purple, 'angle': 4.0},
      {'color': AppColors.error, 'angle': 5.0},
    ];
    
    for (var dot in dots) {
      final angle = (dot['angle'] as double) * 1.0;
      final x = center.dx + radius * 0.8 * (angle.cos());
      final y = center.dy + radius * 0.8 * (angle.sin());
      
      final paint = Paint()
        ..color = dot['color'] as Color
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(Offset(x, y), 3, paint);
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

extension on double {
  double cos() => math.cos(this);
  double sin() => math.sin(this);
}
