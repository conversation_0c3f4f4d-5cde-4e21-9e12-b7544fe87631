import React, { useState } from 'react';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Download, 
  Filter,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  CreditCard,
  Wallet,
  Building,
  Users,
  ShoppingCart,
  Percent,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  FileText,
  Calculator
} from 'lucide-react';

interface FinancialData {
  period: string;
  revenue: number;
  orders: number;
  commission: number;
  expenses: number;
  profit: number;
  growth: number;
}

interface PaymentMethod {
  name: string;
  amount: number;
  percentage: number;
  transactions: number;
}

const FinancialReports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedYear, setSelectedYear] = useState('2024');
  const [selectedMonth, setSelectedMonth] = useState('1');

  // Mock financial data
  const financialData: FinancialData[] = [
    { period: 'يناير', revenue: 125000, orders: 2500, commission: 18750, expenses: 45000, profit: 61250, growth: 12.5 },
    { period: 'فبراير', revenue: 142000, orders: 2840, commission: 21300, expenses: 48000, profit: 72700, growth: 18.7 },
    { period: 'مارس', revenue: 158000, orders: 3160, commission: 23700, expenses: 52000, profit: 83300, growth: 14.6 },
    { period: 'أبريل', revenue: 171000, orders: 3420, commission: 25650, expenses: 55000, profit: 90350, growth: 8.4 },
    { period: 'مايو', revenue: 189000, orders: 3780, commission: 28350, expenses: 58000, profit: 102650, growth: 13.6 },
    { period: 'يونيو', revenue: 203000, orders: 4060, commission: 30450, expenses: 61000, profit: 111550, growth: 8.7 }
  ];

  const paymentMethods: PaymentMethod[] = [
    { name: 'بطاقة ائتمان', amount: 145000, percentage: 58, transactions: 2890 },
    { name: 'نقداً عند التوصيل', amount: 78000, percentage: 31, transactions: 1560 },
    { name: 'محفظة إلكترونية', amount: 28000, percentage: 11, transactions: 560 }
  ];

  const currentMonthData = financialData[financialData.length - 1];
  const previousMonthData = financialData[financialData.length - 2];

  const calculateGrowth = (current: number, previous: number) => {
    return ((current - previous) / previous * 100).toFixed(1);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const exportReport = (format: 'pdf' | 'excel') => {
    alert(`تصدير التقرير بصيغة ${format.toUpperCase()}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">التقارير المالية</h2>
          <p className="text-gray-600 rtl-font">تحليل مفصل للأداء المالي والإيرادات</p>
        </div>
        
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="week">أسبوعي</option>
            <option value="month">شهري</option>
            <option value="quarter">ربع سنوي</option>
            <option value="year">سنوي</option>
          </select>
          
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <button
              onClick={() => exportReport('pdf')}
              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <Download className="w-4 h-4" />
              <span className="rtl-font">PDF</span>
            </button>
            <button
              onClick={() => exportReport('excel')}
              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Download className="w-4 h-4" />
              <span className="rtl-font">Excel</span>
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">إجمالي الإيرادات</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(currentMonthData.revenue)}</p>
              <div className="flex items-center mt-2">
                {parseFloat(calculateGrowth(currentMonthData.revenue, previousMonthData.revenue)) > 0 ? (
                  <ArrowUpRight className="w-4 h-4 text-green-500" />
                ) : (
                  <ArrowDownRight className="w-4 h-4 text-red-500" />
                )}
                <span className={`text-sm font-medium ${parseFloat(calculateGrowth(currentMonthData.revenue, previousMonthData.revenue)) > 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {calculateGrowth(currentMonthData.revenue, previousMonthData.revenue)}%
                </span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">من الشهر السابق</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">صافي الربح</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(currentMonthData.profit)}</p>
              <div className="flex items-center mt-2">
                {parseFloat(calculateGrowth(currentMonthData.profit, previousMonthData.profit)) > 0 ? (
                  <ArrowUpRight className="w-4 h-4 text-green-500" />
                ) : (
                  <ArrowDownRight className="w-4 h-4 text-red-500" />
                )}
                <span className={`text-sm font-medium ${parseFloat(calculateGrowth(currentMonthData.profit, previousMonthData.profit)) > 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {calculateGrowth(currentMonthData.profit, previousMonthData.profit)}%
                </span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">من الشهر السابق</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">العمولات</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(currentMonthData.commission)}</p>
              <div className="flex items-center mt-2">
                <Percent className="w-4 h-4 text-purple-500" />
                <span className="text-sm font-medium text-purple-500">
                  {((currentMonthData.commission / currentMonthData.revenue) * 100).toFixed(1)}%
                </span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">من الإيرادات</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Percent className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">عدد الطلبات</p>
              <p className="text-2xl font-bold text-gray-900">{currentMonthData.orders.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <ShoppingCart className="w-4 h-4 text-orange-500" />
                <span className="text-sm font-medium text-orange-500">
                  {(currentMonthData.revenue / currentMonthData.orders).toFixed(0)} درهم
                </span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">متوسط الطلب</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
              <ShoppingCart className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend Chart */}
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-800 rtl-font">اتجاه الإيرادات</h3>
            <LineChart className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {financialData.slice(-6).map((data, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 rtl-font">{data.period}</span>
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${(data.revenue / Math.max(...financialData.map(d => d.revenue))) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-20 text-left">
                    {formatCurrency(data.revenue)}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded-full ${data.growth > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {data.growth > 0 ? '+' : ''}{data.growth}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Payment Methods */}
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-800 rtl-font">طرق الدفع</h3>
            <PieChart className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {paymentMethods.map((method, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 rtl-font">{method.name}</span>
                  <span className="text-sm text-gray-500">{method.percentage}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="w-full bg-gray-200 rounded-full h-2 mr-4">
                    <div 
                      className={`h-2 rounded-full ${
                        index === 0 ? 'bg-blue-500' : 
                        index === 1 ? 'bg-green-500' : 'bg-purple-500'
                      }`}
                      style={{ width: `${method.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(method.amount)}
                  </span>
                </div>
                <div className="text-xs text-gray-500 rtl-font">
                  {method.transactions.toLocaleString()} معاملة
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Detailed Financial Table */}
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-800 rtl-font">تفاصيل الأداء المالي</h3>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <button className="text-gray-400 hover:text-gray-600">
                <Filter className="w-5 h-5" />
              </button>
              <button className="text-gray-400 hover:text-gray-600">
                <BarChart3 className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الفترة</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الإيرادات</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الطلبات</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">العمولات</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">المصروفات</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">صافي الربح</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">النمو</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 rtl-font">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {financialData.map((data, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 text-sm font-medium text-gray-900 rtl-font">{data.period}</td>
                  <td className="px-6 py-4 text-sm text-gray-900">{formatCurrency(data.revenue)}</td>
                  <td className="px-6 py-4 text-sm text-gray-900">{data.orders.toLocaleString()}</td>
                  <td className="px-6 py-4 text-sm text-gray-900">{formatCurrency(data.commission)}</td>
                  <td className="px-6 py-4 text-sm text-gray-900">{formatCurrency(data.expenses)}</td>
                  <td className="px-6 py-4 text-sm font-medium text-green-600">{formatCurrency(data.profit)}</td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      data.growth > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {data.growth > 0 ? '+' : ''}{data.growth}%
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button className="text-blue-600 hover:text-blue-800" title="عرض التفاصيل">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-800" title="تصدير">
                        <Download className="w-4 h-4" />
                      </button>
                      <button className="text-purple-600 hover:text-purple-800" title="تحليل">
                        <Calculator className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Financial Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6">
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
            <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center">
              <CreditCard className="w-5 h-5 text-white" />
            </div>
            <h3 className="font-semibold text-blue-800 rtl-font">إجمالي المعاملات</h3>
          </div>
          <p className="text-2xl font-bold text-blue-900 mb-2">
            {paymentMethods.reduce((sum, method) => sum + method.transactions, 0).toLocaleString()}
          </p>
          <p className="text-blue-700 text-sm rtl-font">معاملة هذا الشهر</p>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6">
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
            <div className="w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center">
              <Wallet className="w-5 h-5 text-white" />
            </div>
            <h3 className="font-semibold text-green-800 rtl-font">متوسط قيمة المعاملة</h3>
          </div>
          <p className="text-2xl font-bold text-green-900 mb-2">
            {formatCurrency(currentMonthData.revenue / paymentMethods.reduce((sum, method) => sum + method.transactions, 0))}
          </p>
          <p className="text-green-700 text-sm rtl-font">لكل معاملة</p>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6">
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
            <div className="w-10 h-10 bg-purple-500 rounded-xl flex items-center justify-center">
              <Building className="w-5 h-5 text-white" />
            </div>
            <h3 className="font-semibold text-purple-800 rtl-font">هامش الربح</h3>
          </div>
          <p className="text-2xl font-bold text-purple-900 mb-2">
            {((currentMonthData.profit / currentMonthData.revenue) * 100).toFixed(1)}%
          </p>
          <p className="text-purple-700 text-sm rtl-font">من إجمالي الإيرادات</p>
        </div>
      </div>
    </div>
  );
};

export default FinancialReports;
