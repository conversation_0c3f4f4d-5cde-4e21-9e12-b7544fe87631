import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Earnings overview screen showing driver's financial summary
class EarningsOverviewScreen extends StatefulWidget {
  const EarningsOverviewScreen({super.key});

  @override
  State<EarningsOverviewScreen> createState() => _EarningsOverviewScreenState();
}

class _EarningsOverviewScreenState extends State<EarningsOverviewScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  String _selectedPeriod = 'week';
  
  // Mock data - في التطبيق الحقيقي سيأتي من API
  final Map<String, Map<String, dynamic>> _earningsData = {
    'week': {
      'total': 2850.0,
      'trips': 45,
      'hours': 32.5,
      'average': 63.33,
      'growth': 12.5,
    },
    'month': {
      'total': 11200.0,
      'trips': 180,
      'hours': 128.0,
      'average': 62.22,
      'growth': 8.3,
    },
    'year': {
      'total': 125600.0,
      'trips': 2100,
      'hours': 1450.0,
      'average': 59.81,
      'growth': 15.7,
    },
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Earnings Overview', 'نظرة عامة على الأرباح'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Period Selector
                _buildPeriodSelector(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Main Earnings Card
                _buildMainEarningsCard(isDark, languageProvider),
                
                const SizedBox(height: 16),
                
                // Statistics Grid
                _buildStatisticsGrid(isDark, languageProvider),
                
                const SizedBox(height: 16),
                
                // Recent Transactions
                _buildRecentTransactions(isDark, languageProvider),
                
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodSelector(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildPeriodButton('week', languageProvider.getText('Week', 'أسبوع'), isDark, languageProvider),
          _buildPeriodButton('month', languageProvider.getText('Month', 'شهر'), isDark, languageProvider),
          _buildPeriodButton('year', languageProvider.getText('Year', 'سنة'), isDark, languageProvider),
        ],
      ),
    );
  }

  Widget _buildPeriodButton(String period, String label, bool isDark, LanguageProvider languageProvider) {
    final isSelected = _selectedPeriod == period;
    
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedPeriod = period),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected 
                ? const Color(0xFF11B96F) 
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              color: isSelected 
                  ? Colors.white 
                  : (isDark ? Colors.grey[400] : Colors.grey[600]),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainEarningsCard(bool isDark, LanguageProvider languageProvider) {
    final data = _earningsData[_selectedPeriod]!;
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF11B96F),
            const Color(0xFF11B96F).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF11B96F).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageProvider.getText('Total Earnings', 'إجمالي الأرباح'),
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      data['growth'] > 0 ? Icons.trending_up : Icons.trending_down,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${data['growth'].toStringAsFixed(1)}%',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${data['total'].toStringAsFixed(0)} ${languageProvider.getText('SAR', 'ريال')}',
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickStat(
                  languageProvider.getText('Trips', 'رحلات'),
                  '${data['trips']}',
                  Icons.local_taxi,
                ),
              ),
              Expanded(
                child: _buildQuickStat(
                  languageProvider.getText('Hours', 'ساعات'),
                  '${data['hours']}',
                  Icons.access_time,
                ),
              ),
              Expanded(
                child: _buildQuickStat(
                  languageProvider.getText('Avg/Trip', 'متوسط/رحلة'),
                  '${data['average'].toStringAsFixed(0)}',
                  Icons.trending_up,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white70,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsGrid(bool isDark, LanguageProvider languageProvider) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: languageProvider.getText('This Week', 'هذا الأسبوع'),
            value: '2,850 ${languageProvider.getText('SAR', 'ريال')}',
            subtitle: languageProvider.getText('45 trips', '45 رحلة'),
            icon: Icons.calendar_today,
            color: Colors.blue,
            isDark: isDark,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: languageProvider.getText('Last Payout', 'آخر دفعة'),
            value: '2,400 ${languageProvider.getText('SAR', 'ريال')}',
            subtitle: languageProvider.getText('3 days ago', 'منذ 3 أيام'),
            icon: Icons.payment,
            color: Colors.orange,
            isDark: isDark,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool isDark,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 11,
              color: isDark ? Colors.grey[500] : Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                languageProvider.getText('Recent Transactions', 'المعاملات الأخيرة'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to full transaction history
                },
                child: Text(
                  languageProvider.getText('View All', 'عرض الكل'),
                  style: const TextStyle(
                    color: Color(0xFF11B96F),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          _buildTransactionItem(
            title: languageProvider.getText('Weekly Payout', 'دفعة أسبوعية'),
            amount: '+2,400 ${languageProvider.getText('SAR', 'ريال')}',
            date: languageProvider.getText('Dec 18, 2024', '18 ديسمبر 2024'),
            icon: Icons.account_balance,
            isPositive: true,
            isDark: isDark,
          ),
          
          _buildTransactionItem(
            title: languageProvider.getText('Trip Earnings', 'أرباح الرحلات'),
            amount: '+450 ${languageProvider.getText('SAR', 'ريال')}',
            date: languageProvider.getText('Dec 17, 2024', '17 ديسمبر 2024'),
            icon: Icons.local_taxi,
            isPositive: true,
            isDark: isDark,
          ),
          
          _buildTransactionItem(
            title: languageProvider.getText('Service Fee', 'رسوم الخدمة'),
            amount: '-90 ${languageProvider.getText('SAR', 'ريال')}',
            date: languageProvider.getText('Dec 17, 2024', '17 ديسمبر 2024'),
            icon: Icons.receipt,
            isPositive: false,
            isDark: isDark,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem({
    required String title,
    required String amount,
    required String date,
    required IconData icon,
    required bool isPositive,
    required bool isDark,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF0F231A) : const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isPositive 
                  ? const Color(0xFF11B96F).withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: isPositive ? const Color(0xFF11B96F) : Colors.red,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  date,
                  style: TextStyle(
                    fontSize: 12,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: isPositive ? const Color(0xFF11B96F) : Colors.red,
            ),
          ),
        ],
      ),
    );
  }
}
