import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';

class FeedbackChartWidget extends StatelessWidget {
  final String title;
  final Color color;
  final String percentage;
  final bool isPositive;

  const FeedbackChartWidget({
    super.key,
    required this.title,
    required this.color,
    required this.percentage,
    required this.isPositive,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.inputBorder),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    Text(
                      'This Month',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: AppColors.textSecondary,
                      size: 14,
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Chart
          Expanded(
            child: _buildChart(),
          ),
          
          const SizedBox(height: 16),
          
          // Chart Labels
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              'jan', 'feb', 'mar', 'apr', 'may', 'jun',
              'jul', 'aug', 'sep', 'oct', 'nov', 'dec'
            ].map((month) => Text(
              month,
              style: GoogleFonts.inter(
                fontSize: 10,
                color: AppColors.textSecondary,
              ),
            )).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Percentage Indicator
          Row(
            children: [
              Icon(
                isPositive ? Icons.trending_up : Icons.trending_down,
                color: color,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                percentage,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChart() {
    return Container(
      child: CustomPaint(
        painter: FeedbackChartPainter(color: color),
        size: Size.infinite,
      ),
    );
  }
}

class FeedbackChartPainter extends CustomPainter {
  final Color color;

  FeedbackChartPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Sample data for bar chart
    final barData = [
      0.4, 0.6, 0.8, 0.5, 0.9, 0.7, 0.3, 0.8, 0.6, 0.9, 0.5, 0.7
    ];

    final barWidth = size.width / (barData.length * 2);
    final spacing = barWidth * 0.5;

    for (int i = 0; i < barData.length; i++) {
      final x = i * (barWidth + spacing) + spacing;
      final barHeight = size.height * barData[i];
      final y = size.height - barHeight;

      final rect = Rect.fromLTWH(x, y, barWidth, barHeight);
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(2)),
        paint,
      );
    }

    // Draw grid lines
    final gridPaint = Paint()
      ..color = AppColors.inputBorder
      ..strokeWidth = 1;

    // Horizontal grid lines
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // Y-axis labels
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    for (int i = 0; i <= 4; i++) {
      final value = (100 * (4 - i) / 4).toInt();
      textPainter.text = TextSpan(
        text: '$value',
        style: GoogleFonts.inter(
          fontSize: 10,
          color: AppColors.textSecondary,
        ),
      );
      textPainter.layout();
      
      final y = size.height * i / 4 - textPainter.height / 2;
      textPainter.paint(canvas, Offset(-30, y));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
