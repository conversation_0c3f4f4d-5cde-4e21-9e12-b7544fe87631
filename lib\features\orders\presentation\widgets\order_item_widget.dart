import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../screens/order_history_screen.dart';

class OrderItemWidget extends StatelessWidget {
  final OrderItem order;
  final VoidCallback onTap;

  const OrderItemWidget({
    super.key,
    required this.order,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          Checkbox(
            value: false,
            onChanged: (value) {},
            activeColor: AppColors.primary,
          ),
          
          const SizedBox(width: 16),
          
          // Menu with Image and Name
          Expanded(
            flex: 2,
            child: Row(
              children: [
                // Food Image
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: _getImageColor(),
                  ),
                  child: Center(
                    child: Icon(
                      _getImageIcon(),
                      color: AppColors.white,
                      size: 20,
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Menu Name
                Expanded(
                  child: Text(
                    order.menuName,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          
          // Date
          Expanded(
            flex: 2,
            child: Text(
              order.date,
              style: GoogleFonts.inter(
                fontSize: 13,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          // Address
          Expanded(
            flex: 2,
            child: Text(
              order.address,
              style: GoogleFonts.inter(
                fontSize: 13,
                color: AppColors.textSecondary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          
          // Total
          Expanded(
            flex: 1,
            child: Text(
              order.total,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          
          // Status
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getStatusColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    _getStatusText(),
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: _getStatusColor(),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Card Number
          Expanded(
            flex: 2,
            child: Text(
              order.cardNumber,
              style: GoogleFonts.robotoMono(
                fontSize: 13,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          // Action
          Expanded(
            flex: 1,
            child: IconButton(
              onPressed: onTap,
              icon: const Icon(
                Icons.more_horiz,
                color: AppColors.textSecondary,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (order.status) {
      case OrderStatus.completed:
        return AppColors.success;
      case OrderStatus.cancelled:
        return AppColors.error;
      case OrderStatus.delivering:
        return Colors.orange;
    }
  }

  String _getStatusText() {
    switch (order.status) {
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.delivering:
        return 'Delivering';
    }
  }

  Color _getImageColor() {
    // Generate color based on menu name hash
    final hash = order.menuName.hashCode;
    final colors = [
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.pink,
      Colors.teal,
      Colors.indigo,
    ];
    return colors[hash.abs() % colors.length];
  }

  IconData _getImageIcon() {
    final name = order.menuName.toLowerCase();
    if (name.contains('sugar') || name.contains('cola') || name.contains('boba')) {
      return Icons.local_drink;
    } else if (name.contains('ice cream')) {
      return Icons.icecream;
    } else if (name.contains('chicken') || name.contains('beef')) {
      return Icons.restaurant;
    } else if (name.contains('fries') || name.contains('chocolate')) {
      return Icons.fastfood;
    } else {
      return Icons.restaurant_menu;
    }
  }
}
