import React, { useState } from 'react';
import { 
  Bell, 
  Send, 
  Users, 
  Smartphone, 
  Mail, 
  MessageSquare,
  Calendar,
  Target,
  Eye,
  Edit,
  Trash2,
  Plus,
  Filter,
  Search,
  Clock,
  CheckCircle,
  AlertTriangle,
  Info,
  Zap,
  Globe,
  Building,
  Car,
  User,
  Settings,
  BarChart3,
  TrendingUp
} from 'lucide-react';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error' | 'promotion';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  targetAudience: 'all' | 'customers' | 'restaurants' | 'drivers' | 'specific';
  targetUsers?: string[];
  channels: ('push' | 'email' | 'sms' | 'in_app')[];
  status: 'draft' | 'scheduled' | 'sent' | 'failed';
  scheduledAt?: string;
  sentAt?: string;
  createdAt: string;
  createdBy: string;
  deliveryStats: {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
  };
  actionButton?: {
    text: string;
    url: string;
  };
}

interface NotificationTemplate {
  id: string;
  name: string;
  title: string;
  message: string;
  type: string;
  category: string;
  variables: string[];
}

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 'NOT001',
      title: 'عرض خاص - خصم 30%',
      message: 'استمتع بخصم 30% على جميع طلباتك اليوم فقط! استخدم الكود: SAVE30',
      type: 'promotion',
      priority: 'high',
      targetAudience: 'customers',
      channels: ['push', 'email'],
      status: 'sent',
      sentAt: '2024-01-20T10:00:00Z',
      createdAt: '2024-01-20T09:30:00Z',
      createdBy: 'فريق التسويق',
      deliveryStats: {
        sent: 15420,
        delivered: 14890,
        opened: 8934,
        clicked: 2156
      },
      actionButton: {
        text: 'اطلب الآن',
        url: '/restaurants'
      }
    },
    {
      id: 'NOT002',
      title: 'تحديث النظام',
      message: 'سيتم إجراء صيانة مجدولة للنظام غداً من الساعة 2:00 إلى 4:00 صباحاً',
      type: 'warning',
      priority: 'medium',
      targetAudience: 'all',
      channels: ['push', 'email', 'in_app'],
      status: 'scheduled',
      scheduledAt: '2024-01-21T18:00:00Z',
      createdAt: '2024-01-20T14:00:00Z',
      createdBy: 'فريق التطوير',
      deliveryStats: {
        sent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0
      }
    },
    {
      id: 'NOT003',
      title: 'مطعم جديد انضم للمنصة',
      message: 'مرحباً بمطعم "البحر الأبيض" الجديد في منطقة الرباط',
      type: 'info',
      priority: 'low',
      targetAudience: 'customers',
      channels: ['push'],
      status: 'sent',
      sentAt: '2024-01-19T16:30:00Z',
      createdAt: '2024-01-19T16:00:00Z',
      createdBy: 'فريق المطاعم',
      deliveryStats: {
        sent: 8750,
        delivered: 8234,
        opened: 4521,
        clicked: 892
      }
    }
  ]);

  const [templates, setTemplates] = useState<NotificationTemplate[]>([
    {
      id: 'TPL001',
      name: 'ترحيب بعميل جديد',
      title: 'مرحباً بك في وصلتي!',
      message: 'أهلاً وسهلاً {{customerName}}! استمتع بتجربة طلب الطعام الأفضل',
      type: 'welcome',
      category: 'عملاء',
      variables: ['customerName']
    },
    {
      id: 'TPL002',
      name: 'تأكيد الطلب',
      title: 'تم تأكيد طلبك',
      message: 'تم تأكيد طلبك رقم {{orderNumber}} وسيصل خلال {{estimatedTime}} دقيقة',
      type: 'order_confirmation',
      category: 'طلبات',
      variables: ['orderNumber', 'estimatedTime']
    },
    {
      id: 'TPL003',
      name: 'عرض ترويجي',
      title: 'عرض خاص لك!',
      message: 'احصل على خصم {{discountPercent}}% على طلبك التالي',
      type: 'promotion',
      category: 'تسويق',
      variables: ['discountPercent']
    }
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'info': return 'bg-blue-100 text-blue-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'success': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'promotion': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'info': return <Info className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'success': return <CheckCircle className="w-4 h-4" />;
      case 'error': return <AlertTriangle className="w-4 h-4" />;
      case 'promotion': return <Zap className="w-4 h-4" />;
      default: return <Bell className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800';
      case 'medium': return 'bg-blue-100 text-blue-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'sent': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAudienceIcon = (audience: string) => {
    switch (audience) {
      case 'customers': return <User className="w-4 h-4" />;
      case 'restaurants': return <Building className="w-4 h-4" />;
      case 'drivers': return <Car className="w-4 h-4" />;
      case 'all': return <Globe className="w-4 h-4" />;
      default: return <Users className="w-4 h-4" />;
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'push': return <Smartphone className="w-4 h-4" />;
      case 'email': return <Mail className="w-4 h-4" />;
      case 'sms': return <MessageSquare className="w-4 h-4" />;
      case 'in_app': return <Bell className="w-4 h-4" />;
      default: return <Bell className="w-4 h-4" />;
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    const matchesStatus = filterStatus === 'all' || notification.status === filterStatus;
    const matchesType = filterType === 'all' || notification.type === filterType;
    const matchesSearch = notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatus && matchesType && matchesSearch;
  });

  const calculateEngagementRate = (stats: any) => {
    if (stats.sent === 0) return 0;
    return ((stats.opened / stats.sent) * 100).toFixed(1);
  };

  const calculateClickRate = (stats: any) => {
    if (stats.opened === 0) return 0;
    return ((stats.clicked / stats.opened) * 100).toFixed(1);
  };

  const formatDateTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString('ar-MA'),
      time: date.toLocaleTimeString('ar-MA', { hour12: false })
    };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">مركز الإشعارات</h2>
          <p className="text-gray-600 rtl-font">إدارة وإرسال الإشعارات لجميع المستخدمين</p>
        </div>
        
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Settings className="w-4 h-4" />
            <span className="rtl-font">القوالب</span>
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary/90 transition-colors"
          >
            <Plus className="w-5 h-5" />
            <span className="font-medium rtl-font">إشعار جديد</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">إجمالي الإشعارات</p>
              <p className="text-2xl font-bold text-blue-600">{notifications.length}</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500 font-medium">+15%</span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">هذا الشهر</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Bell className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">تم الإرسال</p>
              <p className="text-2xl font-bold text-green-600">
                {notifications.filter(n => n.status === 'sent').length}
              </p>
              <div className="flex items-center mt-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500 font-medium">نشط</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <Send className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">مجدول</p>
              <p className="text-2xl font-bold text-yellow-600">
                {notifications.filter(n => n.status === 'scheduled').length}
              </p>
              <div className="flex items-center mt-2">
                <Clock className="w-4 h-4 text-yellow-500" />
                <span className="text-sm text-yellow-500 font-medium">قيد الانتظار</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <Calendar className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">معدل الفتح</p>
              <p className="text-2xl font-bold text-purple-600">
                {(() => {
                  const totalSent = notifications.reduce((sum, n) => sum + n.deliveryStats.sent, 0);
                  const totalOpened = notifications.reduce((sum, n) => sum + n.deliveryStats.opened, 0);
                  return totalSent > 0 ? ((totalOpened / totalSent) * 100).toFixed(1) : '0';
                })()}%
              </p>
              <div className="flex items-center mt-2">
                <Eye className="w-4 h-4 text-purple-500" />
                <span className="text-sm text-purple-500 font-medium">متوسط</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="البحث في الإشعارات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
            />
          </div>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="all">جميع الحالات</option>
            <option value="draft">مسودة</option>
            <option value="scheduled">مجدول</option>
            <option value="sent">تم الإرسال</option>
            <option value="failed">فشل</option>
          </select>
          
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
          >
            <option value="all">جميع الأنواع</option>
            <option value="info">معلومات</option>
            <option value="warning">تحذير</option>
            <option value="success">نجاح</option>
            <option value="error">خطأ</option>
            <option value="promotion">ترويجي</option>
          </select>
          
          <div className="text-sm text-gray-600 rtl-font flex items-center">
            عرض {filteredNotifications.length} من {notifications.length} إشعار
          </div>
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-4">
        {filteredNotifications.map((notification) => (
          <div key={notification.id} className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                  <div className={`p-2 rounded-lg ${getTypeColor(notification.type)}`}>
                    {getTypeIcon(notification.type)}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 rtl-font">{notification.title}</h3>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(notification.priority)}`}>
                        {notification.priority === 'low' ? 'منخفض' :
                         notification.priority === 'medium' ? 'متوسط' :
                         notification.priority === 'high' ? 'عالي' : 'عاجل'}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(notification.status)}`}>
                        {notification.status === 'draft' ? 'مسودة' :
                         notification.status === 'scheduled' ? 'مجدول' :
                         notification.status === 'sent' ? 'تم الإرسال' : 'فشل'}
                      </span>
                    </div>
                  </div>
                </div>
                
                <p className="text-gray-700 mb-4 rtl-font">{notification.message}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                      {getAudienceIcon(notification.targetAudience)}
                      <span className="text-sm font-medium text-gray-700 rtl-font">الجمهور المستهدف</span>
                    </div>
                    <span className="text-sm text-gray-600 rtl-font">
                      {notification.targetAudience === 'all' ? 'جميع المستخدمين' :
                       notification.targetAudience === 'customers' ? 'العملاء' :
                       notification.targetAudience === 'restaurants' ? 'المطاعم' :
                       notification.targetAudience === 'drivers' ? 'السائقين' : 'مستخدمين محددين'}
                    </span>
                  </div>
                  
                  <div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                      <Send className="w-4 h-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-700 rtl-font">قنوات الإرسال</span>
                    </div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      {notification.channels.map((channel, index) => (
                        <div key={index} className="flex items-center space-x-1 rtl:space-x-reverse">
                          {getChannelIcon(channel)}
                          <span className="text-xs text-gray-600">
                            {channel === 'push' ? 'دفع' :
                             channel === 'email' ? 'إيميل' :
                             channel === 'sms' ? 'رسائل' : 'داخلي'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                      <Clock className="w-4 h-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-700 rtl-font">التوقيت</span>
                    </div>
                    <span className="text-sm text-gray-600">
                      {notification.sentAt ? 
                        `تم الإرسال: ${formatDateTime(notification.sentAt).date}` :
                        notification.scheduledAt ?
                        `مجدول: ${formatDateTime(notification.scheduledAt).date}` :
                        `تم الإنشاء: ${formatDateTime(notification.createdAt).date}`
                      }
                    </span>
                  </div>
                </div>
                
                {notification.status === 'sent' && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-xl">
                    <h4 className="font-medium text-gray-800 mb-3 rtl-font">إحصائيات التسليم</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">{notification.deliveryStats.sent.toLocaleString()}</div>
                        <div className="text-xs text-gray-600 rtl-font">تم الإرسال</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">{notification.deliveryStats.delivered.toLocaleString()}</div>
                        <div className="text-xs text-gray-600 rtl-font">تم التسليم</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-purple-600">{notification.deliveryStats.opened.toLocaleString()}</div>
                        <div className="text-xs text-gray-600 rtl-font">تم الفتح ({calculateEngagementRate(notification.deliveryStats)}%)</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-orange-600">{notification.deliveryStats.clicked.toLocaleString()}</div>
                        <div className="text-xs text-gray-600 rtl-font">تم النقر ({calculateClickRate(notification.deliveryStats)}%)</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex items-center space-x-2 rtl:space-x-reverse ml-4">
                <button
                  onClick={() => setSelectedNotification(notification)}
                  className="text-blue-600 hover:text-blue-800"
                  title="عرض التفاصيل"
                >
                  <Eye className="w-5 h-5" />
                </button>
                <button className="text-green-600 hover:text-green-800" title="تعديل">
                  <Edit className="w-5 h-5" />
                </button>
                <button className="text-red-600 hover:text-red-800" title="حذف">
                  <Trash2 className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default NotificationCenter;
