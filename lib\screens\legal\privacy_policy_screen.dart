import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Privacy Policy screen
class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Privacy Policy', 'سياسة الخصوصية'),
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Center(
                  child: Column(
                    children: [
                      const Icon(
                        Icons.privacy_tip,
                        size: 60,
                        color: Color(0xFF11B96F),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        languageProvider.getText('Privacy Policy', 'سياسة الخصوصية'),
                        style: DriverTypography.getContextualStyle(
                          context,
                          fontSize: DriverTypography.titleLarge,
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        languageProvider.getText(
                          'Last updated: December 7, 2024',
                          'آخر تحديث: 7 ديسمبر 2024'
                        ),
                        style: DriverTypography.getContextualStyle(
                          context,
                          fontSize: DriverTypography.bodyMedium,
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 30),
                
                // Privacy policy content
                _buildSection(
                  title: languageProvider.getText('1. Information We Collect', '1. المعلومات التي نجمعها'),
                  content: languageProvider.getText(
                    'We collect information you provide directly to us, such as:\n• Personal information (name, email, phone number)\n• Driver\'s license and vehicle information\n• Location data when using the app\n• Trip and earnings history\n• Device information and usage data',
                    'نجمع المعلومات التي تقدمها لنا مباشرة، مثل:\n• المعلومات الشخصية (الاسم، البريد الإلكتروني، رقم الهاتف)\n• معلومات رخصة القيادة والمركبة\n• بيانات الموقع عند استخدام التطبيق\n• تاريخ الرحلات والأرباح\n• معلومات الجهاز وبيانات الاستخدام'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('2. How We Use Your Information', '2. كيف نستخدم معلوماتك'),
                  content: languageProvider.getText(
                    'We use the information we collect to:\n• Provide and improve our services\n• Process payments and manage your account\n• Communicate with you about trips and updates\n• Ensure safety and security\n• Comply with legal requirements\n• Analyze usage patterns and improve the app',
                    'نستخدم المعلومات التي نجمعها من أجل:\n• توفير وتحسين خدماتنا\n• معالجة المدفوعات وإدارة حسابك\n• التواصل معك حول الرحلات والتحديثات\n• ضمان السلامة والأمان\n• الامتثال للمتطلبات القانونية\n• تحليل أنماط الاستخدام وتحسين التطبيق'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('3. Information Sharing', '3. مشاركة المعلومات'),
                  content: languageProvider.getText(
                    'We may share your information with:\n• Customers for trip coordination\n• Service providers who help us operate\n• Law enforcement when required by law\n• Business partners for legitimate purposes\n\nWe do not sell your personal information to third parties.',
                    'قد نشارك معلوماتك مع:\n• العملاء لتنسيق الرحلات\n• مقدمي الخدمات الذين يساعدوننا في التشغيل\n• إنفاذ القانون عند الطلب قانونياً\n• الشركاء التجاريين لأغراض مشروعة\n\nنحن لا نبيع معلوماتك الشخصية لأطراف ثالثة.'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('4. Data Security', '4. أمان البيانات'),
                  content: languageProvider.getText(
                    'We implement appropriate security measures to protect your information:\n• Encryption of sensitive data\n• Secure data transmission\n• Regular security audits\n• Access controls and authentication\n• Employee training on data protection',
                    'نطبق تدابير أمنية مناسبة لحماية معلوماتك:\n• تشفير البيانات الحساسة\n• نقل آمن للبيانات\n• عمليات تدقيق أمنية منتظمة\n• ضوابط الوصول والمصادقة\n• تدريب الموظفين على حماية البيانات'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('5. Your Rights', '5. حقوقك'),
                  content: languageProvider.getText(
                    'You have the right to:\n• Access your personal information\n• Correct inaccurate information\n• Delete your account and data\n• Opt out of marketing communications\n• Request data portability\n• File complaints with regulatory authorities',
                    'لديك الحق في:\n• الوصول إلى معلوماتك الشخصية\n• تصحيح المعلومات غير الدقيقة\n• حذف حسابك وبياناتك\n• إلغاء الاشتراك في الاتصالات التسويقية\n• طلب قابلية نقل البيانات\n• تقديم شكاوى للسلطات التنظيمية'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('6. Data Retention', '6. الاحتفاظ بالبيانات'),
                  content: languageProvider.getText(
                    'We retain your information for as long as necessary to:\n• Provide our services\n• Comply with legal obligations\n• Resolve disputes\n• Enforce our agreements\n\nWhen no longer needed, we securely delete or anonymize your data.',
                    'نحتفظ بمعلوماتك طالما كان ذلك ضرورياً من أجل:\n• تقديم خدماتنا\n• الامتثال للالتزامات القانونية\n• حل النزاعات\n• إنفاذ اتفاقياتنا\n\nعندما لا تعود هناك حاجة، نحذف بياناتك بشكل آمن أو نجعلها مجهولة.'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('7. Contact Us', '7. اتصل بنا'),
                  content: languageProvider.getText(
                    'If you have questions about this Privacy Policy, please contact us:\n\nEmail: <EMAIL>\nPhone: +966 123 456 789\nAddress: Riyadh, Saudi Arabia\n\nData Protection Officer: <EMAIL>',
                    'إذا كان لديك أسئلة حول سياسة الخصوصية هذه، يرجى الاتصال بنا:\n\nالبريد الإلكتروني: <EMAIL>\nالهاتف: +966 123 456 789\nالعنوان: الرياض، المملكة العربية السعودية\n\nمسؤول حماية البيانات: <EMAIL>'
                  ),
                  isDark: isDark,
                ),
                
                const SizedBox(height: 30),
                
                // Privacy notice
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.shield_outlined,
                        color: Colors.blue,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          languageProvider.getText(
                            'Your privacy is important to us. We are committed to protecting your personal information and being transparent about how we use it.',
                            'خصوصيتك مهمة بالنسبة لنا. نحن ملتزمون بحماية معلوماتك الشخصية والشفافية حول كيفية استخدامها.'
                          ),
                          style: DriverTypography.getContextualStyle(
                            context,
                            fontSize: DriverTypography.bodyMedium,
                            color: Colors.blue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    required bool isDark,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: DriverTypography.titleMedium,
              fontWeight: FontWeight.bold,
              color: Color(0xFF11B96F),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: TextStyle(
              fontSize: DriverTypography.bodyMedium,
              color: isDark ? Colors.grey[300] : Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
