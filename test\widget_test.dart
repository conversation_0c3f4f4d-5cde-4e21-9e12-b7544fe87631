// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:wasslti_partner/main.dart';
import 'package:wasslti_partner/screens/auth/otp_verification_screen.dart';
import 'package:wasslti_partner/constants/driver_themes.dart';

void main() {
  testWidgets('Login screen loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const WassltiPartnerApp());

    // Verify that the login screen elements are present.
    expect(find.text('Welcome to Wasslti Partner'), findsOneWidget);
    expect(find.text('Log in to start delivering orders.'), findsOneWidget);
    expect(find.text('Phone Number'), findsOneWidget);
    expect(find.text('Password'), findsOneWidget);
    expect(find.text('Log In'), findsOneWidget);
  });

  testWidgets('OTP verification screen loads correctly', (WidgetTester tester) async {
    // Build the OTP screen with test data.
    await tester.pumpWidget(
      MaterialApp(
        theme: DriverThemes.lightTheme,
        home: const OtpVerificationScreen(phoneNumber: '+212 6XXXXXXXX'),
      ),
    );

    // Verify that the OTP screen elements are present.
    expect(find.text('Verify Your Number'), findsOneWidget);
    expect(find.text('Enter the 6-digit code we sent to'), findsOneWidget);
    expect(find.text('+212 XXXXXX XXX'), findsOneWidget);
    expect(find.text('Verify'), findsOneWidget);

    // Verify that there are 6 OTP input fields.
    expect(find.byType(TextFormField), findsNWidgets(6));
  });
}
