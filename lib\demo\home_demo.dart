import 'package:flutter/material.dart';
import '../constants/driver_themes.dart';
import '../screens/home/<USER>';

/// تطبيق تجريبي لعرض الشاشة الرئيسية للسائق
/// مع إمكانية التبديل بين الأوضاع المختلفة
class HomeDemoApp extends StatefulWidget {
  const HomeDemoApp({super.key});

  @override
  State<HomeDemoApp> createState() => _HomeDemoAppState();
}

class _HomeDemoAppState extends State<HomeDemoApp> {
  ThemeMode _themeMode = ThemeMode.system;
  bool _isNightDriving = false;
  String _selectedDriverName = 'Yassine';

  final List<String> _driverNames = [
    'Yassine',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wasslti Partner - Home Demo',
      debugShowCheckedModeBanner: false,
      
      // استخدام نظام الثيمات المخصص
      theme: DriverThemes.getTheme(ThemeMode.light, isNightDriving: _isNightDriving),
      darkTheme: DriverThemes.getTheme(ThemeMode.dark, isNightDriving: _isNightDriving),
      themeMode: _themeMode,
      
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Driver Home Demo'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: [
            // اختيار اسم السائق
            PopupMenuButton<String>(
              icon: const Icon(Icons.person),
              onSelected: (value) {
                setState(() {
                  _selectedDriverName = value;
                });
              },
              itemBuilder: (context) => _driverNames.map((name) {
                return PopupMenuItem(
                  value: name,
                  child: Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 16,
                        color: _selectedDriverName == name 
                            ? const Color(0xFF11B96F) 
                            : null,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        name,
                        style: TextStyle(
                          color: _selectedDriverName == name 
                              ? const Color(0xFF11B96F) 
                              : null,
                          fontWeight: _selectedDriverName == name 
                              ? FontWeight.bold 
                              : null,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
            
            // زر تبديل الوضع
            PopupMenuButton<String>(
              icon: const Icon(Icons.palette),
              onSelected: (value) {
                setState(() {
                  switch (value) {
                    case 'light':
                      _themeMode = ThemeMode.light;
                      _isNightDriving = false;
                      break;
                    case 'dark':
                      _themeMode = ThemeMode.dark;
                      _isNightDriving = false;
                      break;
                    case 'system':
                      _themeMode = ThemeMode.system;
                      _isNightDriving = false;
                      break;
                    case 'night':
                      _themeMode = ThemeMode.dark;
                      _isNightDriving = true;
                      break;
                  }
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'light',
                  child: Row(
                    children: [
                      Icon(Icons.light_mode, size: 20),
                      SizedBox(width: 8),
                      Text('Light Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'dark',
                  child: Row(
                    children: [
                      Icon(Icons.dark_mode, size: 20),
                      SizedBox(width: 8),
                      Text('Dark Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'system',
                  child: Row(
                    children: [
                      Icon(Icons.settings, size: 20),
                      SizedBox(width: 8),
                      Text('System'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'night',
                  child: Row(
                    children: [
                      Icon(Icons.nights_stay, size: 20),
                      SizedBox(width: 8),
                      Text('Night Driving'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: DriverHomeScreenTemp(
          driverName: _selectedDriverName,
        ),
        
        // معلومات الوضع الحالي
        bottomNavigationBar: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // معلومات الوضع
              Row(
                children: [
                  Icon(
                    _getCurrentModeIcon(),
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getCurrentModeText(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
              
              // اسم السائق المحدد
              Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 16,
                    color: const Color(0xFF11B96F),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _selectedDriverName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: const Color(0xFF11B96F),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCurrentModeIcon() {
    if (_isNightDriving) return Icons.nights_stay;
    
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.settings;
    }
  }

  String _getCurrentModeText() {
    if (_isNightDriving) return 'Night Driving Mode';
    
    switch (_themeMode) {
      case ThemeMode.light:
        return 'Light Mode';
      case ThemeMode.dark:
        return 'Dark Mode';
      case ThemeMode.system:
        return 'System Mode';
    }
  }
}

/// دالة لتشغيل التطبيق التجريبي
void runHomeDemo() {
  runApp(const HomeDemoApp());
}

/// مثال على الاستخدام:
/// 
/// ```dart
/// // في main.dart أو أي ملف آخر
/// import 'demo/home_demo.dart';
/// 
/// void main() {
///   runHomeDemo();
/// }
/// ```
