import React, { useState } from 'react';
import { 
  Truck, 
  MapPin, 
  Clock, 
  Route, 
  Navigation,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Filter,
  Search,
  Calendar,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Settings,
  Eye,
  Edit,
  Phone,
  MessageCircle,
  Car,
  Building,
  User,
  Target,
  Timer,
  DollarSign
} from 'lucide-react';

interface DeliveryZone {
  id: string;
  name: string;
  coordinates: { lat: number; lng: number }[];
  deliveryFee: number;
  estimatedTime: number;
  isActive: boolean;
  driverCount: number;
  orderCount: number;
  averageRating: number;
}

interface DeliveryRoute {
  id: string;
  driverId: string;
  driverName: string;
  orders: string[];
  startTime: string;
  estimatedEndTime: string;
  actualEndTime?: string;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  totalDistance: number;
  totalTime: number;
  efficiency: number;
}

interface DeliveryMetrics {
  averageDeliveryTime: number;
  onTimeDeliveryRate: number;
  customerSatisfaction: number;
  totalDeliveries: number;
  activeDrivers: number;
  pendingOrders: number;
  completedToday: number;
  revenue: number;
}

const DeliveryManagement = () => {
  const [deliveryZones, setDeliveryZones] = useState<DeliveryZone[]>([
    {
      id: 'ZONE001',
      name: 'وسط المدينة',
      coordinates: [
        { lat: 33.5731, lng: -7.5898 },
        { lat: 33.5851, lng: -7.6034 },
        { lat: 33.5641, lng: -7.5756 }
      ],
      deliveryFee: 15,
      estimatedTime: 25,
      isActive: true,
      driverCount: 12,
      orderCount: 45,
      averageRating: 4.6
    },
    {
      id: 'ZONE002',
      name: 'الأحياء الشمالية',
      coordinates: [
        { lat: 33.5891, lng: -7.6134 },
        { lat: 33.5951, lng: -7.6234 },
        { lat: 33.5781, lng: -7.5896 }
      ],
      deliveryFee: 20,
      estimatedTime: 35,
      isActive: true,
      driverCount: 8,
      orderCount: 28,
      averageRating: 4.4
    },
    {
      id: 'ZONE003',
      name: 'المنطقة الصناعية',
      coordinates: [
        { lat: 33.5671, lng: -7.5776 },
        { lat: 33.5721, lng: -7.5826 },
        { lat: 33.5621, lng: -7.5726 }
      ],
      deliveryFee: 25,
      estimatedTime: 40,
      isActive: false,
      driverCount: 3,
      orderCount: 8,
      averageRating: 4.1
    }
  ]);

  const [deliveryRoutes, setDeliveryRoutes] = useState<DeliveryRoute[]>([
    {
      id: 'ROUTE001',
      driverId: 'DRV001',
      driverName: 'أحمد محمد',
      orders: ['ORD123', 'ORD124', 'ORD125'],
      startTime: '2024-01-20T12:00:00Z',
      estimatedEndTime: '2024-01-20T13:30:00Z',
      status: 'in_progress',
      totalDistance: 15.5,
      totalTime: 90,
      efficiency: 85
    },
    {
      id: 'ROUTE002',
      driverId: 'DRV002',
      driverName: 'يوسف العلوي',
      orders: ['ORD126', 'ORD127'],
      startTime: '2024-01-20T11:30:00Z',
      estimatedEndTime: '2024-01-20T12:45:00Z',
      actualEndTime: '2024-01-20T12:40:00Z',
      status: 'completed',
      totalDistance: 12.3,
      totalTime: 70,
      efficiency: 92
    }
  ]);

  const [metrics, setMetrics] = useState<DeliveryMetrics>({
    averageDeliveryTime: 28,
    onTimeDeliveryRate: 87.5,
    customerSatisfaction: 4.6,
    totalDeliveries: 1250,
    activeDrivers: 23,
    pendingOrders: 18,
    completedToday: 156,
    revenue: 45680
  });

  const [selectedTab, setSelectedTab] = useState('zones');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'planned': return <Clock className="w-4 h-4" />;
      case 'in_progress': return <Navigation className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'cancelled': return <XCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 90) return 'text-green-600';
    if (efficiency >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}س ${mins}د` : `${mins}د`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">إدارة التوصيل واللوجستيات</h2>
          <p className="text-gray-600 rtl-font">تحسين عمليات التوصيل وإدارة المناطق والمسارات</p>
        </div>
        
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <RefreshCw className="w-4 h-4" />
            <span className="rtl-font">تحديث</span>
          </button>
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Settings className="w-4 h-4" />
            <span className="rtl-font">إعدادات</span>
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">متوسط وقت التوصيل</p>
              <p className="text-2xl font-bold text-blue-600">{metrics.averageDeliveryTime} دقيقة</p>
              <div className="flex items-center mt-2">
                <TrendingDown className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500 font-medium">-5%</span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">تحسن</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">معدل التوصيل في الوقت</p>
              <p className="text-2xl font-bold text-green-600">{metrics.onTimeDeliveryRate}%</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500 font-medium">+3%</span>
                <span className="text-gray-500 text-sm mr-1 rtl-font">تحسن</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <Target className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">السائقين النشطين</p>
              <p className="text-2xl font-bold text-purple-600">{metrics.activeDrivers}</p>
              <div className="flex items-center mt-2">
                <Car className="w-4 h-4 text-purple-500" />
                <span className="text-sm text-purple-500 font-medium">متاح</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Car className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm rtl-font">الطلبات المعلقة</p>
              <p className="text-2xl font-bold text-orange-600">{metrics.pendingOrders}</p>
              <div className="flex items-center mt-2">
                <Timer className="w-4 h-4 text-orange-500" />
                <span className="text-sm text-orange-500 font-medium">قيد المعالجة</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
              <Timer className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 rtl:space-x-reverse px-6">
            <button
              onClick={() => setSelectedTab('zones')}
              className={`py-4 px-2 border-b-2 font-medium text-sm ${
                selectedTab === 'zones'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              } rtl-font`}
            >
              مناطق التوصيل
            </button>
            <button
              onClick={() => setSelectedTab('routes')}
              className={`py-4 px-2 border-b-2 font-medium text-sm ${
                selectedTab === 'routes'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              } rtl-font`}
            >
              المسارات والرحلات
            </button>
            <button
              onClick={() => setSelectedTab('analytics')}
              className={`py-4 px-2 border-b-2 font-medium text-sm ${
                selectedTab === 'analytics'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              } rtl-font`}
            >
              تحليلات الأداء
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* Delivery Zones Tab */}
          {selectedTab === 'zones' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-800 rtl-font">مناطق التوصيل</h3>
                <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                  <MapPin className="w-4 h-4" />
                  <span className="rtl-font">إضافة منطقة</span>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {deliveryZones.map((zone) => (
                  <div key={zone.id} className="border border-gray-200 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-gray-900 rtl-font">{zone.name}</h4>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <span className={`w-3 h-3 rounded-full ${zone.isActive ? 'bg-green-500' : 'bg-red-500'}`}></span>
                        <span className="text-sm text-gray-600 rtl-font">
                          {zone.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 rtl-font">رسوم التوصيل</span>
                        <span className="font-medium text-green-600">{formatCurrency(zone.deliveryFee)}</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 rtl-font">الوقت المتوقع</span>
                        <span className="font-medium text-blue-600">{formatTime(zone.estimatedTime)}</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 rtl-font">السائقين المتاحين</span>
                        <span className="font-medium text-purple-600">{zone.driverCount}</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 rtl-font">الطلبات النشطة</span>
                        <span className="font-medium text-orange-600">{zone.orderCount}</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 rtl-font">التقييم</span>
                        <span className="font-medium text-yellow-600">⭐ {zone.averageRating}</span>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-100 flex items-center space-x-2 rtl:space-x-reverse">
                      <button className="text-blue-600 hover:text-blue-800" title="عرض">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-800" title="تعديل">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="text-purple-600 hover:text-purple-800" title="إعدادات">
                        <Settings className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Delivery Routes Tab */}
          {selectedTab === 'routes' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-800 rtl-font">المسارات والرحلات</h3>
                <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                  <Route className="w-4 h-4" />
                  <span className="rtl-font">تحسين المسارات</span>
                </button>
              </div>

              <div className="space-y-4">
                {deliveryRoutes.map((route) => (
                  <div key={route.id} className="border border-gray-200 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                          <Car className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 rtl-font">{route.driverName}</h4>
                          <p className="text-sm text-gray-600">رحلة #{route.id}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4 rtl:space-x-reverse">
                        <span className={`inline-flex items-center space-x-1 rtl:space-x-reverse px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(route.status)}`}>
                          {getStatusIcon(route.status)}
                          <span className="rtl-font">
                            {route.status === 'planned' ? 'مخطط' :
                             route.status === 'in_progress' ? 'جاري' :
                             route.status === 'completed' ? 'مكتمل' : 'ملغي'}
                          </span>
                        </span>
                        
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <button className="text-blue-600 hover:text-blue-800" title="اتصال">
                            <Phone className="w-4 h-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-800" title="رسالة">
                            <MessageCircle className="w-4 h-4" />
                          </button>
                          <button className="text-purple-600 hover:text-purple-800" title="تتبع">
                            <Navigation className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <span className="text-sm text-gray-600 rtl-font">عدد الطلبات</span>
                        <div className="font-semibold text-blue-600">{route.orders.length}</div>
                      </div>
                      
                      <div>
                        <span className="text-sm text-gray-600 rtl-font">المسافة الإجمالية</span>
                        <div className="font-semibold text-green-600">{route.totalDistance} كم</div>
                      </div>
                      
                      <div>
                        <span className="text-sm text-gray-600 rtl-font">الوقت المتوقع</span>
                        <div className="font-semibold text-orange-600">{formatTime(route.totalTime)}</div>
                      </div>
                      
                      <div>
                        <span className="text-sm text-gray-600 rtl-font">الكفاءة</span>
                        <div className={`font-semibold ${getEfficiencyColor(route.efficiency)}`}>
                          {route.efficiency}%
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-100">
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span className="rtl-font">
                          بدء: {new Date(route.startTime).toLocaleTimeString('ar-MA', { hour12: false })}
                        </span>
                        <span className="rtl-font">
                          {route.actualEndTime ? 
                            `انتهى: ${new Date(route.actualEndTime).toLocaleTimeString('ar-MA', { hour12: false })}` :
                            `متوقع: ${new Date(route.estimatedEndTime).toLocaleTimeString('ar-MA', { hour12: false })}`
                          }
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Analytics Tab */}
          {selectedTab === 'analytics' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-800 rtl-font">تحليلات الأداء</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium text-gray-800 rtl-font">توزيع أوقات التوصيل</h4>
                    <BarChart3 className="w-5 h-5 text-gray-400" />
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 rtl-font">أقل من 20 دقيقة</span>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div className="bg-green-500 h-2 rounded-full" style={{ width: '35%' }}></div>
                        </div>
                        <span className="text-sm font-medium">35%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 rtl-font">20-30 دقيقة</span>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }}></div>
                        </div>
                        <span className="text-sm font-medium">45%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 rtl-font">أكثر من 30 دقيقة</span>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div className="bg-red-500 h-2 rounded-full" style={{ width: '20%' }}></div>
                        </div>
                        <span className="text-sm font-medium">20%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium text-gray-800 rtl-font">كفاءة السائقين</h4>
                    <PieChart className="w-5 h-5 text-gray-400" />
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 rtl-font">ممتاز (90%+)</span>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div className="bg-green-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                        </div>
                        <span className="text-sm font-medium">60%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 rtl-font">جيد (75-89%)</span>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '30%' }}></div>
                        </div>
                        <span className="text-sm font-medium">30%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 rtl-font">يحتاج تحسين</span>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div className="bg-red-500 h-2 rounded-full" style={{ width: '10%' }}></div>
                        </div>
                        <span className="text-sm font-medium">10%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DeliveryManagement;
