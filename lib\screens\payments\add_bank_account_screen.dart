import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Add bank account screen for payment setup
class AddBankAccountScreen extends StatefulWidget {
  const AddBankAccountScreen({super.key});

  @override
  State<AddBankAccountScreen> createState() => _AddBankAccountScreenState();
}

class _AddBankAccountScreenState extends State<AddBankAccountScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final _formKey = GlobalKey<FormState>();
  final _accountHolderController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _ibanController = TextEditingController();
  final _bankNameController = TextEditingController();
  final _branchController = TextEditingController();
  
  String _selectedBankType = 'local';
  bool _isLoading = false;
  bool _agreeToTerms = false;

  // Saudi banks list
  final List<Map<String, String>> _saudiBanks = [
    {'name': 'Al Rajhi Bank', 'nameAr': 'مصرف الراجحي', 'code': 'RJHI'},
    {'name': 'National Commercial Bank', 'nameAr': 'البنك الأهلي التجاري', 'code': 'NCB'},
    {'name': 'Riyad Bank', 'nameAr': 'بنك الرياض', 'code': 'RIBL'},
    {'name': 'SAMBA Financial Group', 'nameAr': 'مجموعة سامبا المالية', 'code': 'SAMBA'},
    {'name': 'Banque Saudi Fransi', 'nameAr': 'البنك السعودي الفرنسي', 'code': 'BSFR'},
    {'name': 'Arab National Bank', 'nameAr': 'البنك العربي الوطني', 'code': 'ARNB'},
    {'name': 'Saudi Investment Bank', 'nameAr': 'البنك السعودي للاستثمار', 'code': 'SIBC'},
    {'name': 'Alinma Bank', 'nameAr': 'بنك الإنماء', 'code': 'ALINMA'},
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _accountHolderController.dispose();
    _accountNumberController.dispose();
    _ibanController.dispose();
    _bankNameController.dispose();
    _branchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Add Bank Account', 'إضافة حساب بنكي'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  // Header
                  _buildHeader(isDark, languageProvider),
                  
                  const SizedBox(height: 20),
                  
                  // Bank Type Selector
                  _buildBankTypeSelector(isDark, languageProvider),
                  
                  const SizedBox(height: 20),
                  
                  // Form Fields
                  _buildFormFields(isDark, languageProvider),
                  
                  const SizedBox(height: 20),
                  
                  // Terms Agreement
                  _buildTermsAgreement(isDark, languageProvider),
                  
                  const SizedBox(height: 20),
                  
                  // Add Account Button
                  _buildAddAccountButton(isDark, languageProvider),
                  
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue[800]!,
            Colors.blue[600]!,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.account_balance,
            size: 60,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('Bank Account Setup', 'إعداد الحساب البنكي'),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Add your bank account to receive payments securely',
              'أضف حسابك البنكي لاستلام المدفوعات بأمان'
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBankTypeSelector(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildBankTypeButton(
            'local',
            languageProvider.getText('Saudi Bank', 'بنك سعودي'),
            Icons.location_on,
            isDark,
            languageProvider,
          ),
          _buildBankTypeButton(
            'international',
            languageProvider.getText('International', 'دولي'),
            Icons.public,
            isDark,
            languageProvider,
          ),
        ],
      ),
    );
  }

  Widget _buildBankTypeButton(
    String type,
    String label,
    IconData icon,
    bool isDark,
    LanguageProvider languageProvider,
  ) {
    final isSelected = _selectedBankType == type;
    
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedBankType = type),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected 
                ? const Color(0xFF11B96F) 
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 18,
                color: isSelected 
                    ? Colors.white 
                    : (isDark ? Colors.grey[400] : Colors.grey[600]),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color: isSelected 
                      ? Colors.white 
                      : (isDark ? Colors.grey[400] : Colors.grey[600]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormFields(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageProvider.getText('Account Information', 'معلومات الحساب'),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 20),
          
          // Account Holder Name
          _buildTextField(
            controller: _accountHolderController,
            label: languageProvider.getText('Account Holder Name', 'اسم صاحب الحساب'),
            hint: languageProvider.getText('Enter full name as on bank records', 'أدخل الاسم الكامل كما في سجلات البنك'),
            icon: Icons.person,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return languageProvider.getText('Please enter account holder name', 'يرجى إدخال اسم صاحب الحساب');
              }
              return null;
            },
            isDark: isDark,
          ),
          
          const SizedBox(height: 16),
          
          // Bank Selection or Name
          if (_selectedBankType == 'local')
            _buildBankDropdown(isDark, languageProvider)
          else
            _buildTextField(
              controller: _bankNameController,
              label: languageProvider.getText('Bank Name', 'اسم البنك'),
              hint: languageProvider.getText('Enter bank name', 'أدخل اسم البنك'),
              icon: Icons.account_balance,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return languageProvider.getText('Please enter bank name', 'يرجى إدخال اسم البنك');
                }
                return null;
              },
              isDark: isDark,
            ),
          
          const SizedBox(height: 16),
          
          // Account Number
          _buildTextField(
            controller: _accountNumberController,
            label: languageProvider.getText('Account Number', 'رقم الحساب'),
            hint: languageProvider.getText('Enter account number', 'أدخل رقم الحساب'),
            icon: Icons.numbers,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return languageProvider.getText('Please enter account number', 'يرجى إدخال رقم الحساب');
              }
              if (value.length < 10) {
                return languageProvider.getText('Account number must be at least 10 digits', 'رقم الحساب يجب أن يكون 10 أرقام على الأقل');
              }
              return null;
            },
            isDark: isDark,
          ),
          
          const SizedBox(height: 16),
          
          // IBAN
          _buildTextField(
            controller: _ibanController,
            label: languageProvider.getText('IBAN', 'رقم الآيبان'),
            hint: languageProvider.getText('SA00 0000 0000 0000 0000 0000', 'SA00 0000 0000 0000 0000 0000'),
            icon: Icons.credit_card,
            textCapitalization: TextCapitalization.characters,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return languageProvider.getText('Please enter IBAN', 'يرجى إدخال رقم الآيبان');
              }
              if (_selectedBankType == 'local' && !value.startsWith('SA')) {
                return languageProvider.getText('Saudi IBAN must start with SA', 'الآيبان السعودي يجب أن يبدأ بـ SA');
              }
              return null;
            },
            isDark: isDark,
          ),
          
          if (_selectedBankType == 'local') ...[
            const SizedBox(height: 16),
            _buildTextField(
              controller: _branchController,
              label: languageProvider.getText('Branch Name (Optional)', 'اسم الفرع (اختياري)'),
              hint: languageProvider.getText('Enter branch name', 'أدخل اسم الفرع'),
              icon: Icons.location_city,
              isDark: isDark,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBankDropdown(bool isDark, LanguageProvider languageProvider) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: languageProvider.getText('Select Bank', 'اختر البنك'),
        prefixIcon: const Icon(Icons.account_balance, color: Color(0xFF11B96F)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
        ),
        filled: true,
        fillColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF8F9FA),
      ),
      items: _saudiBanks.map((bank) {
        return DropdownMenuItem<String>(
          value: bank['code'],
          child: Text(
            languageProvider.isArabic ? bank['nameAr']! : bank['name']!,
            style: TextStyle(
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          final selectedBank = _saudiBanks.firstWhere((bank) => bank['code'] == value);
          _bankNameController.text = languageProvider.isArabic 
              ? selectedBank['nameAr']! 
              : selectedBank['name']!;
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return languageProvider.getText('Please select a bank', 'يرجى اختيار بنك');
        }
        return null;
      },
      dropdownColor: isDark ? const Color(0xFF1B3B2E) : Colors.white,
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required bool isDark,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    TextCapitalization? textCapitalization,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      textCapitalization: textCapitalization ?? TextCapitalization.none,
      style: TextStyle(
        color: isDark ? Colors.white : Colors.black87,
      ),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: const Color(0xFF11B96F)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF8F9FA),
        labelStyle: TextStyle(
          color: isDark ? Colors.grey[400] : Colors.grey[600],
        ),
        hintStyle: TextStyle(
          color: isDark ? Colors.grey[500] : Colors.grey[500],
        ),
      ),
    );
  }

  Widget _buildTermsAgreement(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Checkbox(
            value: _agreeToTerms,
            onChanged: (value) => setState(() => _agreeToTerms = value ?? false),
            activeColor: const Color(0xFF11B96F),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _agreeToTerms = !_agreeToTerms),
              child: Text(
                languageProvider.getText(
                  'I agree to the Terms of Service and Privacy Policy. I confirm that the provided bank account information is accurate and belongs to me.',
                  'أوافق على شروط الخدمة وسياسة الخصوصية. أؤكد أن معلومات الحساب البنكي المقدمة دقيقة وتخصني.'
                ),
                style: TextStyle(
                  fontSize: 14,
                  color: isDark ? Colors.grey[300] : Colors.grey[700],
                  height: 1.4,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddAccountButton(bool isDark, LanguageProvider languageProvider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _agreeToTerms && !_isLoading ? _addBankAccount : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF11B96F),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          elevation: 2,
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                languageProvider.getText('Add Bank Account', 'إضافة حساب بنكي'),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  void _addBankAccount() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));
    
    setState(() => _isLoading = false);
    
    if (mounted) {
      final languageProvider = context.read<LanguageProvider>();
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            languageProvider.getText(
              'Bank account added successfully!',
              'تم إضافة الحساب البنكي بنجاح!'
            )
          ),
          backgroundColor: const Color(0xFF11B96F),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
      
      // Navigate back
      Navigator.of(context).pop(true);
    }
  }
}
