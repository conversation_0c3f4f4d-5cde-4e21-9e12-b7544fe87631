import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../screens/restaurant_screen.dart';

class CustomerInfoWidget extends StatefulWidget {
  final CustomerInfo? customerInfo;
  final Function(CustomerInfo) onCustomerInfoChanged;

  const CustomerInfoWidget({
    super.key,
    required this.customerInfo,
    required this.onCustomerInfoChanged,
  });

  @override
  State<CustomerInfoWidget> createState() => _CustomerInfoWidgetState();
}

class _CustomerInfoWidgetState extends State<CustomerInfoWidget> {
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  String _selectedOrderType = 'dine-in';

  final List<String> orderTypes = ['dine-in', 'takeaway', 'delivery'];

  @override
  void initState() {
    super.initState();
    if (widget.customerInfo != null) {
      _nameController.text = widget.customerInfo!.name;
      _phoneController.text = widget.customerInfo!.phone;
      _emailController.text = widget.customerInfo!.email ?? '';
      _addressController.text = widget.customerInfo!.address ?? '';
      _selectedOrderType = widget.customerInfo!.orderType;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.person,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Customer Information',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Order Type Selection
          Text(
            'Order Type',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: orderTypes.map((type) {
              final isSelected = _selectedOrderType == type;
              return Expanded(
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedOrderType = type;
                      });
                      _updateCustomerInfo();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primary : AppColors.greyLight,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected ? AppColors.primary : AppColors.inputBorder,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _getOrderTypeIcon(type),
                            color: isSelected ? AppColors.white : AppColors.textSecondary,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getOrderTypeLabel(type),
                            style: GoogleFonts.inter(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: isSelected ? AppColors.white : AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Customer Name
          _buildTextField(
            controller: _nameController,
            label: 'Customer Name',
            hint: 'Enter customer name',
            icon: Icons.person_outline,
            required: true,
          ),
          
          const SizedBox(height: 12),
          
          // Phone Number
          _buildTextField(
            controller: _phoneController,
            label: 'Phone Number',
            hint: 'Enter phone number',
            icon: Icons.phone_outlined,
            required: true,
            keyboardType: TextInputType.phone,
          ),
          
          const SizedBox(height: 12),
          
          // Email (Optional)
          _buildTextField(
            controller: _emailController,
            label: 'Email (Optional)',
            hint: 'Enter email address',
            icon: Icons.email_outlined,
            keyboardType: TextInputType.emailAddress,
          ),
          
          // Address (for delivery)
          if (_selectedOrderType == 'delivery') ...[
            const SizedBox(height: 12),
            _buildTextField(
              controller: _addressController,
              label: 'Delivery Address',
              hint: 'Enter delivery address',
              icon: Icons.location_on_outlined,
              required: true,
              maxLines: 2,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool required = false,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            if (required)
              Text(
                ' *',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: AppColors.error,
                ),
              ),
          ],
        ),
        const SizedBox(height: 4),
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          onChanged: (_) => _updateCustomerInfo(),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.inter(
              fontSize: 14,
              color: AppColors.textHint,
            ),
            prefixIcon: Icon(
              icon,
              color: AppColors.textSecondary,
              size: 18,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.inputBorder),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
          ),
          style: GoogleFonts.inter(
            fontSize: 14,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  IconData _getOrderTypeIcon(String type) {
    switch (type) {
      case 'dine-in':
        return Icons.restaurant;
      case 'takeaway':
        return Icons.shopping_bag;
      case 'delivery':
        return Icons.delivery_dining;
      default:
        return Icons.restaurant;
    }
  }

  String _getOrderTypeLabel(String type) {
    switch (type) {
      case 'dine-in':
        return 'Dine In';
      case 'takeaway':
        return 'Takeaway';
      case 'delivery':
        return 'Delivery';
      default:
        return type;
    }
  }

  void _updateCustomerInfo() {
    if (_nameController.text.isNotEmpty && _phoneController.text.isNotEmpty) {
      final customerInfo = CustomerInfo(
        name: _nameController.text,
        phone: _phoneController.text,
        email: _emailController.text.isNotEmpty ? _emailController.text : null,
        address: _addressController.text.isNotEmpty ? _addressController.text : null,
        orderType: _selectedOrderType,
      );
      widget.onCustomerInfoChanged(customerInfo);
    }
  }
}
