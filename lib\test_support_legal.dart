import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'constants/driver_themes.dart';
import 'providers/auth_provider.dart';
import 'providers/language_provider.dart';
import 'screens/support/support_screen.dart';
import 'screens/legal/legal_screen.dart';
import 'screens/legal/terms_of_service_screen.dart';
import 'screens/legal/privacy_policy_screen.dart';

/// Test Support and Legal screens
void main() {
  runApp(const TestSupportLegalApp());
}

class TestSupportLegalApp extends StatelessWidget {
  const TestSupportLegalApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
      ],
      child: MaterialApp(
        title: 'Test Support & Legal',
        debugShowCheckedModeBanner: false,
        theme: DriverThemes.lightTheme,
        darkTheme: DriverThemes.darkTheme,
        themeMode: ThemeMode.system,
        home: const TestSupportLegalHome(),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon, Color color, bool isDark) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildScreenButton({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}

class TestSupportLegalHome extends StatefulWidget {
  const TestSupportLegalHome({super.key});

  @override
  State<TestSupportLegalHome> createState() => _TestSupportLegalHomeState();
}

class _TestSupportLegalHomeState extends State<TestSupportLegalHome> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LanguageProvider>().initializeLanguage();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: Text(
          languageProvider.getText('Support & Legal Test', 'اختبار الدعم والقانونية'),
        ),
        backgroundColor: const Color(0xFF11B96F),
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: () {
              final newLang = languageProvider.isEnglish ? 'العربية' : 'English';
              languageProvider.changeLanguage(newLang);
            },
            child: Text(
              languageProvider.isEnglish ? 'العربية' : 'English',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.support_agent,
                        size: 40,
                        color: Color(0xFF11B96F),
                      ),
                      const SizedBox(width: 16),
                      const Icon(
                        Icons.gavel,
                        size: 40,
                        color: Colors.blue,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    languageProvider.getText(
                      'Support & Legal Screens',
                      'شاشات الدعم والقانونية'
                    ),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    languageProvider.getText(
                      'Test all support and legal functionality',
                      'اختبر جميع وظائف الدعم والقانونية'
                    ),
                    style: TextStyle(
                      fontSize: 16,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Support Section
            _buildSectionTitle(
              languageProvider.getText('Support Screens', 'شاشات الدعم'),
              Icons.support_agent,
              const Color(0xFF11B96F),
              isDark,
            ),

            const SizedBox(height: 16),

            _buildScreenButton(
              title: languageProvider.getText('Support & Help', 'الدعم والمساعدة'),
              description: languageProvider.getText(
                'Contact support, FAQ, live chat, and help center',
                'التواصل مع الدعم، الأسئلة الشائعة، المحادثة المباشرة، ومركز المساعدة'
              ),
              icon: Icons.support_agent,
              color: const Color(0xFF11B96F),
              onTap: () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SupportScreen(),
                ),
              ),
              isDark: isDark,
            ),

            const SizedBox(height: 30),

            // Legal Section
            _buildSectionTitle(
              languageProvider.getText('Legal Screens', 'الشاشات القانونية'),
              Icons.gavel,
              Colors.blue,
              isDark,
            ),

            const SizedBox(height: 16),

            _buildScreenButton(
              title: languageProvider.getText('Legal Information', 'المعلومات القانونية'),
              description: languageProvider.getText(
                'Legal documents, terms, privacy policy, and agreements',
                'الوثائق القانونية، الشروط، سياسة الخصوصية، والاتفاقيات'
              ),
              icon: Icons.gavel,
              color: Colors.blue,
              onTap: () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const LegalScreen(),
                ),
              ),
              isDark: isDark,
            ),

            const SizedBox(height: 16),

            _buildScreenButton(
              title: languageProvider.getText('Terms of Service', 'شروط الخدمة'),
              description: languageProvider.getText(
                'Complete terms and conditions for using Wasslti Partner',
                'الشروط والأحكام الكاملة لاستخدام شريك وصلتي'
              ),
              icon: Icons.description,
              color: Colors.orange,
              onTap: () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const TermsOfServiceScreen(),
                ),
              ),
              isDark: isDark,
            ),

            const SizedBox(height: 16),

            _buildScreenButton(
              title: languageProvider.getText('Privacy Policy', 'سياسة الخصوصية'),
              description: languageProvider.getText(
                'How we collect, use, and protect your personal data',
                'كيف نجمع ونستخدم ونحمي بياناتك الشخصية'
              ),
              icon: Icons.privacy_tip,
              color: Colors.purple,
              onTap: () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PrivacyPolicyScreen(),
                ),
              ),
              isDark: isDark,
            ),

            const SizedBox(height: 30),

            // Features List
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDark ? const Color(0xFF1B3B2E) : Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Text(
                    languageProvider.getText('Features', 'الميزات'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    languageProvider.getText(
                      '✅ Support center with multiple contact options\n✅ FAQ section with common questions\n✅ Contact form for detailed inquiries\n✅ Complete legal documentation\n✅ Terms of service and privacy policy\n✅ Bilingual support (Arabic/English)\n✅ Dark/Light theme compatibility',
                      '✅ مركز دعم مع خيارات اتصال متعددة\n✅ قسم الأسئلة الشائعة\n✅ نموذج اتصال للاستفسارات المفصلة\n✅ وثائق قانونية كاملة\n✅ شروط الخدمة وسياسة الخصوصية\n✅ دعم ثنائي اللغة (عربي/إنجليزي)\n✅ توافق مع الوضع المظلم والفاتح'
                    ),
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon, Color color, bool isDark) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildScreenButton({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
