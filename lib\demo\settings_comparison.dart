import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/driver_themes.dart';
import '../providers/auth_provider.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/settings/settings_screen_en.dart';

/// Settings screens comparison demo (Arabic vs English)
void main() {
  runApp(const SettingsComparisonApp());
}

class SettingsComparisonApp extends StatelessWidget {
  const SettingsComparisonApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'Settings Comparison Demo',
        debugShowCheckedModeBanner: false,
        theme: DriverThemes.lightTheme,
        darkTheme: DriverThemes.darkTheme,
        themeMode: ThemeMode.system,
        home: const SettingsComparisonScreen(),
      ),
    );
  }
}

class SettingsComparisonScreen extends StatefulWidget {
  const SettingsComparisonScreen({super.key});

  @override
  State<SettingsComparisonScreen> createState() => _SettingsComparisonScreenState();
}

class _SettingsComparisonScreenState extends State<SettingsComparisonScreen> {
  ThemeMode _themeMode = ThemeMode.system;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Settings Comparison',
      debugShowCheckedModeBanner: false,
      theme: DriverThemes.lightTheme,
      darkTheme: DriverThemes.darkTheme,
      themeMode: _themeMode,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Settings Comparison Demo'),
          backgroundColor: const Color(0xFF11B96F),
          foregroundColor: Colors.white,
          actions: [
            PopupMenuButton<ThemeMode>(
              icon: Icon(_getThemeIcon()),
              onSelected: (ThemeMode mode) {
                setState(() {
                  _themeMode = mode;
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: ThemeMode.light,
                  child: Row(
                    children: [
                      Icon(Icons.light_mode),
                      SizedBox(width: 8),
                      Text('Light Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: ThemeMode.dark,
                  child: Row(
                    children: [
                      Icon(Icons.dark_mode),
                      SizedBox(width: 8),
                      Text('Dark Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: ThemeMode.system,
                  child: Row(
                    children: [
                      Icon(Icons.settings),
                      SizedBox(width: 8),
                      Text('System'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
          ],
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.compare,
                  size: 80,
                  color: Color(0xFF11B96F),
                ),
                const SizedBox(height: 20),
                const Text(
                  'Settings Screen Comparison',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                const Text(
                  'Compare Arabic and English versions',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                
                // Arabic Version Button
                SizedBox(
                  width: double.infinity,
                  height: 60,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const SettingsScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF11B96F),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    icon: const Icon(Icons.language, size: 24),
                    label: const Text(
                      'Arabic Settings (الإعدادات)',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // English Version Button
                SizedBox(
                  width: double.infinity,
                  height: 60,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const SettingsScreenEN(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    icon: const Icon(Icons.language, size: 24),
                    label: const Text(
                      'English Settings',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 40),
                
                // Features comparison
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? const Color(0xFF1B3B2E)
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Features Comparison:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildFeatureItem('✅ Same UI design and layout'),
                      _buildFeatureItem('✅ Both support Light/Dark modes'),
                      _buildFeatureItem('✅ RTL support for Arabic'),
                      _buildFeatureItem('✅ LTR layout for English'),
                      _buildFeatureItem('✅ All settings sections included'),
                      _buildFeatureItem('✅ Identical functionality'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[300]
              : Colors.grey[700],
        ),
      ),
    );
  }

  IconData _getThemeIcon() {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.settings;
    }
  }
}

/// Function to run the settings comparison demo
void runSettingsComparison() {
  runApp(const SettingsComparisonApp());
}

/// Usage example:
/// 
/// ```dart
/// // In main.dart or any other file
/// import 'demo/settings_comparison.dart';
/// 
/// void main() {
///   runSettingsComparison();
/// }
/// ```
