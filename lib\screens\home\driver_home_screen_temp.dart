import 'package:flutter/material.dart';
import 'dart:async';
import '../../models/driver_status.dart';
import '../../models/delivery_order.dart';
import '../../models/order_status.dart';
import '../../widgets/simple_top_bar.dart';
import '../../widgets/incoming_order_popup.dart';
import '../../widgets/draggable_order_sheet.dart';
import '../../utils/settings_helper.dart';

/// Driver home screen (temporary without map)
/// Shows a map-like interface until Google Maps API Key is configured
class DriverHomeScreenTemp extends StatefulWidget {
  final String driverName;
  
  const DriverHomeScreenTemp({
    super.key,
    this.driverName = 'Yassine',
  });

  @override
  State<DriverHomeScreenTemp> createState() => _DriverHomeScreenTempState();
}

class _DriverHomeScreenTempState extends State<DriverHomeScreenTemp>
    with TickerProviderStateMixin {

  // Driver status
  DriverStatus _driverStatus = DriverStatus.working;

  // Current delivery order
  DeliveryOrder? _currentOrder;

  // Incoming order state
  bool _hasIncomingOrder = false;
  
  // متحكم الرسوم المتحركة لنافذة الطلب
  late AnimationController _orderPopupController;
  late Animation<Offset> _orderPopupAnimation;



  @override
  void initState() {
    super.initState();
    
    // إعداد رسوم نافذة الطلب المتحركة
    _orderPopupController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _orderPopupAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _orderPopupController,
      curve: Curves.easeOut,
    ));

    // محاكاة طلب وارد بعد 3 ثوان
    Timer(const Duration(seconds: 3), () {
      _showIncomingOrder();
    });
  }

  @override
  void dispose() {
    _orderPopupController.dispose();
    super.dispose();
  }

  void _showIncomingOrder() {
    setState(() {
      _hasIncomingOrder = true;
    });
    _orderPopupController.forward();
  }

  void _hideIncomingOrder() {
    _orderPopupController.reverse().then((_) {
      setState(() {
        _hasIncomingOrder = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      body: Stack(
        children: [
          // خلفية مؤقتة تحاكي الخريطة
          _buildMapPlaceholder(isDark),
          
          // الشريط العلوي العائم
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            right: 16,
            child: FloatingTopBar(
              driverStatus: _driverStatus,
              onStatusTap: _handleStatusTap,
              onSettingsTap: _handleSettingsTap,
              onSupportTap: _handleSupportTap,
            ),
          ),
          
          // نافذة الطلب الوارد
          if (_hasIncomingOrder)
            Positioned(
              bottom: 100,
              left: 16,
              right: 16,
              child: SlideTransition(
                position: _orderPopupAnimation,
                child: IncomingOrderPopup(
                  onAccept: _handleAcceptOrder,
                  onDecline: _handleDeclineOrder,
                ),
              ),
            ),

          // Draggable order sheet
          if (_currentOrder != null)
            DraggableOrderSheet(
              order: _currentOrder!,
              onActionPressed: _handleOrderAction,
              onCancelPressed: _handleCancelOrder,
            ),

          // Demo button for testing
          if (_currentOrder == null && !_hasIncomingOrder)
            Positioned(
              bottom: 100,
              right: 16,
              child: FloatingActionButton.extended(
                onPressed: _simulateIncomingOrder,
                backgroundColor: const Color(0xFF11B96F),
                icon: const Icon(Icons.add, color: Colors.white),
                label: const Text(
                  'Simulate Order',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),

        ],
      ),
    );
  }

  Widget _buildMapPlaceholder(bool isDark) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDark 
            ? [
                const Color(0xFF0F231A),
                const Color(0xFF1B3B2E),
              ]
            : [
                const Color(0xFFE8F5E8),
                const Color(0xFFF0F8F0),
              ],
        ),
      ),
      child: Stack(
        children: [
          // شبكة تحاكي الخريطة
          _buildGridPattern(isDark),
          
          // موقع السائق
          Center(
            child: _buildDriverLocationPin(isDark),
          ),
          

        ],
      ),
    );
  }

  Widget _buildGridPattern(bool isDark) {
    return CustomPaint(
      size: Size.infinite,
      painter: GridPainter(
        color: isDark 
          ? Colors.white.withValues(alpha: 0.1)
          : Colors.black.withValues(alpha: 0.1),
      ),
    );
  }

  Widget _buildDriverLocationPin(bool isDark) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: const Color(0xFF11B96F),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF11B96F).withValues(alpha: 0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: const Icon(
        Icons.my_location,
        color: Colors.white,
        size: 30,
      ),
    );
  }

  // Event handlers
  void _handleStatusTap() {
    setState(() {
      _driverStatus = _driverStatus.nextStatus;
    });
    _showSnackBar('Status changed to: ${_driverStatus.displayText}');
  }

  void _handleSettingsTap() {
    SettingsHelper.navigateToSettingsWithProvider(context);
  }
  void _handleSupportTap() => _showSnackBar('Open Support');

  void _simulateIncomingOrder() {
    _showIncomingOrder();
  }

  void _handleAcceptOrder() {
    _hideIncomingOrder();
    setState(() {
      _currentOrder = DeliveryOrder.createSample();
      _currentOrder!.updateStatus(OrderStatus.accepted);
    });

    _showSnackBar('Order accepted - Navigate to restaurant');
  }

  void _handleDeclineOrder() {
    _hideIncomingOrder();
    _showSnackBar('Order rejected');
  }

  void _handleOrderAction() {
    if (_currentOrder == null) return;

    final nextStatus = _currentOrder!.status.nextStatus;
    if (nextStatus != null) {
      setState(() {
        _currentOrder!.updateStatus(nextStatus);
      });
      _showSnackBar('Status updated: ${nextStatus.displayText}');

      // إذا تم التوصيل، إخفاء الواجهة بعد 3 ثوان
      if (nextStatus == OrderStatus.delivered) {
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _currentOrder = null;
            });
          }
        });
      }
    }
  }

  void _handleCancelOrder() {
    setState(() {
      _currentOrder!.updateStatus(OrderStatus.cancelled);
    });
    _showSnackBar('Order cancelled');

    // Hide order after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _currentOrder = null;
        });
      }
    });
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF11B96F),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}

// رسام الشبكة لمحاكاة الخريطة
class GridPainter extends CustomPainter {
  final Color color;
  
  GridPainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1;
    
    const spacing = 50.0;
    
    // خطوط عمودية
    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
    
    // خطوط أفقية
    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
