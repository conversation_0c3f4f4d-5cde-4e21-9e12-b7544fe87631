import React, { useState, useEffect } from 'react';
import { PlayCircle, Star, MapPin, Clock, Search, ArrowRight, Zap, Shield, Award } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { useModal } from '../hooks/useModal';
import AuthModal from './AuthModal';

const Hero = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentSlide, setCurrentSlide] = useState(0);
  const { t, isRTL } = useLanguage();
  const signupModal = useModal();

  const heroSlides = [
    {
      image: "https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop",
      titleKey: "hero.title_1",
      subtitleKey: "hero.title_2",
      descriptionKey: "hero.description_1"
    },
    {
      image: "https://images.pexels.com/photos/1435904/pexels-photo-1435904.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop",
      titleKey: "hero.subtitle_1",
      subtitleKey: "hero.subtitle_2",
      descriptionKey: "hero.description_2"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const currentHero = heroSlides[currentSlide];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Handle search logic
      console.log('Searching for:', searchQuery);
      alert(`البحث عن: ${searchQuery}`);
    }
  };

  const handleOrderNow = () => {
    // Scroll to restaurants section
    const restaurantsSection = document.getElementById('restaurants');
    if (restaurantsSection) {
      restaurantsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleWatchVideo = () => {
    // Open video modal or redirect to video
    alert('سيتم فتح فيديو توضيحي قريباً...');
  };
  return (
    <>
      <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden pt-32">
      {/* Background Slideshow */}
      <div className="absolute inset-0 z-0">
        {heroSlides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img 
              src={slide.image}
              alt="Delicious Food"
              className="w-full h-full object-cover scale-105"
            />
          </div>
        ))}
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/30"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-primary/20 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-32 right-16 w-32 h-32 bg-orange-400/20 rounded-full blur-2xl animate-pulse delay-1000"></div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 text-center text-white">
        <div className="max-w-5xl mx-auto">
          {/* Main Heading */}
          <div className="mb-8">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight rtl-font">
              {t(currentHero.titleKey)}
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-orange-400">
                {t(currentHero.subtitleKey)}
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto leading-relaxed rtl-font">
              {t(currentHero.descriptionKey)}
            </p>
          </div>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <form onSubmit={handleSearch} className="relative">
              <div className="absolute inset-y-0 right-4 flex items-center">
                <Search className="w-6 h-6 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder={t('hero.search_placeholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full py-4 px-6 pr-14 rounded-2xl bg-white/95 backdrop-blur-sm text-gray-800 placeholder-gray-500 text-lg focus:outline-none focus:ring-4 focus:ring-primary/30 shadow-2xl rtl-font"
              />
              <button 
                type="submit"
                className="absolute left-2 top-2 bottom-2 bg-primary hover:bg-primary/90 text-white px-8 rounded-xl font-semibold transition-all duration-300 hover:scale-105"
              >
                {t('hero.search_button')}
              </button>
            </form>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-10">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <div className="flex items-center justify-center mb-2">
                <Star className="w-6 h-6 text-yellow-400 fill-current" />
              </div>
              <div className="text-2xl font-bold text-primary">4.9</div>
              <div className="text-sm text-gray-300">{t('hero.app_rating')}</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <div className="flex items-center justify-center mb-2">
                <Clock className="w-6 h-6 text-primary" />
              </div>
              <div className="text-2xl font-bold text-primary">15</div>
              <div className="text-sm text-gray-300">15 {t('hero.delivery_time')}</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <div className="flex items-center justify-center mb-2">
                <MapPin className="w-6 h-6 text-primary" />
              </div>
              <div className="text-2xl font-bold text-primary">500+</div>
              <div className="text-sm text-gray-300">{t('hero.restaurants_count')}</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <div className="flex items-center justify-center mb-2">
                <Award className="w-6 h-6 text-primary" />
              </div>
              <div className="text-2xl font-bold text-primary">1M+</div>
              <div className="text-sm text-gray-300">{t('hero.happy_customers')}</div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <button 
              onClick={handleOrderNow}
              className="group bg-gradient-to-r from-primary to-primary/90 text-white px-10 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2 rtl:space-x-reverse"
            >
              <span>{t('hero.order_now')}</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform" />
            </button>
            <button 
              onClick={signupModal.openModal}
              className="group bg-white/15 backdrop-blur-sm text-white border-2 border-white/30 px-10 py-4 rounded-2xl font-bold text-lg hover:bg-white/25 transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2 rtl:space-x-reverse"
            >
              <span>{t('hero.become_partner')}</span>
              <Shield className="w-5 h-5 group-hover:rotate-12 transition-transform" />
            </button>
          </div>

          {/* Features */}
          <div className="flex flex-wrap justify-center gap-6 mb-8">
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-white/90">
              <Zap className="w-5 h-5 text-yellow-400" />
              <span className="text-sm font-medium rtl-font">{t('hero.instant_delivery')}</span>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-white/90">
              <Shield className="w-5 h-5 text-green-400" />
              <span className="text-sm font-medium rtl-font">{t('hero.secure_payment')}</span>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-white/90">
              <Award className="w-5 h-5 text-blue-400" />
              <span className="text-sm font-medium rtl-font">{t('hero.guaranteed_quality')}</span>
            </div>
          </div>

          {/* Video Play Button */}
          <button 
            onClick={handleWatchVideo}
            className="group flex items-center space-x-3 rtl:space-x-reverse mx-auto text-white/80 hover:text-white transition-all duration-300"
          >
            <div className="relative">
              <PlayCircle className="w-16 h-16 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-white/20 rounded-full animate-ping"></div>
            </div>
            <span className="text-lg font-medium rtl-font">{t('hero.watch_video')}</span>
          </button>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {heroSlides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide ? 'bg-primary scale-125' : 'bg-white/50'
            }`}
          />
        ))}
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 right-8 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
      </section>
      
      {/* Auth Modal */}
      <AuthModal 
        isOpen={signupModal.isOpen} 
        onClose={signupModal.closeModal} 
        initialMode="signup" 
      />
    </>
  );
};

export default Hero;