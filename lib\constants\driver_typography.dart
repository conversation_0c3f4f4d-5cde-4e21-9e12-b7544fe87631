import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// نظام الخطوط المخصص لتطبيق السائق
/// محسن للقراءة أثناء القيادة مع دعم العربية والإنجليزية
class DriverTypography {
  
  // ========== الخطوط الأساسية ==========
  
  /// الخط الأساسي للإنجليزية - Roboto (واضح ومقروء)
  static const String primaryFontFamily = 'Roboto';
  
  /// الخط الأساسي للعربية - Cairo (واضح ومقروء)
  static const String arabicFontFamily = 'Cairo';
  
  /// خط الأرقام والبيانات - Roboto Mono (للأرقام والمعرفات)
  static const String monoFontFamily = 'RobotoMono';

  // ========== أحجام الخطوط ==========
  
  /// أحجام محسنة للاستخدام أثناء القيادة (أكبر من المعتاد)
  static const double displayLarge = 40.0;      // العناوين الكبيرة
  static const double displayMedium = 32.0;     // العناوين المتوسطة
  static const double displaySmall = 28.0;      // العناوين الصغيرة
  
  static const double headlineLarge = 24.0;     // عناوين الصفحات
  static const double headlineMedium = 22.0;    // عناوين الأقسام
  static const double headlineSmall = 20.0;     // عناوين فرعية
  
  static const double titleLarge = 18.0;        // عناوين الكروت
  static const double titleMedium = 16.0;       // عناوين العناصر
  static const double titleSmall = 14.0;        // عناوين صغيرة
  
  static const double bodyLarge = 16.0;         // النص الأساسي
  static const double bodyMedium = 14.0;        // النص العادي
  static const double bodySmall = 12.0;         // النص الصغير
  
  static const double labelLarge = 14.0;        // تسميات الأزرار
  static const double labelMedium = 12.0;       // تسميات عادية
  static const double labelSmall = 10.0;        // تسميات صغيرة

  // ========== أوزان الخطوط ==========
  
  static const FontWeight thin = FontWeight.w100;
  static const FontWeight extraLight = FontWeight.w200;
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  static const FontWeight black = FontWeight.w900;

  // ========== نظام الخطوط للوضع الفاتح ==========
  
  static TextTheme get lightTextTheme => TextTheme(
    // العناوين الكبيرة
    displayLarge: GoogleFonts.roboto(
      fontSize: displayLarge,
      fontWeight: bold,
      color: const Color(0xFF1C1C1C),
      height: 1.2,
      letterSpacing: -0.5,
    ),
    displayMedium: GoogleFonts.roboto(
      fontSize: displayMedium,
      fontWeight: bold,
      color: const Color(0xFF1C1C1C),
      height: 1.2,
      letterSpacing: -0.25,
    ),
    displaySmall: GoogleFonts.roboto(
      fontSize: displaySmall,
      fontWeight: semiBold,
      color: const Color(0xFF1C1C1C),
      height: 1.3,
    ),
    
    // عناوين الصفحات
    headlineLarge: GoogleFonts.roboto(
      fontSize: headlineLarge,
      fontWeight: bold,
      color: const Color(0xFF1C1C1C),
      height: 1.3,
    ),
    headlineMedium: GoogleFonts.roboto(
      fontSize: headlineMedium,
      fontWeight: semiBold,
      color: const Color(0xFF1C1C1C),
      height: 1.3,
    ),
    headlineSmall: GoogleFonts.roboto(
      fontSize: headlineSmall,
      fontWeight: semiBold,
      color: const Color(0xFF1C1C1C),
      height: 1.4,
    ),
    
    // عناوين الكروت والعناصر
    titleLarge: GoogleFonts.roboto(
      fontSize: titleLarge,
      fontWeight: semiBold,
      color: const Color(0xFF1C1C1C),
      height: 1.4,
    ),
    titleMedium: GoogleFonts.roboto(
      fontSize: titleMedium,
      fontWeight: medium,
      color: const Color(0xFF1C1C1C),
      height: 1.4,
    ),
    titleSmall: GoogleFonts.roboto(
      fontSize: titleSmall,
      fontWeight: medium,
      color: const Color(0xFF424242),
      height: 1.4,
    ),
    
    // النصوص الأساسية
    bodyLarge: GoogleFonts.roboto(
      fontSize: bodyLarge,
      fontWeight: regular,
      color: const Color(0xFF1C1C1C),
      height: 1.5,
    ),
    bodyMedium: GoogleFonts.roboto(
      fontSize: bodyMedium,
      fontWeight: regular,
      color: const Color(0xFF424242),
      height: 1.5,
    ),
    bodySmall: GoogleFonts.roboto(
      fontSize: bodySmall,
      fontWeight: regular,
      color: const Color(0xFF757575),
      height: 1.5,
    ),
    
    // التسميات
    labelLarge: GoogleFonts.roboto(
      fontSize: labelLarge,
      fontWeight: semiBold,
      color: const Color(0xFF1C1C1C),
      height: 1.4,
      letterSpacing: 0.1,
    ),
    labelMedium: GoogleFonts.roboto(
      fontSize: labelMedium,
      fontWeight: medium,
      color: const Color(0xFF424242),
      height: 1.4,
      letterSpacing: 0.5,
    ),
    labelSmall: GoogleFonts.roboto(
      fontSize: labelSmall,
      fontWeight: medium,
      color: const Color(0xFF757575),
      height: 1.4,
      letterSpacing: 0.5,
    ),
  );

  // ========== نظام الخطوط للوضع المظلم ==========
  
  static TextTheme get darkTextTheme => TextTheme(
    // العناوين الكبيرة
    displayLarge: GoogleFonts.roboto(
      fontSize: displayLarge,
      fontWeight: bold,
      color: const Color(0xFFE0E0E0),
      height: 1.2,
      letterSpacing: -0.5,
    ),
    displayMedium: GoogleFonts.roboto(
      fontSize: displayMedium,
      fontWeight: bold,
      color: const Color(0xFFE0E0E0),
      height: 1.2,
      letterSpacing: -0.25,
    ),
    displaySmall: GoogleFonts.roboto(
      fontSize: displaySmall,
      fontWeight: semiBold,
      color: const Color(0xFFE0E0E0),
      height: 1.3,
    ),
    
    // عناوين الصفحات
    headlineLarge: GoogleFonts.roboto(
      fontSize: headlineLarge,
      fontWeight: bold,
      color: const Color(0xFFE0E0E0),
      height: 1.3,
    ),
    headlineMedium: GoogleFonts.roboto(
      fontSize: headlineMedium,
      fontWeight: semiBold,
      color: const Color(0xFFE0E0E0),
      height: 1.3,
    ),
    headlineSmall: GoogleFonts.roboto(
      fontSize: headlineSmall,
      fontWeight: semiBold,
      color: const Color(0xFFE0E0E0),
      height: 1.4,
    ),
    
    // عناوين الكروت والعناصر
    titleLarge: GoogleFonts.roboto(
      fontSize: titleLarge,
      fontWeight: semiBold,
      color: const Color(0xFFE0E0E0),
      height: 1.4,
    ),
    titleMedium: GoogleFonts.roboto(
      fontSize: titleMedium,
      fontWeight: medium,
      color: const Color(0xFFE0E0E0),
      height: 1.4,
    ),
    titleSmall: GoogleFonts.roboto(
      fontSize: titleSmall,
      fontWeight: medium,
      color: const Color(0xFFBDBDBD),
      height: 1.4,
    ),
    
    // النصوص الأساسية
    bodyLarge: GoogleFonts.roboto(
      fontSize: bodyLarge,
      fontWeight: regular,
      color: const Color(0xFFE0E0E0),
      height: 1.5,
    ),
    bodyMedium: GoogleFonts.roboto(
      fontSize: bodyMedium,
      fontWeight: regular,
      color: const Color(0xFFBDBDBD),
      height: 1.5,
    ),
    bodySmall: GoogleFonts.roboto(
      fontSize: bodySmall,
      fontWeight: regular,
      color: const Color(0xFF9E9E9E),
      height: 1.5,
    ),
    
    // التسميات
    labelLarge: GoogleFonts.roboto(
      fontSize: labelLarge,
      fontWeight: semiBold,
      color: const Color(0xFFE0E0E0),
      height: 1.4,
      letterSpacing: 0.1,
    ),
    labelMedium: GoogleFonts.roboto(
      fontSize: labelMedium,
      fontWeight: medium,
      color: const Color(0xFFBDBDBD),
      height: 1.4,
      letterSpacing: 0.5,
    ),
    labelSmall: GoogleFonts.roboto(
      fontSize: labelSmall,
      fontWeight: medium,
      color: const Color(0xFF9E9E9E),
      height: 1.4,
      letterSpacing: 0.5,
    ),
  );

  // ========== أنماط مخصصة للسائق ==========
  
  /// نص كبير للأرقام المهمة (الأرباح، المسافة، الوقت)
  static TextStyle get bigNumberStyle => GoogleFonts.robotoMono(
    fontSize: 32,
    fontWeight: bold,
    letterSpacing: -0.5,
  );
  
  /// نص متوسط للمعرفات (رقم الطلب، رقم الهاتف)
  static TextStyle get identifierStyle => GoogleFonts.robotoMono(
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 0.5,
  );
  
  /// نص للحالة (متصل، غير متصل، في الطريق)
  static TextStyle get statusStyle => GoogleFonts.roboto(
    fontSize: 12,
    fontWeight: semiBold,
    letterSpacing: 0.5,
  );
  
  /// نص للأزرار الكبيرة (قبول، رفض، تأكيد)
  static TextStyle get bigButtonStyle => GoogleFonts.roboto(
    fontSize: 18,
    fontWeight: bold,
    letterSpacing: 0.25,
  );
  
  /// نص للعناوين العربية
  static TextStyle get arabicTitleStyle => GoogleFonts.cairo(
    fontSize: 20,
    fontWeight: bold,
    height: 1.6,
  );
  
  /// نص للمحتوى العربي
  static TextStyle get arabicBodyStyle => GoogleFonts.cairo(
    fontSize: 16,
    fontWeight: regular,
    height: 1.8,
  );

  // ========== دوال مساعدة ==========
  
  /// الحصول على نمط نص حسب السياق
  static TextStyle getContextualStyle(BuildContext context, {
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return GoogleFonts.roboto(
      fontSize: fontSize,
      fontWeight: fontWeight ?? regular,
      color: color ?? (isDark ? const Color(0xFFE0E0E0) : const Color(0xFF1C1C1C)),
      height: height ?? 1.4,
      letterSpacing: letterSpacing ?? 0,
    );
  }
  
  /// الحصول على نمط نص للأرقام
  static TextStyle getNumberStyle(BuildContext context, {
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return GoogleFonts.robotoMono(
      fontSize: fontSize,
      fontWeight: fontWeight ?? semiBold,
      color: color ?? (isDark ? const Color(0xFFE0E0E0) : const Color(0xFF1C1C1C)),
      letterSpacing: 0.5,
    );
  }
  
  /// الحصول على نمط نص عربي
  static TextStyle getArabicStyle(BuildContext context, {
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return GoogleFonts.cairo(
      fontSize: fontSize,
      fontWeight: fontWeight ?? regular,
      color: color ?? (isDark ? const Color(0xFFE0E0E0) : const Color(0xFF1C1C1C)),
      height: height ?? 1.6,
    );
  }
}
