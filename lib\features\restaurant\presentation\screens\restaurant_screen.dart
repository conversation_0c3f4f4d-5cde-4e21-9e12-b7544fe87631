import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/menu_categories_widget.dart';
import '../widgets/menu_items_grid.dart';
import '../widgets/order_summary_panel.dart';
import '../widgets/customer_info_widget.dart';

class RestaurantScreen extends StatefulWidget {
  const RestaurantScreen({super.key});

  @override
  State<RestaurantScreen> createState() => _RestaurantScreenState();
}

class _RestaurantScreenState extends State<RestaurantScreen> {
  String selectedCategory = 'All';
  String searchQuery = '';
  List<OrderItem> currentOrder = [];
  CustomerInfo? customerInfo;

  final List<String> categories = [
    'All',
    'Appetizers',
    'Main Course',
    'Desserts',
    'Beverages',
    'Salads',
    'Pizza',
    'Burgers'
  ];

  // Sample menu items - in real app, this would come from menu management
  final List<MenuItem> menuItems = [
    MenuItem(
      id: '1',
      name: 'Grilled Chicken Breast',
      category: 'Main Course',
      price: 18.99,
      image: 'assets/images/chicken.png',
      description: 'Tender grilled chicken breast with herbs and spices',
      isAvailable: true,
      preparationTime: 15,
    ),
    MenuItem(
      id: '2',
      name: 'Caesar Salad',
      category: 'Salads',
      price: 12.99,
      image: 'assets/images/caesar_salad.png',
      description: 'Fresh romaine lettuce with caesar dressing and croutons',
      isAvailable: true,
      preparationTime: 8,
    ),
    MenuItem(
      id: '3',
      name: 'Margherita Pizza',
      category: 'Pizza',
      price: 16.99,
      image: 'assets/images/pizza.png',
      description: 'Classic pizza with tomato sauce, mozzarella and basil',
      isAvailable: true,
      preparationTime: 20,
    ),
    MenuItem(
      id: '4',
      name: 'Fresh Orange Juice',
      category: 'Beverages',
      price: 4.99,
      image: 'assets/images/juice.png',
      description: 'Freshly squeezed orange juice',
      isAvailable: true,
      preparationTime: 3,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: Row(
                    children: [
                      // Left Panel - Menu
                      Expanded(
                        flex: 3,
                        child: Column(
                          children: [
                            // Categories
                            MenuCategoriesWidget(
                              categories: categories,
                              selectedCategory: selectedCategory,
                              onCategorySelected: (category) {
                                setState(() {
                                  selectedCategory = category;
                                });
                              },
                            ),
                            
                            // Menu Items Grid
                            Expanded(
                              child: MenuItemsGrid(
                                menuItems: _getFilteredMenuItems(),
                                onItemSelected: _addToOrder,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Right Panel - Order Summary
                      Container(
                        width: 400,
                        decoration: const BoxDecoration(
                          color: AppColors.white,
                          border: Border(
                            left: BorderSide(
                              color: AppColors.inputBorder,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Column(
                          children: [
                            // Customer Info
                            CustomerInfoWidget(
                              customerInfo: customerInfo,
                              onCustomerInfoChanged: (info) {
                                setState(() {
                                  customerInfo = info;
                                });
                              },
                            ),
                            
                            // Order Summary
                            Expanded(
                              child: OrderSummaryPanel(
                                orderItems: currentOrder,
                                customerInfo: customerInfo,
                                onQuantityChanged: _updateQuantity,
                                onItemRemoved: _removeFromOrder,
                                onOrderCompleted: _completeOrder,
                                onPrintReceipt: _printReceipt,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Icon(
                Icons.restaurant,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Restaurant POS',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search menu items...',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Order Stats
          if (currentOrder.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppColors.primary,
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.shopping_cart,
                    color: AppColors.primary,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${currentOrder.length} items - \$${_getTotalAmount().toStringAsFixed(2)}',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  List<MenuItem> _getFilteredMenuItems() {
    List<MenuItem> filtered = menuItems;

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((item) =>
          item.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().contains(searchQuery.toLowerCase())).toList();
    }

    // Apply category filter
    if (selectedCategory != 'All') {
      filtered = filtered.where((item) => item.category == selectedCategory).toList();
    }

    return filtered;
  }

  void _addToOrder(MenuItem menuItem) {
    setState(() {
      final existingIndex = currentOrder.indexWhere((item) => item.menuItem.id == menuItem.id);
      if (existingIndex >= 0) {
        currentOrder[existingIndex].quantity++;
      } else {
        currentOrder.add(OrderItem(menuItem: menuItem, quantity: 1));
      }
    });
  }

  void _updateQuantity(String itemId, int quantity) {
    setState(() {
      final index = currentOrder.indexWhere((item) => item.menuItem.id == itemId);
      if (index >= 0) {
        if (quantity <= 0) {
          currentOrder.removeAt(index);
        } else {
          currentOrder[index].quantity = quantity;
        }
      }
    });
  }

  void _removeFromOrder(String itemId) {
    setState(() {
      currentOrder.removeWhere((item) => item.menuItem.id == itemId);
    });
  }

  double _getTotalAmount() {
    return currentOrder.fold(0.0, (total, item) => total + (item.menuItem.price * item.quantity));
  }

  void _completeOrder() {
    if (currentOrder.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please add items to the order', style: GoogleFonts.inter()),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Process the order
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Order Completed',
          style: GoogleFonts.inter(fontWeight: FontWeight.w600),
        ),
        content: Text(
          'Order has been successfully processed!',
          style: GoogleFonts.inter(),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                currentOrder.clear();
                customerInfo = null;
              });
            },
            child: Text(
              'OK',
              style: GoogleFonts.inter(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _printReceipt() {
    if (currentOrder.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No items to print', style: GoogleFonts.inter()),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Navigate to print preview
    Navigator.pushNamed(context, AppRoutes.printReceipt, arguments: {
      'orderItems': currentOrder,
      'customerInfo': customerInfo,
      'total': _getTotalAmount(),
    });
  }
}

class MenuItem {
  final String id;
  final String name;
  final String category;
  final double price;
  final String image;
  final String description;
  final bool isAvailable;
  final int preparationTime;

  MenuItem({
    required this.id,
    required this.name,
    required this.category,
    required this.price,
    required this.image,
    required this.description,
    required this.isAvailable,
    required this.preparationTime,
  });
}

class OrderItem {
  final MenuItem menuItem;
  int quantity;

  OrderItem({
    required this.menuItem,
    required this.quantity,
  });
}

class CustomerInfo {
  final String name;
  final String phone;
  final String? email;
  final String? address;
  final String orderType; // 'dine-in', 'takeaway', 'delivery'

  CustomerInfo({
    required this.name,
    required this.phone,
    this.email,
    this.address,
    required this.orderType,
  });
}
