import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/driver.dart';

/// خدمة التخزين المحلي لحفظ بيانات المستخدم والجلسات
class StorageService {
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _driverKey = 'driver_data';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _phoneKey = 'user_phone';
  static const String _lastLoginKey = 'last_login';

  static SharedPreferences? _prefs;

  /// تهيئة خدمة التخزين
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// التأكد من تهيئة الخدمة
  static Future<SharedPreferences> get _preferences async {
    if (_prefs == null) {
      await init();
    }
    return _prefs!;
  }

  // ==================== إدارة التوكن ====================

  /// حفظ توكن المصادقة
  static Future<bool> saveAuthToken(String token) async {
    final prefs = await _preferences;
    return await prefs.setString(_tokenKey, token);
  }

  /// الحصول على توكن المصادقة
  static Future<String?> getAuthToken() async {
    final prefs = await _preferences;
    return prefs.getString(_tokenKey);
  }

  /// حفظ توكن التحديث
  static Future<bool> saveRefreshToken(String refreshToken) async {
    final prefs = await _preferences;
    return await prefs.setString(_refreshTokenKey, refreshToken);
  }

  /// الحصول على توكن التحديث
  static Future<String?> getRefreshToken() async {
    final prefs = await _preferences;
    return prefs.getString(_refreshTokenKey);
  }

  /// حذف جميع التوكنات
  static Future<bool> clearTokens() async {
    final prefs = await _preferences;
    final results = await Future.wait([
      prefs.remove(_tokenKey),
      prefs.remove(_refreshTokenKey),
    ]);
    return results.every((result) => result);
  }

  // ==================== إدارة بيانات السائق ====================

  /// حفظ بيانات السائق
  static Future<bool> saveDriver(Driver driver) async {
    final prefs = await _preferences;
    final driverJson = jsonEncode(driver.toJson());
    return await prefs.setString(_driverKey, driverJson);
  }

  /// الحصول على بيانات السائق
  static Future<Driver?> getDriver() async {
    final prefs = await _preferences;
    final driverJson = prefs.getString(_driverKey);
    
    if (driverJson == null) return null;
    
    try {
      final driverMap = jsonDecode(driverJson) as Map<String, dynamic>;
      return Driver.fromJson(driverMap);
    } catch (e) {
      // في حالة خطأ في البيانات، احذفها
      await clearDriver();
      return null;
    }
  }

  /// حذف بيانات السائق
  static Future<bool> clearDriver() async {
    final prefs = await _preferences;
    return await prefs.remove(_driverKey);
  }

  // ==================== إدارة حالة تسجيل الدخول ====================

  /// تعيين حالة تسجيل الدخول
  static Future<bool> setLoggedIn(bool isLoggedIn) async {
    final prefs = await _preferences;
    return await prefs.setBool(_isLoggedInKey, isLoggedIn);
  }

  /// التحقق من حالة تسجيل الدخول
  static Future<bool> isLoggedIn() async {
    final prefs = await _preferences;
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  /// حفظ رقم الهاتف
  static Future<bool> savePhone(String phone) async {
    final prefs = await _preferences;
    return await prefs.setString(_phoneKey, phone);
  }

  /// الحصول على رقم الهاتف المحفوظ
  static Future<String?> getPhone() async {
    final prefs = await _preferences;
    return prefs.getString(_phoneKey);
  }

  /// حفظ وقت آخر تسجيل دخول
  static Future<bool> saveLastLogin() async {
    final prefs = await _preferences;
    final now = DateTime.now().toIso8601String();
    return await prefs.setString(_lastLoginKey, now);
  }

  /// الحصول على وقت آخر تسجيل دخول
  static Future<DateTime?> getLastLogin() async {
    final prefs = await _preferences;
    final lastLoginStr = prefs.getString(_lastLoginKey);
    
    if (lastLoginStr == null) return null;
    
    try {
      return DateTime.parse(lastLoginStr);
    } catch (e) {
      return null;
    }
  }

  // ==================== إدارة الجلسة ====================

  /// حفظ جلسة كاملة (توكن + بيانات السائق + حالة تسجيل الدخول)
  static Future<bool> saveSession({
    required String authToken,
    String? refreshToken,
    required Driver driver,
    required String phone,
  }) async {
    final results = await Future.wait([
      saveAuthToken(authToken),
      if (refreshToken != null) saveRefreshToken(refreshToken),
      saveDriver(driver),
      savePhone(phone),
      setLoggedIn(true),
      saveLastLogin(),
    ]);
    
    return results.every((result) => result);
  }

  /// التحقق من صحة الجلسة
  static Future<bool> isValidSession() async {
    final token = await getAuthToken();
    final isLoggedIn = await StorageService.isLoggedIn();
    final driver = await getDriver();
    
    return token != null && isLoggedIn && driver != null;
  }

  /// مسح الجلسة بالكامل
  static Future<bool> clearSession() async {
    final results = await Future.wait([
      clearTokens(),
      clearDriver(),
      setLoggedIn(false),
      _preferences.then((prefs) => prefs.remove(_phoneKey)),
      _preferences.then((prefs) => prefs.remove(_lastLoginKey)),
    ]);
    
    return results.every((result) => result);
  }

  // ==================== إعدادات التطبيق ====================

  /// حفظ إعدادات التطبيق
  static Future<bool> saveSetting(String key, dynamic value) async {
    final prefs = await _preferences;
    
    if (value is String) {
      return await prefs.setString(key, value);
    } else if (value is bool) {
      return await prefs.setBool(key, value);
    } else if (value is int) {
      return await prefs.setInt(key, value);
    } else if (value is double) {
      return await prefs.setDouble(key, value);
    } else {
      // للكائنات المعقدة، احفظها كـ JSON
      return await prefs.setString(key, jsonEncode(value));
    }
  }

  /// الحصول على إعداد
  static Future<T?> getSetting<T>(String key) async {
    final prefs = await _preferences;
    
    if (T == String) {
      return prefs.getString(key) as T?;
    } else if (T == bool) {
      return prefs.getBool(key) as T?;
    } else if (T == int) {
      return prefs.getInt(key) as T?;
    } else if (T == double) {
      return prefs.getDouble(key) as T?;
    } else {
      // للكائنات المعقدة
      final jsonStr = prefs.getString(key);
      if (jsonStr == null) return null;
      
      try {
        return jsonDecode(jsonStr) as T;
      } catch (e) {
        return null;
      }
    }
  }

  /// حذف إعداد
  static Future<bool> removeSetting(String key) async {
    final prefs = await _preferences;
    return await prefs.remove(key);
  }

  // ==================== أدوات مساعدة ====================

  /// الحصول على جميع المفاتيح المحفوظة
  static Future<Set<String>> getAllKeys() async {
    final prefs = await _preferences;
    return prefs.getKeys();
  }

  /// مسح جميع البيانات (استخدم بحذر!)
  static Future<bool> clearAll() async {
    final prefs = await _preferences;
    return await prefs.clear();
  }

  /// طباعة معلومات الجلسة للتطوير
  static Future<void> debugPrintSession() async {
    if (kDebugMode) {
      final token = await getAuthToken();
      final isLoggedIn = await StorageService.isLoggedIn();
      final driver = await getDriver();
      final phone = await getPhone();
      final lastLogin = await getLastLogin();
      
      print('=== Session Debug Info ===');
      print('Token: ${token?.substring(0, 20)}...');
      print('Is Logged In: $isLoggedIn');
      print('Driver: ${driver?.name}');
      print('Phone: $phone');
      print('Last Login: $lastLogin');
      print('========================');
    }
  }
}
