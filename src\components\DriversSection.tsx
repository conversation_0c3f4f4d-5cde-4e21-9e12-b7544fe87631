import React, { useState } from 'react';
import { Car, Clock, DollarSign, MapPin, Star, Users, Smartphone, CheckCircle, ArrowRight, Shield, Award, TrendingUp, Zap } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const DriversSection = () => {
  const [activeTab, setActiveTab] = useState('benefits');
  const { t, isRTL } = useLanguage();

  const benefits = [
    {
      icon: <DollarSign className="w-8 h-8" />,
      title: "دخل مرتفع",
      description: "اكسب حتى 8000 درهم شهرياً مع مرونة في أوقات العمل",
      color: "from-green-500 to-green-600"
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: "مرونة في العمل",
      description: "اختر أوقات عملك واعمل متى شئت، بدوام كامل أو جزئي",
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: <MapPin className="w-8 h-8" />,
      title: "تغطية واسعة",
      description: "اعمل في أي منطقة تريدها في 12 مدينة مغربية",
      color: "from-purple-500 to-purple-600"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "تأمين شامل",
      description: "تأمين صحي وتأمين على المركبة مع دعم فني 24/7",
      color: "from-orange-500 to-orange-600"
    }
  ];

  const requirements = [
    {
      icon: <Car className="w-6 h-6" />,
      title: "مركبة مناسبة",
      description: "دراجة نارية أو سيارة في حالة جيدة"
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: "هاتف ذكي",
      description: "هاتف أندرويد أو آيفون مع إنترنت"
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: "رخصة قيادة",
      description: "رخصة قيادة سارية المفعول"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "العمر المناسب",
      description: "18 سنة فما فوق مع خبرة في القيادة"
    }
  ];

  const steps = [
    {
      number: "01",
      title: "حمل التطبيق",
      description: "حمل تطبيق Wasslti Partner من متجر التطبيقات",
      icon: <Smartphone className="w-6 h-6" />
    },
    {
      number: "02", 
      title: "أكمل التسجيل",
      description: "املأ بياناتك الشخصية وبيانات المركبة",
      icon: <CheckCircle className="w-6 h-6" />
    },
    {
      number: "03",
      title: "التحقق والموافقة",
      description: "سنراجع طلبك ونوافق عليه خلال 24 ساعة",
      icon: <Award className="w-6 h-6" />
    },
    {
      number: "04",
      title: "ابدأ العمل",
      description: "ابدأ في استقبال الطلبات وكسب المال فوراً",
      icon: <TrendingUp className="w-6 h-6" />
    }
  ];

  const stats = [
    {
      icon: <Users className="w-8 h-8" />,
      number: "5000+",
      label: "سائق نشط",
      color: "text-blue-500"
    },
    {
      icon: <DollarSign className="w-8 h-8" />,
      number: "6500",
      label: "متوسط الدخل الشهري",
      color: "text-green-500"
    },
    {
      icon: <Star className="w-8 h-8" />,
      number: "4.8",
      label: "تقييم السائقين",
      color: "text-yellow-500"
    },
    {
      icon: <Clock className="w-8 h-8" />,
      number: "15",
      label: "دقيقة متوسط التوصيل",
      color: "text-purple-500"
    }
  ];

  const testimonials = [
    {
      name: "محمد الإدريسي",
      city: "الدار البيضاء",
      rating: 5,
      comment: "أعمل مع وصلتي منذ سنة، الدخل ممتاز والعمل مريح. التطبيق سهل الاستخدام والدعم الفني ممتاز.",
      avatar: "https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      monthlyEarnings: "7200 درهم"
    },
    {
      name: "عبد الرحمن بنعلي",
      city: "الرباط",
      rating: 5,
      comment: "المرونة في العمل رائعة، أعمل في أوقات فراغي وأكسب دخل إضافي جيد. أنصح كل سائق بالانضمام.",
      avatar: "https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      monthlyEarnings: "5800 درهم"
    },
    {
      name: "يوسف الحسني",
      city: "مراكش",
      rating: 5,
      comment: "تطبيق Wasslti Partner سهل جداً، الطلبات كثيرة والعملاء محترمون. الدفع سريع ومضمون.",
      avatar: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      monthlyEarnings: "6900 درهم"
    }
  ];

  const handleJoinAsDriver = () => {
    alert('سيتم توجيهك لتحميل تطبيق Wasslti Partner...');
  };

  const handleDownloadPartnerApp = () => {
    alert('سيتم فتح متجر التطبيقات لتحميل Wasslti Partner...');
  };

  return (
    <section id="drivers" className="py-24 bg-gradient-to-br from-background via-white to-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-blue-400/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <Car className="w-4 h-4" />
            <span className="rtl-font">انضم كسائق</span>
          </div>
          <h2 className="text-5xl font-bold text-accent mb-6 rtl-font">
            كن جزءاً من فريق
            <br />
            <span className="text-primary">وصلتي للسائقين</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed rtl-font">
            انضم إلى آلاف السائقين الذين يكسبون دخلاً ممتازاً مع مرونة كاملة في أوقات العمل باستخدام تطبيق Wasslti Partner
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 ${stat.color} bg-opacity-10`}>
                <div className={stat.color}>
                  {stat.icon}
                </div>
              </div>
              <div className="text-2xl font-bold text-accent mb-1">{stat.number}</div>
              <div className="text-sm text-gray-600 rtl-font">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Tabs */}
        <div className="flex justify-center mb-12">
          <div className="bg-white rounded-2xl p-2 shadow-lg">
            <div className="flex space-x-2 rtl:space-x-reverse">
              {[
                { id: 'benefits', label: 'المزايا', icon: <Award className="w-4 h-4" /> },
                { id: 'requirements', label: 'المتطلبات', icon: <CheckCircle className="w-4 h-4" /> },
                { id: 'steps', label: 'خطوات التسجيل', icon: <Smartphone className="w-4 h-4" /> }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-primary text-white shadow-lg'
                      : 'text-gray-600 hover:text-primary hover:bg-gray-50'
                  }`}
                >
                  {tab.icon}
                  <span className="rtl-font">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mb-20">
          {activeTab === 'benefits' && (
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="group text-center">
                  <div className={`w-20 h-20 bg-gradient-to-br ${benefit.color} rounded-3xl flex items-center justify-center mx-auto mb-6 text-white shadow-2xl group-hover:shadow-3xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500`}>
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold text-accent mb-3 rtl-font">{benefit.title}</h3>
                  <p className="text-gray-600 leading-relaxed rtl-font">{benefit.description}</p>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'requirements' && (
            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-2 gap-8">
                {requirements.map((req, index) => (
                  <div key={index} className="flex items-start space-x-4 rtl:space-x-reverse bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                    <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center text-primary flex-shrink-0">
                      {req.icon}
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-accent mb-2 rtl-font">{req.title}</h4>
                      <p className="text-gray-600 rtl-font">{req.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'steps' && (
            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {steps.map((step, index) => (
                  <div key={index} className="relative text-center group">
                    {/* Connection Line */}
                    {index < steps.length - 1 && (
                      <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-primary/30 to-transparent z-0"></div>
                    )}
                    
                    <div className="relative z-10">
                      <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center mx-auto mb-4 text-white shadow-xl group-hover:scale-110 transition-transform duration-300">
                        <span className="text-xl font-bold">{step.number}</span>
                      </div>
                      <div className="bg-white rounded-2xl p-6 shadow-lg group-hover:shadow-xl transition-all duration-300">
                        <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4 text-primary">
                          {step.icon}
                        </div>
                        <h4 className="text-lg font-bold text-accent mb-3 rtl-font">{step.title}</h4>
                        <p className="text-gray-600 text-sm rtl-font">{step.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Wasslti Partner App Section */}
        <div className="bg-gradient-to-r from-accent via-accent/95 to-accent text-white rounded-3xl p-8 md:p-12 mb-20 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 right-0 w-40 h-40 bg-white rounded-full blur-3xl"></div>
          </div>
          
          <div className="relative z-10 grid lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div>
              <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/20 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-6">
                <Smartphone className="w-4 h-4" />
                <span className="rtl-font">تطبيق السائقين</span>
              </div>
              
              <h3 className="text-3xl md:text-4xl font-bold mb-6 rtl-font">
                تطبيق
                <br />
                <span className="text-primary">Wasslti Partner</span>
              </h3>
              
              <p className="text-xl text-gray-300 mb-8 leading-relaxed rtl-font">
                تطبيق مخصص للسائقين مع جميع الأدوات التي تحتاجها لإدارة عملك بكفاءة وسهولة
              </p>
              
              {/* Features */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                {[
                  { icon: <MapPin className="w-5 h-5" />, text: "خرائط ذكية" },
                  { icon: <DollarSign className="w-5 h-5" />, text: "تتبع الأرباح" },
                  { icon: <Clock className="w-5 h-5" />, text: "إدارة الوقت" },
                  { icon: <Star className="w-5 h-5" />, text: "نظام التقييم" }
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center text-primary">
                      {feature.icon}
                    </div>
                    <span className="font-medium rtl-font">{feature.text}</span>
                  </div>
                ))}
              </div>
              
              {/* Download Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <button 
                  onClick={handleDownloadPartnerApp}
                  className="flex items-center justify-center space-x-3 rtl:space-x-reverse bg-black text-white px-6 py-4 rounded-2xl hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-xl"
                >
                  <Smartphone className="w-6 h-6" />
                  <div className="text-right rtl:text-left">
                    <div className="text-xs opacity-80">حمل</div>
                    <div className="text-lg font-bold">Wasslti Partner</div>
                  </div>
                </button>
              </div>
            </div>
            
            {/* Phone Mockup */}
            <div className="relative">
              <div className="relative z-10 max-w-sm mx-auto">
                <div className="relative bg-gray-900 rounded-[3rem] p-2 shadow-2xl">
                  <div className="bg-black rounded-[2.5rem] overflow-hidden">
                    <div className="relative">
                      <img 
                        src="https://images.pexels.com/photos/4386321/pexels-photo-4386321.jpeg?auto=compress&cs=tinysrgb&w=400&h=800&fit=crop"
                        alt="Wasslti Partner App"
                        className="w-full h-[600px] object-cover"
                      />
                      
                      {/* App UI Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/20">
                        <div className="absolute top-0 left-0 right-0 p-6 text-white">
                          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 mb-4">
                            <div className="flex items-center space-x-3 rtl:space-x-reverse">
                              <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                                <Car className="w-6 h-6 text-white" />
                              </div>
                              <div>
                                <h3 className="font-bold rtl-font">Wasslti Partner</h3>
                                <p className="text-sm opacity-80 rtl-font">للسائقين</p>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                          <div className="grid grid-cols-2 gap-3 mb-4">
                            <div className="bg-primary/20 backdrop-blur-sm rounded-xl p-3 text-center">
                              <div className="text-lg font-bold">12</div>
                              <div className="text-xs opacity-80">طلب اليوم</div>
                            </div>
                            <div className="bg-primary/20 backdrop-blur-sm rounded-xl p-3 text-center">
                              <div className="text-lg font-bold">450 درهم</div>
                              <div className="text-xs opacity-80">أرباح اليوم</div>
                            </div>
                          </div>
                          
                          <div className="bg-primary/20 backdrop-blur-sm rounded-xl p-3 text-center">
                            <div className="text-sm font-bold">متصل - جاهز لاستقبال الطلبات</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Background Glow */}
              <div className="absolute inset-0 bg-primary/20 rounded-3xl blur-3xl scale-110"></div>
            </div>
          </div>
        </div>

        {/* Testimonials */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-accent mb-4 rtl-font">آراء السائقين</h3>
            <p className="text-gray-600 text-lg rtl-font">ماذا يقول شركاؤنا من السائقين</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="flex items-center mb-4">
                  <img 
                    src={testimonial.avatar} 
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full object-cover mr-4"
                  />
                  <div>
                    <h4 className="font-bold text-accent rtl-font">{testimonial.name}</h4>
                    <p className="text-sm text-gray-500 rtl-font">{testimonial.city}</p>
                  </div>
                </div>
                
                <div className="flex items-center mb-3">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                <p className="text-gray-700 mb-4 rtl-font">"{testimonial.comment}"</p>
                
                <div className="bg-primary/5 rounded-xl p-3 text-center">
                  <div className="text-lg font-bold text-primary">{testimonial.monthlyEarnings}</div>
                  <div className="text-sm text-gray-600 rtl-font">الدخل الشهري</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-primary via-primary/95 to-primary/90 rounded-3xl p-8 md:p-12 text-white text-center relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-white rounded-full blur-3xl"></div>
          </div>
          
          <div className="relative z-10">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Car className="w-10 h-10 text-white" />
            </div>
            
            <h3 className="text-3xl font-bold mb-4 rtl-font">جاهز لتبدأ رحلتك معنا؟</h3>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto rtl-font">
              انضم إلى فريق وصلتي اليوم وابدأ في كسب دخل ممتاز مع مرونة كاملة في العمل
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={handleJoinAsDriver}
                className="bg-white text-primary px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 hover:scale-105 transition-all duration-300 shadow-xl flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <span>انضم كسائق الآن</span>
                <ArrowRight className="w-5 h-5" />
              </button>
              <button 
                onClick={handleDownloadPartnerApp}
                className="bg-white/20 backdrop-blur-sm text-white border-2 border-white/30 px-8 py-4 rounded-2xl font-bold hover:bg-white/30 hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <Smartphone className="w-5 h-5" />
                <span>حمل التطبيق</span>
              </button>
            </div>
            
            <div className="grid grid-cols-3 gap-8 mt-12 pt-8 border-t border-white/20">
              <div>
                <div className="text-2xl font-bold mb-1">24/7</div>
                <div className="text-sm opacity-80 rtl-font">دعم فني</div>
              </div>
              <div>
                <div className="text-2xl font-bold mb-1">5000+</div>
                <div className="text-sm opacity-80 rtl-font">سائق نشط</div>
              </div>
              <div>
                <div className="text-2xl font-bold mb-1">12</div>
                <div className="text-sm opacity-80 rtl-font">مدينة متاحة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DriversSection;