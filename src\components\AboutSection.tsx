import React from 'react';
import { Users, Target, Award, Heart, CheckCircle, Star, Globe, Shield } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const AboutSection = () => {
  const { t, isRTL } = useLanguage();

  const values = [
    {
      icon: <Heart className="w-8 h-8" />,
      title: "شغف بالطعام",
      description: "نحب الطعام الجيد ونسعى لتقديم أفضل تجربة طعام لعملائنا",
      color: "from-red-500 to-red-600"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "خدمة العملاء",
      description: "رضا عملائنا هو أولويتنا القصوى في كل ما نقوم به",
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: <Award className="w-8 h-8" />,
      title: "الجودة والتميز",
      description: "نلتزم بأعلى معايير الجودة في الطعام والخدمة",
      color: "from-yellow-500 to-yellow-600"
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "الابتكار",
      description: "نستخدم أحدث التقنيات لتحسين تجربة توصيل الطعام",
      color: "from-purple-500 to-purple-600"
    }
  ];

  const achievements = [
    {
      icon: <Users className="w-8 h-8" />,
      number: "1M+",
      label: "عميل سعيد",
      color: "text-blue-500"
    },
    {
      icon: <Star className="w-8 h-8" />,
      number: "500+",
      label: "مطعم شريك",
      color: "text-yellow-500"
    },
    {
      icon: <Globe className="w-8 h-8" />,
      number: "12",
      label: "مدينة مغربية",
      color: "text-green-500"
    },
    {
      icon: <Award className="w-8 h-8" />,
      number: "4.9",
      label: "تقييم العملاء",
      color: "text-purple-500"
    }
  ];

  const timeline = [
    {
      year: "2021",
      title: "البداية",
      description: "تأسيس وصلتي في الدار البيضاء مع 50 مطعم شريك"
    },
    {
      year: "2022",
      title: "التوسع",
      description: "وصلنا إلى 5 مدن مغربية مع أكثر من 200 مطعم"
    },
    {
      year: "2023",
      title: "النمو السريع",
      description: "تجاوزنا المليون عميل وأطلقنا برنامج الولاء"
    },
    {
      year: "2024",
      title: "القيادة",
      description: "أصبحنا المنصة الرائدة لتوصيل الطعام في المغرب"
    }
  ];

  return (
    <section id="about" className="py-24 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-72 h-72 bg-blue-400/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <Heart className="w-4 h-4" />
            <span className="rtl-font">من نحن</span>
          </div>
          <h2 className="text-5xl font-bold text-accent mb-6 rtl-font">
            قصة
            <br />
            <span className="text-primary">وصلتي</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed rtl-font">
            نحن منصة المغرب الرائدة لتوصيل الطعام، نربط بين الناس وطعامهم المفضل من أفضل المطاعم في المملكة
          </p>
        </div>

        {/* Story Section */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          <div>
            <h3 className="text-3xl font-bold text-accent mb-6 rtl-font">رحلتنا نحو التميز</h3>
            <p className="text-gray-600 mb-6 leading-relaxed rtl-font">
              بدأت وصلتي كحلم بسيط: جعل الطعام اللذيذ متاحاً للجميع في أي وقت وأي مكان. منذ انطلاقتنا في 2021، 
              نمونا لنصبح أكبر منصة لتوصيل الطعام في المغرب.
            </p>
            <p className="text-gray-600 mb-8 leading-relaxed rtl-font">
              نؤمن بأن الطعام الجيد يجمع الناس، ولهذا نعمل بلا كلل لضمان وصول أشهى الأطباق من أفضل المطاعم 
              إلى منازلكم بأسرع وقت ممكن وبأعلى جودة.
            </p>
            
            <div className="grid grid-cols-2 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">2021</div>
                <div className="text-sm text-gray-600 rtl-font">سنة التأسيس</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">3 سنوات</div>
                <div className="text-sm text-gray-600 rtl-font">من الخبرة</div>
              </div>
            </div>
          </div>
          
          <div className="relative">
            <img 
              src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
              alt="فريق وصلتي"
              className="w-full h-80 object-cover rounded-3xl shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-3xl"></div>
            <div className="absolute bottom-6 left-6 text-white">
              <h4 className="text-xl font-bold rtl-font">فريق وصلتي</h4>
              <p className="opacity-90 rtl-font">نعمل معاً لخدمتكم</p>
            </div>
          </div>
        </div>

        {/* Values */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-accent mb-4 rtl-font">قيمنا ومبادئنا</h3>
            <p className="text-gray-600 text-lg rtl-font">المبادئ التي توجه عملنا كل يوم</p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="group text-center">
                <div className={`w-20 h-20 bg-gradient-to-br ${value.color} rounded-3xl flex items-center justify-center mx-auto mb-6 text-white shadow-2xl group-hover:shadow-3xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500`}>
                  {value.icon}
                </div>
                <h4 className="text-xl font-bold text-accent mb-3 rtl-font">{value.title}</h4>
                <p className="text-gray-600 leading-relaxed rtl-font">{value.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Achievements */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-accent mb-4 rtl-font">إنجازاتنا</h3>
            <p className="text-gray-600 text-lg rtl-font">أرقام تعكس ثقتكم بنا</p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 ${achievement.color} bg-opacity-10`}>
                  <div className={achievement.color}>
                    {achievement.icon}
                  </div>
                </div>
                <div className="text-3xl font-bold text-accent mb-2">{achievement.number}</div>
                <div className="text-sm text-gray-600 rtl-font">{achievement.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Timeline */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-accent mb-4 rtl-font">رحلة النمو</h3>
            <p className="text-gray-600 text-lg rtl-font">المحطات المهمة في تاريخ وصلتي</p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary/20 rounded-full"></div>
              
              <div className="space-y-12">
                {timeline.map((item, index) => (
                  <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                    <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                      <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                        <div className="text-2xl font-bold text-primary mb-2">{item.year}</div>
                        <h4 className="text-xl font-bold text-accent mb-3 rtl-font">{item.title}</h4>
                        <p className="text-gray-600 rtl-font">{item.description}</p>
                      </div>
                    </div>
                    
                    {/* Timeline Dot */}
                    <div className="relative z-10">
                      <div className="w-6 h-6 bg-primary rounded-full border-4 border-white shadow-lg"></div>
                    </div>
                    
                    <div className="w-1/2"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-8 mb-20">
          <div className="bg-gradient-to-br from-primary/5 to-primary/10 rounded-3xl p-8">
            <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mb-6">
              <Target className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-accent mb-4 rtl-font">رسالتنا</h3>
            <p className="text-gray-700 leading-relaxed rtl-font">
              نسعى لجعل تجربة طلب الطعام سهلة وممتعة لكل عائلة مغربية، من خلال ربطهم بأفضل المطاعم 
              وتقديم خدمة توصيل سريعة وموثوقة تلبي احتياجاتهم في أي وقت.
            </p>
          </div>
          
          <div className="bg-gradient-to-br from-blue-500/5 to-blue-500/10 rounded-3xl p-8">
            <div className="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mb-6">
              <Star className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-accent mb-4 rtl-font">رؤيتنا</h3>
            <p className="text-gray-700 leading-relaxed rtl-font">
              أن نكون المنصة الأولى والأكثر ثقة لتوصيل الطعام في المغرب والمنطقة، ونساهم في دعم 
              الاقتصاد المحلي وخلق فرص عمل جديدة للشباب المغربي.
            </p>
          </div>
        </div>

        {/* Team Section */}
        <div className="text-center">
          <h3 className="text-3xl font-bold text-accent mb-6 rtl-font">فريق العمل</h3>
          <p className="text-gray-600 text-lg mb-8 rtl-font">
            فريق متخصص ومتحمس يعمل على مدار الساعة لضمان أفضل تجربة لعملائنا
          </p>
          
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "أحمد المرابط",
                position: "المدير التنفيذي",
                image: "https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop"
              },
              {
                name: "فاطمة الزهراء",
                position: "مديرة العمليات",
                image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop"
              },
              {
                name: "يوسف بنعلي",
                position: "مدير التقنية",
                image: "https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop"
              }
            ].map((member, index) => (
              <div key={index} className="group">
                <div className="relative mb-4">
                  <img 
                    src={member.image}
                    alt={member.name}
                    className="w-32 h-32 rounded-full object-cover mx-auto shadow-xl group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-primary/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <h4 className="text-xl font-bold text-accent mb-2 rtl-font">{member.name}</h4>
                <p className="text-gray-600 rtl-font">{member.position}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;