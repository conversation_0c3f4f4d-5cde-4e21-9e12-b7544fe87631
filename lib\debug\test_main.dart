import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/driver_themes.dart';
import '../providers/auth_provider.dart';
import 'login_test_screen.dart';

/// تطبيق اختبار لتجربة تسجيل الدخول
void main() {
  runApp(const LoginTestApp());
}

class LoginTestApp extends StatelessWidget {
  const LoginTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'Login Test App',
        debugShowCheckedModeBanner: false,
        theme: DriverThemes.lightTheme,
        darkTheme: DriverThemes.darkTheme,
        themeMode: ThemeMode.system,
        home: const LoginTestScreen(),
      ),
    );
  }
}
