import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'constants/driver_themes.dart';
import 'providers/auth_provider.dart';
import 'providers/language_provider.dart';
import 'screens/profile/edit_profile_screen.dart';
import 'screens/profile/change_password_screen.dart';
import 'screens/profile/manage_documents_screen.dart';

/// Test profile screens functionality
void main() {
  runApp(const TestProfileScreensApp());
}

class TestProfileScreensApp extends StatelessWidget {
  const TestProfileScreensApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
      ],
      child: Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
          return MaterialApp(
            title: 'Test Profile Screens',
            debugShowCheckedModeBanner: false,
            theme: DriverThemes.lightTheme,
            darkTheme: DriverThemes.darkTheme,
            themeMode: ThemeMode.system,
            home: const TestProfileScreensHome(),
          );
        },
      ),
    );
  }

  Widget _buildScreenButton({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 28,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          color: isDark ? Colors.blue[300] : Colors.blue[700],
        ),
      ),
    );
  }
}

class TestProfileScreensHome extends StatefulWidget {
  const TestProfileScreensHome({super.key});

  @override
  State<TestProfileScreensHome> createState() => _TestProfileScreensHomeState();
}

class _TestProfileScreensHomeState extends State<TestProfileScreensHome> {
  @override
  void initState() {
    super.initState();
    // Initialize language provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LanguageProvider>().initializeLanguage();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          title: Text(
            languageProvider.getText('Profile Screens Test', 'اختبار شاشات الملف الشخصي'),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: const Color(0xFF11B96F),
          foregroundColor: Colors.white,
          actions: [
            PopupMenuButton<String>(
              onSelected: (value) => languageProvider.changeLanguage(value),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'English',
                  child: Text('English'),
                ),
                const PopupMenuItem(
                  value: 'العربية',
                  child: Text('العربية'),
                ),
              ],
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.language, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      languageProvider.currentLanguage,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    const Icon(
                      Icons.account_circle,
                      size: 80,
                      color: Color(0xFF11B96F),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      languageProvider.getText(
                        'Profile Management Screens',
                        'شاشات إدارة الملف الشخصي'
                      ),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      languageProvider.getText(
                        'Test all profile-related functionality',
                        'اختبر جميع وظائف الملف الشخصي'
                      ),
                      style: TextStyle(
                        fontSize: 16,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              // Profile Screens
              _buildScreenButton(
                title: languageProvider.editProfile,
                description: languageProvider.getText(
                  'Update personal information and profile picture',
                  'تحديث المعلومات الشخصية وصورة الملف الشخصي'
                ),
                icon: Icons.person_outline,
                color: const Color(0xFF11B96F),
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const EditProfileScreen(),
                  ),
                ),
                isDark: isDark,
              ),

              const SizedBox(height: 16),

              _buildScreenButton(
                title: languageProvider.changePassword,
                description: languageProvider.getText(
                  'Update your password for better security',
                  'تحديث كلمة المرور لأمان أفضل'
                ),
                icon: Icons.lock_outline,
                color: Colors.blue,
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ChangePasswordScreen(),
                  ),
                ),
                isDark: isDark,
              ),

              const SizedBox(height: 16),

              _buildScreenButton(
                title: languageProvider.manageDocuments,
                description: languageProvider.getText(
                  'Upload and manage required documents',
                  'رفع وإدارة الوثائق المطلوبة'
                ),
                icon: Icons.description_outlined,
                color: Colors.orange,
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ManageDocumentsScreen(),
                  ),
                ),
                isDark: isDark,
              ),

              const SizedBox(height: 30),

              // Features List
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF1B3B2E) : Colors.blue[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isDark ? Colors.blue[800]! : Colors.blue[200]!,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: isDark ? Colors.blue[400] : Colors.blue[600],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          languageProvider.getText('Features', 'الميزات'),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isDark ? Colors.blue[400] : Colors.blue[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildFeatureItem(
                      languageProvider.getText(
                        '✅ Edit Profile with form validation',
                        '✅ تعديل الملف الشخصي مع التحقق من البيانات'
                      ),
                      isDark,
                    ),
                    _buildFeatureItem(
                      languageProvider.getText(
                        '✅ Change Password with security requirements',
                        '✅ تغيير كلمة المرور مع متطلبات الأمان'
                      ),
                      isDark,
                    ),
                    _buildFeatureItem(
                      languageProvider.getText(
                        '✅ Document Management with status tracking',
                        '✅ إدارة الوثائق مع تتبع الحالة'
                      ),
                      isDark,
                    ),
                    _buildFeatureItem(
                      languageProvider.getText(
                        '✅ Bilingual support (Arabic/English)',
                        '✅ دعم ثنائي اللغة (عربي/إنجليزي)'
                      ),
                      isDark,
                    ),
                    _buildFeatureItem(
                      languageProvider.getText(
                        '✅ Dark/Light theme support',
                        '✅ دعم الوضع المظلم والفاتح'
                      ),
                      isDark,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
