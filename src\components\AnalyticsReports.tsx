import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar, 
  Download, 
  Filter, 
  RefreshCw, 
  Users, 
  Clock, 
  MapPin, 
  Star, 
  Car, 
  Target,
  ThumbsUp,
  Utensils,
  Repeat,
  ArrowUpRight,
  ArrowDownRight,
  Percent,
  Activity
} from 'lucide-react';

const AnalyticsReports = () => {
  const [timeFilter, setTimeFilter] = useState('month');
  const [reportType, setReportType] = useState('financial');
  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 2000);
  };

  const handleExport = (format: string) => {
    alert(`سيتم تصدير التقرير بصيغة ${format}...`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white rtl-font">
            التحليلات والتقارير
          </h1>
          <p className="text-gray-600 dark:text-gray-400 rtl-font">
            تحليلات مفصلة وتقارير شاملة لأداء المنصة
          </p>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <select
            value={timeFilter}
            onChange={(e) => setTimeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            <option value="day">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="quarter">هذا الربع</option>
            <option value="year">هذا العام</option>
          </select>
          
          <button 
            onClick={handleRefresh}
            disabled={isLoading}
            className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>تحديث</span>
          </button>
          
          <div className="relative group">
            <button className="bg-primary text-white px-6 py-2 rounded-xl font-semibold hover:bg-primary/90 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
              <Download className="w-4 h-4" />
              <span>تصدير</span>
            </button>
            <div className="absolute left-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-10">
              <button 
                onClick={() => handleExport('pdf')}
                className="w-full text-left px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300 text-sm rounded-t-xl"
              >
                PDF
              </button>
              <button 
                onClick={() => handleExport('excel')}
                className="w-full text-left px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300 text-sm"
              >
                Excel
              </button>
              <button 
                onClick={() => handleExport('csv')}
                className="w-full text-left px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300 text-sm rounded-b-xl"
              >
                CSV
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Report Type Tabs */}
      <div className="flex justify-center mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-2 shadow-lg">
          <div className="flex space-x-2 rtl:space-x-reverse">
            {[
              { id: 'financial', label: 'التقارير المالية', icon: <DollarSign className="w-4 h-4" /> },
              { id: 'customers', label: 'سلوك العملاء', icon: <Users className="w-4 h-4" /> },
              { id: 'drivers', label: 'أداء السائقين', icon: <Car className="w-4 h-4" /> }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setReportType(tab.id)}
                className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                  reportType === tab.id
                    ? 'bg-primary text-white shadow-lg'
                    : 'text-gray-600 dark:text-gray-300 hover:text-primary hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                {tab.icon}
                <span className="rtl-font">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Financial Reports */}
      {reportType === 'financial' && (
        <div className="space-y-6">
          {/* Revenue Overview */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800 dark:text-white rtl-font">
                نظرة عامة على الإيرادات
              </h3>
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <div className="w-3 h-3 bg-primary rounded-full"></div>
                  <span className="text-gray-600 dark:text-gray-400">الإيرادات</span>
                </div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-600 dark:text-gray-400">العمولات</span>
                </div>
              </div>
            </div>
            
            {/* Chart Placeholder */}
            <div className="h-80 bg-gray-50 dark:bg-gray-700 rounded-xl mb-4 relative overflow-hidden">
              {/* Simulated Chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full h-full px-4 py-6">
                  <div className="relative h-full">
                    {/* Revenue Bars */}
                    <div className="absolute bottom-0 left-[5%] w-[5%] h-[60%] bg-primary/80 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[15%] w-[5%] h-[75%] bg-primary/80 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[25%] w-[5%] h-[65%] bg-primary/80 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[35%] w-[5%] h-[80%] bg-primary/80 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[45%] w-[5%] h-[70%] bg-primary/80 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[55%] w-[5%] h-[85%] bg-primary/80 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[65%] w-[5%] h-[90%] bg-primary/80 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[75%] w-[5%] h-[78%] bg-primary/80 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[85%] w-[5%] h-[88%] bg-primary/80 rounded-t-md"></div>
                    
                    {/* Commission Line */}
                    <div className="absolute bottom-[30%] left-[5%] w-[85%] h-[2px] bg-blue-500"></div>
                    <div className="absolute bottom-[30%] left-[5%] w-[2px] h-[2px] bg-blue-500 rounded-full"></div>
                    <div className="absolute bottom-[35%] left-[15%] w-[2px] h-[2px] bg-blue-500 rounded-full"></div>
                    <div className="absolute bottom-[32%] left-[25%] w-[2px] h-[2px] bg-blue-500 rounded-full"></div>
                    <div className="absolute bottom-[38%] left-[35%] w-[2px] h-[2px] bg-blue-500 rounded-full"></div>
                    <div className="absolute bottom-[36%] left-[45%] w-[2px] h-[2px] bg-blue-500 rounded-full"></div>
                    <div className="absolute bottom-[40%] left-[55%] w-[2px] h-[2px] bg-blue-500 rounded-full"></div>
                    <div className="absolute bottom-[42%] left-[65%] w-[2px] h-[2px] bg-blue-500 rounded-full"></div>
                    <div className="absolute bottom-[38%] left-[75%] w-[2px] h-[2px] bg-blue-500 rounded-full"></div>
                    <div className="absolute bottom-[44%] left-[85%] w-[2px] h-[2px] bg-blue-500 rounded-full"></div>
                  </div>
                </div>
              </div>
              
              {/* Chart Labels */}
              <div className="absolute bottom-2 left-0 right-0 flex justify-between px-8 text-xs text-gray-500 dark:text-gray-400">
                <span>يناير</span>
                <span>فبراير</span>
                <span>مارس</span>
                <span>أبريل</span>
                <span>مايو</span>
                <span>يونيو</span>
                <span>يوليو</span>
                <span>أغسطس</span>
                <span>سبتمبر</span>
              </div>
            </div>
            
            {/* Revenue Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">إجمالي الإيرادات</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">845,290 درهم</div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse text-green-600 text-sm mt-1">
                  <TrendingUp className="w-4 h-4" />
                  <span>+23.5%</span>
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">العمولات</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">126,793 درهم</div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse text-green-600 text-sm mt-1">
                  <TrendingUp className="w-4 h-4" />
                  <span>+18.2%</span>
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">تكاليف التشغيل</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">67,450 درهم</div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse text-red-600 text-sm mt-1">
                  <TrendingDown className="w-4 h-4" />
                  <span>-5.3%</span>
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">الأرباح الصافية</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">59,343 درهم</div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse text-green-600 text-sm mt-1">
                  <TrendingUp className="w-4 h-4" />
                  <span>+27.8%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Revenue Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue by Source */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 rtl-font">
                الإيرادات حسب المصدر
              </h3>
              
              <div className="flex items-center space-x-6 rtl:space-x-reverse">
                {/* Pie Chart Placeholder */}
                <div className="w-40 h-40 relative">
                  <div className="absolute inset-0 rounded-full border-8 border-primary/80"></div>
                  <div className="absolute inset-0 rounded-full border-8 border-blue-500/80" style={{ clipPath: 'polygon(50% 50%, 100% 50%, 100% 0, 50% 0)' }}></div>
                  <div className="absolute inset-0 rounded-full border-8 border-green-500/80" style={{ clipPath: 'polygon(50% 50%, 50% 0, 0 0, 0 50%)' }}></div>
                  <div className="absolute inset-0 rounded-full border-8 border-yellow-500/80" style={{ clipPath: 'polygon(50% 50%, 0 50%, 0 100%, 50% 100%)' }}></div>
                </div>
                
                <div className="flex-1 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div className="w-3 h-3 bg-primary rounded-full"></div>
                      <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">توصيل الطعام</span>
                    </div>
                    <div className="text-sm font-semibold text-gray-800 dark:text-white">65%</div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">عمولات المطاعم</span>
                    </div>
                    <div className="text-sm font-semibold text-gray-800 dark:text-white">20%</div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">اشتراكات المطاعم</span>
                    </div>
                    <div className="text-sm font-semibold text-gray-800 dark:text-white">10%</div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إعلانات وترويجات</span>
                    </div>
                    <div className="text-sm font-semibold text-gray-800 dark:text-white">5%</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Revenue by City */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 rtl-font">
                الإيرادات حسب المدينة
              </h3>
              
              <div className="space-y-4">
                {[
                  { city: 'الدار البيضاء', revenue: 320450, percentage: 38, growth: '+15.2%' },
                  { city: 'الرباط', revenue: 185320, percentage: 22, growth: '+12.8%' },
                  { city: 'مراكش', revenue: 152780, percentage: 18, growth: '+24.5%' },
                  { city: 'فاس', revenue: 93650, percentage: 11, growth: '+8.3%' },
                  { city: 'طنجة', revenue: 76540, percentage: 9, growth: '+19.7%' }
                ].map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <MapPin className="w-4 h-4 text-gray-500" />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">{item.city}</span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <span className="text-sm font-semibold text-gray-800 dark:text-white">{item.revenue.toLocaleString()} درهم</span>
                        <span className="text-xs text-green-600">{item.growth}</span>
                      </div>
                    </div>
                    <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-primary rounded-full"
                        style={{ width: `${item.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Comparison with Previous Periods */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 rtl-font">
              مقارنة مع الفترات السابقة
            </h3>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">المؤشر</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">الشهر الحالي</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">الشهر السابق</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">التغيير</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">نفس الشهر العام الماضي</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">التغيير السنوي</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                  {[
                    { 
                      metric: 'إجمالي الإيرادات', 
                      current: '845,290', 
                      previous: '768,120', 
                      change: '+10.0%', 
                      lastYear: '542,350', 
                      yearChange: '+55.9%',
                      isPositive: true
                    },
                    { 
                      metric: 'عدد الطلبات', 
                      current: '12,847', 
                      previous: '11,523', 
                      change: '+11.5%', 
                      lastYear: '8,234', 
                      yearChange: '+56.0%',
                      isPositive: true
                    },
                    { 
                      metric: 'متوسط قيمة الطلب', 
                      current: '65.8', 
                      previous: '66.7', 
                      change: '-1.3%', 
                      lastYear: '65.9', 
                      yearChange: '-0.2%',
                      isPositive: false
                    },
                    { 
                      metric: 'العمولات', 
                      current: '126,793', 
                      previous: '115,218', 
                      change: '+10.0%', 
                      lastYear: '81,352', 
                      yearChange: '+55.9%',
                      isPositive: true
                    },
                    { 
                      metric: 'تكاليف التشغيل', 
                      current: '67,450', 
                      previous: '71,235', 
                      change: '-5.3%', 
                      lastYear: '54,235', 
                      yearChange: '+24.4%',
                      isPositive: true
                    },
                    { 
                      metric: 'الأرباح الصافية', 
                      current: '59,343', 
                      previous: '43,983', 
                      change: '+34.9%', 
                      lastYear: '27,117', 
                      yearChange: '+118.8%',
                      isPositive: true
                    }
                  ].map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <td className="px-4 py-3 text-sm font-medium text-gray-800 dark:text-white rtl-font">{row.metric}</td>
                      <td className="px-4 py-3 text-sm text-gray-700 dark:text-gray-300">{row.current}</td>
                      <td className="px-4 py-3 text-sm text-gray-700 dark:text-gray-300">{row.previous}</td>
                      <td className="px-4 py-3 text-sm">
                        <span className={`flex items-center space-x-1 rtl:space-x-reverse ${
                          row.isPositive ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {row.isPositive ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                          <span>{row.change}</span>
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-700 dark:text-gray-300">{row.lastYear}</td>
                      <td className="px-4 py-3 text-sm">
                        <span className={`flex items-center space-x-1 rtl:space-x-reverse ${
                          row.isPositive ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {row.isPositive ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                          <span>{row.yearChange}</span>
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Customer Behavior Analysis */}
      {reportType === 'customers' && (
        <div className="space-y-6">
          {/* Peak Order Times */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-6 rtl-font">
              أوقات الذروة للطلبات
            </h3>
            
            {/* Chart Placeholder */}
            <div className="h-80 bg-gray-50 dark:bg-gray-700 rounded-xl mb-4 relative overflow-hidden">
              {/* Simulated Chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full h-full px-4 py-6">
                  <div className="relative h-full">
                    {/* Hour Bars */}
                    <div className="absolute bottom-0 left-[4%] w-[3%] h-[20%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[8%] w-[3%] h-[15%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[12%] w-[3%] h-[10%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[16%] w-[3%] h-[5%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[20%] w-[3%] h-[8%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[24%] w-[3%] h-[12%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[28%] w-[3%] h-[25%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[32%] w-[3%] h-[40%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[36%] w-[3%] h-[55%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[40%] w-[3%] h-[45%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[44%] w-[3%] h-[35%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[48%] w-[3%] h-[60%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[52%] w-[3%] h-[80%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[56%] w-[3%] h-[90%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[60%] w-[3%] h-[75%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[64%] w-[3%] h-[65%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[68%] w-[3%] h-[85%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[72%] w-[3%] h-[95%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[76%] w-[3%] h-[70%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[80%] w-[3%] h-[50%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[84%] w-[3%] h-[40%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[88%] w-[3%] h-[30%] bg-primary/60 rounded-t-md"></div>
                    <div className="absolute bottom-0 left-[92%] w-[3%] h-[20%] bg-primary/60 rounded-t-md"></div>
                  </div>
                </div>
              </div>
              
              {/* Chart Labels */}
              <div className="absolute bottom-2 left-0 right-0 flex justify-between px-4 text-xs text-gray-500 dark:text-gray-400">
                <span>00:00</span>
                <span>06:00</span>
                <span>12:00</span>
                <span>18:00</span>
                <span>23:00</span>
              </div>
            </div>
            
            {/* Peak Times Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">وقت الذروة</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">19:00 - 21:00</div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 rtl-font">35% من الطلبات</div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">وقت الغداء</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">12:00 - 14:00</div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 rtl-font">25% من الطلبات</div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">أيام الذروة</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">الجمعة، السبت</div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 rtl-font">45% من الطلبات الأسبوعية</div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">متوسط وقت الانتظار</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">22 دقيقة</div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse text-green-600 text-sm mt-1">
                  <TrendingDown className="w-4 h-4" />
                  <span>-8.3%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Most Ordered Areas & Food Preferences */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Most Ordered Areas */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 rtl-font">
                المناطق الأكثر طلباً
              </h3>
              
              <div className="space-y-4">
                {[
                  { area: 'المعاريف، الدار البيضاء', orders: 3450, percentage: 18, growth: '+12.5%' },
                  { area: 'أكدال، الرباط', orders: 2780, percentage: 14, growth: '+8.7%' },
                  { area: 'جليز، مراكش', orders: 2340, percentage: 12, growth: '+15.2%' },
                  { area: 'الحي المحمدي، الدار البيضاء', orders: 1980, percentage: 10, growth: '+5.3%' },
                  { area: 'حسان، الرباط', orders: 1650, percentage: 8, growth: '+9.8%' }
                ].map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <MapPin className="w-4 h-4 text-primary" />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">{item.area}</span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <span className="text-sm font-semibold text-gray-800 dark:text-white">{item.orders}</span>
                        <span className="text-xs text-green-600">{item.growth}</span>
                      </div>
                    </div>
                    <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-primary rounded-full"
                        style={{ width: `${item.percentage * 5}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Food Preferences by Area */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 rtl-font">
                تفضيلات الطعام حسب المنطقة
              </h3>
              
              <div className="space-y-6">
                {[
                  { 
                    area: 'الدار البيضاء', 
                    preferences: [
                      { type: 'وجبات سريعة', percentage: 35 },
                      { type: 'مأكولات مغربية', percentage: 25 },
                      { type: 'مأكولات عالمية', percentage: 20 },
                      { type: 'حلويات', percentage: 15 },
                      { type: 'أخرى', percentage: 5 }
                    ]
                  },
                  { 
                    area: 'الرباط', 
                    preferences: [
                      { type: 'مأكولات مغربية', percentage: 40 },
                      { type: 'وجبات سريعة', percentage: 30 },
                      { type: 'مأكولات عالمية', percentage: 15 },
                      { type: 'حلويات', percentage: 10 },
                      { type: 'أخرى', percentage: 5 }
                    ]
                  }
                ].map((item, index) => (
                  <div key={index}>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mb-3">
                      <MapPin className="w-4 h-4 text-primary" />
                      <h4 className="font-semibold text-gray-800 dark:text-white rtl-font">{item.area}</h4>
                    </div>
                    
                    <div className="flex items-center space-x-1 rtl:space-x-reverse">
                      {item.preferences.map((pref, i) => (
                        <div 
                          key={i}
                          className="h-8 rounded-md"
                          style={{ 
                            width: `${pref.percentage}%`,
                            backgroundColor: i === 0 ? '#11B96F' : 
                                            i === 1 ? '#3B82F6' : 
                                            i === 2 ? '#8B5CF6' : 
                                            i === 3 ? '#F59E0B' : '#6B7280'
                          }}
                          title={`${pref.type}: ${pref.percentage}%`}
                        ></div>
                      ))}
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 mt-3">
                      {item.preferences.map((pref, i) => (
                        <div key={i} className="flex items-center space-x-2 rtl:space-x-reverse">
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ 
                              backgroundColor: i === 0 ? '#11B96F' : 
                                              i === 1 ? '#3B82F6' : 
                                              i === 2 ? '#8B5CF6' : 
                                              i === 3 ? '#F59E0B' : '#6B7280'
                            }}
                          ></div>
                          <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">
                            {pref.type} ({pref.percentage}%)
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Customer Retention */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 rtl-font">
              معدل العملاء المتكررين
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Retention Rate */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 text-center">
                <div className="relative w-32 h-32 mx-auto mb-4">
                  <div className="absolute inset-0 rounded-full border-8 border-gray-200 dark:border-gray-600"></div>
                  <div 
                    className="absolute inset-0 rounded-full border-8 border-primary"
                    style={{ clipPath: 'polygon(50% 50%, 100% 50%, 100% 0, 50% 0)' }}
                  ></div>
                  <div 
                    className="absolute inset-0 rounded-full border-8 border-primary"
                    style={{ clipPath: 'polygon(50% 50%, 50% 0, 0 0, 0 50%)' }}
                  ></div>
                  <div 
                    className="absolute inset-0 rounded-full border-8 border-primary"
                    style={{ clipPath: 'polygon(50% 50%, 0 50%, 0 100%, 25% 100%)' }}
                  ></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-3xl font-bold text-gray-800 dark:text-white">78%</div>
                  </div>
                </div>
                <h4 className="text-lg font-semibold text-gray-800 dark:text-white rtl-font">معدل الاحتفاظ</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 rtl-font">
                  نسبة العملاء الذين يطلبون مرة أخرى خلال 30 يوم
                </p>
              </div>
              
              {/* Repeat Customers */}
              <div className="md:col-span-2">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold text-gray-800 dark:text-white rtl-font">تكرار الطلبات</h4>
                    <div className="text-sm text-gray-600 dark:text-gray-400">إجمالي العملاء: 8,942</div>
                  </div>
                  
                  <div className="space-y-3">
                    {[
                      { label: 'طلب واحد فقط', count: 1967, percentage: 22 },
                      { label: '2-5 طلبات', count: 3845, percentage: 43 },
                      { label: '6-10 طلبات', count: 1878, percentage: 21 },
                      { label: '11-20 طلب', count: 894, percentage: 10 },
                      { label: 'أكثر من 20 طلب', count: 358, percentage: 4 }
                    ].map((item, index) => (
                      <div key={index} className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">{item.label}</span>
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <span className="text-sm font-medium text-gray-800 dark:text-white">{item.count}</span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">({item.percentage}%)</span>
                          </div>
                        </div>
                        <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-primary rounded-full"
                            style={{ width: `${item.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Driver Performance Analysis */}
      {reportType === 'drivers' && (
        <div className="space-y-6">
          {/* Delivery Time Analysis */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-6 rtl-font">
              تحليل أوقات التوصيل
            </h3>
            
            {/* Chart Placeholder */}
            <div className="h-80 bg-gray-50 dark:bg-gray-700 rounded-xl mb-4 relative overflow-hidden">
              {/* Simulated Chart */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full h-full px-4 py-6">
                  <div className="relative h-full">
                    {/* Line Chart */}
                    <div className="absolute top-1/2 left-0 right-0 h-[1px] bg-gray-300 dark:bg-gray-500"></div>
                    <div className="absolute top-1/4 left-0 right-0 h-[1px] bg-gray-300 dark:bg-gray-500"></div>
                    <div className="absolute top-3/4 left-0 right-0 h-[1px] bg-gray-300 dark:bg-gray-500"></div>
                    
                    {/* Average Line */}
                    <div className="absolute top-[40%] left-[5%] right-[5%] h-[2px] bg-primary"></div>
                    
                    {/* Data Points */}
                    <div className="absolute top-[30%] left-[10%] w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-[35%] left-[20%] w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-[25%] left-[30%] w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-[20%] left-[40%] w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-[15%] left-[50%] w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-[25%] left-[60%] w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-[20%] left-[70%] w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-[15%] left-[80%] w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-[20%] left-[90%] w-2 h-2 bg-blue-500 rounded-full"></div>
                    
                    {/* Target Line */}
                    <div className="absolute top-[20%] left-[5%] right-[5%] h-[2px] bg-green-500 border-dashed"></div>
                  </div>
                </div>
              </div>
              
              {/* Chart Labels */}
              <div className="absolute bottom-2 left-0 right-0 flex justify-between px-8 text-xs text-gray-500 dark:text-gray-400">
                <span>يناير</span>
                <span>فبراير</span>
                <span>مارس</span>
                <span>أبريل</span>
                <span>مايو</span>
                <span>يونيو</span>
                <span>يوليو</span>
                <span>أغسطس</span>
                <span>سبتمبر</span>
              </div>
              
              <div className="absolute left-2 top-0 bottom-0 flex flex-col justify-between py-8 text-xs text-gray-500 dark:text-gray-400">
                <span>10 د</span>
                <span>20 د</span>
                <span>30 د</span>
                <span>40 د</span>
              </div>
            </div>
            
            {/* Delivery Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">متوسط وقت التوصيل</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">18 دقيقة</div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse text-green-600 text-sm mt-1">
                  <TrendingDown className="w-4 h-4" />
                  <span>-12.5%</span>
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">التوصيل في الوقت المحدد</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">92%</div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse text-green-600 text-sm mt-1">
                  <TrendingUp className="w-4 h-4" />
                  <span>+3.8%</span>
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">متوسط المسافة</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">4.2 كم</div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse text-gray-600 text-sm mt-1">
                  <Activity className="w-4 h-4" />
                  <span>ثابت</span>
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">الهدف</div>
                <div className="text-2xl font-bold text-gray-800 dark:text-white">15 دقيقة</div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 rtl-font">-3 دقائق للوصول</div>
              </div>
            </div>
          </div>

          {/* Completed Orders & Route Efficiency */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Completed Orders */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 rtl-font">
                الطلبات المكتملة
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-gray-800 dark:text-white rtl-font">إحصائيات الطلبات</h4>
                  <div className="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات: 12,847</div>
                </div>
                
                <div className="space-y-3">
                  {[
                    { label: 'مكتملة بنجاح', count: 11890, percentage: 92.5, color: 'bg-green-500' },
                    { label: 'ملغية من العميل', count: 578, percentage: 4.5, color: 'bg-orange-500' },
                    { label: 'ملغية من المطعم', count: 245, percentage: 1.9, color: 'bg-red-500' },
                    { label: 'مشاكل في التوصيل', count: 134, percentage: 1.1, color: 'bg-yellow-500' }
                  ].map((item, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">{item.label}</span>
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <span className="text-sm font-medium text-gray-800 dark:text-white">{item.count}</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">({item.percentage}%)</span>
                        </div>
                      </div>
                      <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                        <div 
                          className={`h-full ${item.color} rounded-full`}
                          style={{ width: `${item.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Route Efficiency */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 rtl-font">
                كفاءة المسارات
              </h3>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 text-center">
                    <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">متوسط المسافة المقطوعة</div>
                    <div className="text-2xl font-bold text-gray-800 dark:text-white">4.2 كم</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1 rtl-font">لكل طلب</div>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 text-center">
                    <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 rtl-font">كفاءة المسار</div>
                    <div className="text-2xl font-bold text-gray-800 dark:text-white">87%</div>
                    <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse text-green-600 text-sm mt-1">
                      <TrendingUp className="w-4 h-4" />
                      <span>+5.2%</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-800 dark:text-white mb-3 rtl-font">أكثر المناطق كفاءة</h4>
                  <div className="space-y-3">
                    {[
                      { area: 'المعاريف، الدار البيضاء', efficiency: 92, deliveryTime: 15 },
                      { area: 'أكدال، الرباط', efficiency: 89, deliveryTime: 17 },
                      { area: 'جليز، مراكش', efficiency: 85, deliveryTime: 18 },
                      { area: 'الحي المحمدي، الدار البيضاء', efficiency: 83, deliveryTime: 20 }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <MapPin className="w-4 h-4 text-primary" />
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">{item.area}</span>
                        </div>
                        <div className="flex items-center space-x-4 rtl:space-x-reverse">
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Target className="w-4 h-4 text-green-600" />
                            <span className="text-sm font-medium">{item.efficiency}%</span>
                          </div>
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Clock className="w-4 h-4 text-blue-600" />
                            <span className="text-sm font-medium">{item.deliveryTime} د</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Driver Ratings & Complaints */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 rtl-font">
              التقييمات والشكاوى
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Rating Distribution */}
              <div className="md:col-span-2">
                <h4 className="font-semibold text-gray-800 dark:text-white mb-4 rtl-font">توزيع التقييمات</h4>
                <div className="space-y-3">
                  {[
                    { stars: 5, percentage: 68, count: 8732 },
                    { stars: 4, percentage: 22, count: 2826 },
                    { stars: 3, percentage: 7, count: 899 },
                    { stars: 2, percentage: 2, count: 257 },
                    { stars: 1, percentage: 1, count: 128 }
                  ].map((item) => (
                    <div key={item.stars} className="flex items-center space-x-4 rtl:space-x-reverse">
                      <div className="flex items-center space-x-1 rtl:space-x-reverse w-20">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{item.stars}</span>
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      </div>
                      <div className="flex-1">
                        <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-yellow-400 rounded-full"
                            style={{ width: `${item.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="w-20 text-right">
                        <span className="text-sm text-gray-600 dark:text-gray-400">{item.percentage}%</span>
                      </div>
                      <div className="w-20 text-right">
                        <span className="text-sm text-gray-600 dark:text-gray-400">{item.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <ThumbsUp className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-green-700 dark:text-green-400 rtl-font">متوسط التقييم: 4.5/5</span>
                  </div>
                </div>
              </div>
              
              {/* Complaints */}
              <div>
                <h4 className="font-semibold text-gray-800 dark:text-white mb-4 rtl-font">أنواع الشكاوى</h4>
                <div className="space-y-3">
                  {[
                    { type: 'تأخير في التوصيل', percentage: 45, count: 215 },
                    { type: 'سلوك السائق', percentage: 25, count: 120 },
                    { type: 'حالة الطعام', percentage: 20, count: 96 },
                    { type: 'خطأ في الطلب', percentage: 10, count: 48 }
                  ].map((item, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">{item.type}</span>
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <span className="text-sm font-medium text-gray-800 dark:text-white">{item.count}</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">({item.percentage}%)</span>
                        </div>
                      </div>
                      <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-red-500 rounded-full"
                          style={{ width: `${item.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Percent className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-700 dark:text-blue-400 rtl-font">معدل الشكاوى: 3.8%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsReports;