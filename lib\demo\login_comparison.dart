import 'package:flutter/material.dart';
import '../constants/driver_themes.dart';
import '../screens/auth/login_screen.dart';

/// عرض مقارنة بين الوضع الفاتح والمظلم لصفحة تسجيل الدخول
class LoginComparisonScreen extends StatelessWidget {
  const LoginComparisonScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Login Screen Comparison',
      debugShowCheckedModeBanner: false,
      theme: DriverThemes.lightTheme,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Login Screen - Light vs Dark'),
          backgroundColor: Colors.grey[100],
          foregroundColor: Colors.black87,
          elevation: 1,
        ),
        backgroundColor: Colors.grey[100],
        body: LayoutBuilder(
          builder: (context, constraints) {
            // للشاشات الكبيرة: عرض جنباً إلى جنب
            if (constraints.maxWidth > 800) {
              return Row(
                children: [
                  // الوضع الفاتح
                  Expanded(
                    child: _buildModeContainer(
                      title: 'Light Mode',
                      icon: Icons.light_mode,
                      color: Colors.orange,
                      child: Theme(
                        data: DriverThemes.lightTheme,
                        child: const LoginScreen(),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // الوضع المظلم
                  Expanded(
                    child: _buildModeContainer(
                      title: 'Dark Mode',
                      icon: Icons.dark_mode,
                      color: Colors.blue,
                      child: Theme(
                        data: DriverThemes.darkTheme,
                        child: const LoginScreen(),
                      ),
                    ),
                  ),
                ],
              );
            } 
            // للشاشات الصغيرة: عرض عمودي مع تبديل
            else {
              return const _MobileComparison();
            }
          },
        ),
      ),
    );
  }

  Widget _buildModeContainer({
    required String title,
    required IconData icon,
    required Color color,
    required Widget child,
  }) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // عنوان الوضع
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              border: Border.all(color: color.withOpacity(0.3)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          
          // محتوى الشاشة
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              child: child,
            ),
          ),
        ],
      ),
    );
  }
}

/// عرض للشاشات الصغيرة مع إمكانية التبديل
class _MobileComparison extends StatefulWidget {
  const _MobileComparison();

  @override
  State<_MobileComparison> createState() => _MobileComparisonState();
}

class _MobileComparisonState extends State<_MobileComparison> {
  bool _isDarkMode = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // مفتاح التبديل
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    _isDarkMode ? Icons.dark_mode : Icons.light_mode,
                    color: _isDarkMode ? Colors.blue : Colors.orange,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isDarkMode ? 'Dark Mode' : 'Light Mode',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
              Switch(
                value: _isDarkMode,
                onChanged: (value) {
                  setState(() {
                    _isDarkMode = value;
                  });
                },
                activeColor: Colors.blue,
                inactiveThumbColor: Colors.orange,
              ),
            ],
          ),
        ),
        
        // محتوى الشاشة
        Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Theme(
                data: _isDarkMode ? DriverThemes.darkTheme : DriverThemes.lightTheme,
                child: const LoginScreen(),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
      ],
    );
  }
}

/// دالة لتشغيل عرض المقارنة
void runLoginComparison() {
  runApp(const LoginComparisonScreen());
}

/// مثال على الاستخدام:
/// 
/// ```dart
/// // في main.dart أو أي ملف آخر
/// import 'demo/login_comparison.dart';
/// 
/// void main() {
///   runLoginComparison();
/// }
/// ```
