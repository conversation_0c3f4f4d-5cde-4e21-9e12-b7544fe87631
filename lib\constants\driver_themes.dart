import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'driver_colors.dart';
import 'driver_typography.dart';

/// نظام الثيمات المتكامل لتطبيق السائق
/// يدعم الوضع الفاتح والمظلم مع تحسينات خاصة للقيادة
class DriverThemes {
  
  // ========== الثيم الفاتح ==========
  
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    
    // نظام الألوان
    colorScheme: DriverColors.lightColorScheme,
    
    // نظام الخطوط
    textTheme: DriverTypography.lightTextTheme,
    
    // شريط التطبيق
    appBarTheme: AppBarTheme(
      backgroundColor: DriverColors.lightColorScheme.surface,
      foregroundColor: DriverColors.lightColorScheme.onSurface,
      elevation: 0,
      scrolledUnderElevation: 1,
      shadowColor: DriverColors.lightColorScheme.shadow,
      surfaceTintColor: DriverColors.lightColorScheme.surfaceTint,
      titleTextStyle: DriverTypography.lightTextTheme.headlineSmall,
      toolbarTextStyle: DriverTypography.lightTextTheme.bodyMedium,
      iconTheme: IconThemeData(
        color: DriverColors.lightColorScheme.onSurface,
        size: 24,
      ),
      actionsIconTheme: IconThemeData(
        color: DriverColors.lightColorScheme.onSurface,
        size: 24,
      ),
      systemOverlayStyle: SystemUiOverlayStyle.dark,
    ),
    
    // الأزرار المرفوعة
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: DriverColors.lightColorScheme.primary,
        foregroundColor: DriverColors.lightColorScheme.onPrimary,
        disabledBackgroundColor: DriverColors.lightColorScheme.outline,
        disabledForegroundColor: DriverColors.lightColorScheme.onSurface.withValues(alpha: 0.38),
        elevation: 2,
        shadowColor: DriverColors.lightColorScheme.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: DriverTypography.lightTextTheme.labelLarge,
        minimumSize: const Size(64, 48),
      ),
    ),
    
    // الأزرار المحددة
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: DriverColors.lightColorScheme.primary,
        disabledForegroundColor: DriverColors.lightColorScheme.onSurface.withValues(alpha: 0.38),
        side: BorderSide(
          color: DriverColors.lightColorScheme.outline,
          width: 1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: DriverTypography.lightTextTheme.labelLarge,
        minimumSize: const Size(64, 48),
      ),
    ),
    
    // الأزرار النصية
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: DriverColors.lightColorScheme.primary,
        disabledForegroundColor: DriverColors.lightColorScheme.onSurface.withValues(alpha: 0.38),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        textStyle: DriverTypography.lightTextTheme.labelLarge,
        minimumSize: const Size(64, 36),
      ),
    ),
    
    // الكروت
    cardTheme: CardTheme(
      color: DriverColors.lightColorScheme.surface,
      shadowColor: DriverColors.lightColorScheme.shadow,
      surfaceTintColor: DriverColors.lightColorScheme.surfaceTint,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.all(8),
    ),
    
    // حقول الإدخال
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: DriverColors.lightColorScheme.surfaceContainerHighest,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.lightColorScheme.outline,
          width: 1,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.lightColorScheme.outline,
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.lightColorScheme.primary,
          width: 2,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.lightColorScheme.error,
          width: 1,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.lightColorScheme.error,
          width: 2,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      hintStyle: DriverTypography.lightTextTheme.bodyMedium?.copyWith(
        color: DriverColors.lightColorScheme.onSurfaceVariant,
      ),
      labelStyle: DriverTypography.lightTextTheme.bodyMedium?.copyWith(
        color: DriverColors.lightColorScheme.onSurfaceVariant,
      ),
    ),
    
    // الأيقونات
    iconTheme: IconThemeData(
      color: DriverColors.lightColorScheme.onSurface,
      size: 24,
    ),
    
    // التنقل السفلي
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: DriverColors.lightColorScheme.surface,
      selectedItemColor: DriverColors.lightColorScheme.primary,
      unselectedItemColor: DriverColors.lightColorScheme.onSurfaceVariant,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: DriverTypography.lightTextTheme.labelSmall,
      unselectedLabelStyle: DriverTypography.lightTextTheme.labelSmall,
    ),
    
    // الحوارات
    dialogTheme: DialogTheme(
      backgroundColor: DriverColors.lightColorScheme.surface,
      surfaceTintColor: DriverColors.lightColorScheme.surfaceTint,
      elevation: 6,
      shadowColor: DriverColors.lightColorScheme.shadow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      titleTextStyle: DriverTypography.lightTextTheme.headlineSmall,
      contentTextStyle: DriverTypography.lightTextTheme.bodyMedium,
    ),
    
    // الـ Snackbar
    snackBarTheme: SnackBarThemeData(
      backgroundColor: DriverColors.lightColorScheme.inverseSurface,
      contentTextStyle: DriverTypography.lightTextTheme.bodyMedium?.copyWith(
        color: DriverColors.lightColorScheme.onInverseSurface,
      ),
      actionTextColor: DriverColors.lightColorScheme.inversePrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      behavior: SnackBarBehavior.floating,
      elevation: 6,
    ),
    
    // الـ Chip
    chipTheme: ChipThemeData(
      backgroundColor: DriverColors.lightColorScheme.surfaceContainerHighest,
      selectedColor: DriverColors.lightColorScheme.primaryContainer,
      disabledColor: DriverColors.lightColorScheme.onSurface.withValues(alpha: 0.12),
      labelStyle: DriverTypography.lightTextTheme.labelMedium,
      secondaryLabelStyle: DriverTypography.lightTextTheme.labelMedium,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 0,
      pressElevation: 1,
    ),
    
    // الـ Switch
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return DriverColors.lightColorScheme.onPrimary;
        }
        return DriverColors.lightColorScheme.outline;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return DriverColors.lightColorScheme.primary;
        }
        return DriverColors.lightColorScheme.surfaceContainerHighest;
      }),
    ),
    
    // الـ Slider
    sliderTheme: SliderThemeData(
      activeTrackColor: DriverColors.lightColorScheme.primary,
      inactiveTrackColor: DriverColors.lightColorScheme.surfaceContainerHighest,
      thumbColor: DriverColors.lightColorScheme.primary,
      overlayColor: DriverColors.lightColorScheme.primary.withValues(alpha: 0.12),
      valueIndicatorColor: DriverColors.lightColorScheme.primary,
      valueIndicatorTextStyle: DriverTypography.lightTextTheme.labelMedium?.copyWith(
        color: DriverColors.lightColorScheme.onPrimary,
      ),
    ),
    
    // الـ Progress Indicator
    progressIndicatorTheme: ProgressIndicatorThemeData(
      color: DriverColors.lightColorScheme.primary,
      linearTrackColor: DriverColors.lightColorScheme.surfaceContainerHighest,
      circularTrackColor: DriverColors.lightColorScheme.surfaceContainerHighest,
    ),
    
    // الـ Divider
    dividerTheme: DividerThemeData(
      color: DriverColors.lightColorScheme.outline,
      thickness: 1,
      space: 1,
    ),
    
    // الـ ListTile
    listTileTheme: ListTileThemeData(
      tileColor: Colors.transparent,
      selectedTileColor: DriverColors.lightColorScheme.primaryContainer.withValues(alpha: 0.12),
      iconColor: DriverColors.lightColorScheme.onSurfaceVariant,
      textColor: DriverColors.lightColorScheme.onSurface,
      titleTextStyle: DriverTypography.lightTextTheme.bodyLarge,
      subtitleTextStyle: DriverTypography.lightTextTheme.bodyMedium,
      leadingAndTrailingTextStyle: DriverTypography.lightTextTheme.labelMedium,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    ),
  );

  // ========== الثيم المظلم ==========

  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,

    // نظام الألوان
    colorScheme: DriverColors.darkColorScheme,

    // نظام الخطوط
    textTheme: DriverTypography.darkTextTheme,

    // شريط التطبيق
    appBarTheme: AppBarTheme(
      backgroundColor: DriverColors.darkColorScheme.surface,
      foregroundColor: DriverColors.darkColorScheme.onSurface,
      elevation: 0,
      scrolledUnderElevation: 1,
      shadowColor: DriverColors.darkColorScheme.shadow,
      surfaceTintColor: DriverColors.darkColorScheme.surfaceTint,
      titleTextStyle: DriverTypography.darkTextTheme.headlineSmall,
      toolbarTextStyle: DriverTypography.darkTextTheme.bodyMedium,
      iconTheme: IconThemeData(
        color: DriverColors.darkColorScheme.onSurface,
        size: 24,
      ),
      actionsIconTheme: IconThemeData(
        color: DriverColors.darkColorScheme.onSurface,
        size: 24,
      ),
      systemOverlayStyle: SystemUiOverlayStyle.light,
    ),

    // الأزرار المرفوعة
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: DriverColors.darkColorScheme.primary,
        foregroundColor: DriverColors.darkColorScheme.onPrimary,
        disabledBackgroundColor: DriverColors.darkColorScheme.outline,
        disabledForegroundColor: DriverColors.darkColorScheme.onSurface.withValues(alpha: 0.38),
        elevation: 2,
        shadowColor: DriverColors.darkColorScheme.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: DriverTypography.darkTextTheme.labelLarge,
        minimumSize: const Size(64, 48),
      ),
    ),

    // الأزرار المحددة
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: DriverColors.darkColorScheme.primary,
        disabledForegroundColor: DriverColors.darkColorScheme.onSurface.withValues(alpha: 0.38),
        side: BorderSide(
          color: DriverColors.darkColorScheme.outline,
          width: 1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: DriverTypography.darkTextTheme.labelLarge,
        minimumSize: const Size(64, 48),
      ),
    ),

    // الأزرار النصية
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: DriverColors.darkColorScheme.primary,
        disabledForegroundColor: DriverColors.darkColorScheme.onSurface.withValues(alpha: 0.38),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        textStyle: DriverTypography.darkTextTheme.labelLarge,
        minimumSize: const Size(64, 36),
      ),
    ),

    // الكروت
    cardTheme: CardTheme(
      color: DriverColors.darkColorScheme.surface,
      shadowColor: DriverColors.darkColorScheme.shadow,
      surfaceTintColor: DriverColors.darkColorScheme.surfaceTint,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.all(8),
    ),

    // حقول الإدخال
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: DriverColors.darkColorScheme.surfaceContainerHighest,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.darkColorScheme.outline,
          width: 1,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.darkColorScheme.outline,
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.darkColorScheme.primary,
          width: 2,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.darkColorScheme.error,
          width: 1,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: DriverColors.darkColorScheme.error,
          width: 2,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      hintStyle: DriverTypography.darkTextTheme.bodyMedium?.copyWith(
        color: DriverColors.darkColorScheme.onSurfaceVariant,
      ),
      labelStyle: DriverTypography.darkTextTheme.bodyMedium?.copyWith(
        color: DriverColors.darkColorScheme.onSurfaceVariant,
      ),
    ),

    // الأيقونات
    iconTheme: IconThemeData(
      color: DriverColors.darkColorScheme.onSurface,
      size: 24,
    ),

    // التنقل السفلي
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: DriverColors.darkColorScheme.surface,
      selectedItemColor: DriverColors.darkColorScheme.primary,
      unselectedItemColor: DriverColors.darkColorScheme.onSurfaceVariant,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: DriverTypography.darkTextTheme.labelSmall,
      unselectedLabelStyle: DriverTypography.darkTextTheme.labelSmall,
    ),

    // الحوارات
    dialogTheme: DialogTheme(
      backgroundColor: DriverColors.darkColorScheme.surface,
      surfaceTintColor: DriverColors.darkColorScheme.surfaceTint,
      elevation: 6,
      shadowColor: DriverColors.darkColorScheme.shadow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      titleTextStyle: DriverTypography.darkTextTheme.headlineSmall,
      contentTextStyle: DriverTypography.darkTextTheme.bodyMedium,
    ),

    // الـ Snackbar
    snackBarTheme: SnackBarThemeData(
      backgroundColor: DriverColors.darkColorScheme.inverseSurface,
      contentTextStyle: DriverTypography.darkTextTheme.bodyMedium?.copyWith(
        color: DriverColors.darkColorScheme.onInverseSurface,
      ),
      actionTextColor: DriverColors.darkColorScheme.inversePrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      behavior: SnackBarBehavior.floating,
      elevation: 6,
    ),

    // الـ Chip
    chipTheme: ChipThemeData(
      backgroundColor: DriverColors.darkColorScheme.surfaceContainerHighest,
      selectedColor: DriverColors.darkColorScheme.primaryContainer,
      disabledColor: DriverColors.darkColorScheme.onSurface.withValues(alpha: 0.12),
      labelStyle: DriverTypography.darkTextTheme.labelMedium,
      secondaryLabelStyle: DriverTypography.darkTextTheme.labelMedium,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 0,
      pressElevation: 1,
    ),

    // الـ Switch
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return DriverColors.darkColorScheme.onPrimary;
        }
        return DriverColors.darkColorScheme.outline;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return DriverColors.darkColorScheme.primary;
        }
        return DriverColors.darkColorScheme.surfaceContainerHighest;
      }),
    ),

    // الـ Slider
    sliderTheme: SliderThemeData(
      activeTrackColor: DriverColors.darkColorScheme.primary,
      inactiveTrackColor: DriverColors.darkColorScheme.surfaceContainerHighest,
      thumbColor: DriverColors.darkColorScheme.primary,
      overlayColor: DriverColors.darkColorScheme.primary.withValues(alpha: 0.12),
      valueIndicatorColor: DriverColors.darkColorScheme.primary,
      valueIndicatorTextStyle: DriverTypography.darkTextTheme.labelMedium?.copyWith(
        color: DriverColors.darkColorScheme.onPrimary,
      ),
    ),

    // الـ Progress Indicator
    progressIndicatorTheme: ProgressIndicatorThemeData(
      color: DriverColors.darkColorScheme.primary,
      linearTrackColor: DriverColors.darkColorScheme.surfaceContainerHighest,
      circularTrackColor: DriverColors.darkColorScheme.surfaceContainerHighest,
    ),

    // الـ Divider
    dividerTheme: DividerThemeData(
      color: DriverColors.darkColorScheme.outline,
      thickness: 1,
      space: 1,
    ),

    // الـ ListTile
    listTileTheme: ListTileThemeData(
      tileColor: Colors.transparent,
      selectedTileColor: DriverColors.darkColorScheme.primaryContainer.withValues(alpha: 0.12),
      iconColor: DriverColors.darkColorScheme.onSurfaceVariant,
      textColor: DriverColors.darkColorScheme.onSurface,
      titleTextStyle: DriverTypography.darkTextTheme.bodyLarge,
      subtitleTextStyle: DriverTypography.darkTextTheme.bodyMedium,
      leadingAndTrailingTextStyle: DriverTypography.darkTextTheme.labelMedium,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    ),
  );

  // ========== الثيم الليلي للقيادة ==========

  static ThemeData get nightDrivingTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,

    // ألوان محسنة للقيادة الليلية
    colorScheme: ColorScheme.dark(
      brightness: Brightness.dark,
      primary: DriverColors.nightModeAccent,
      onPrimary: DriverColors.nightModeBackground,
      secondary: DriverColors.primaryGreenLight,
      onSecondary: DriverColors.nightModeBackground,
      surface: DriverColors.nightModeSurface,
      onSurface: DriverColors.nightModeText,

      error: DriverColors.highContrastRed,
      onError: DriverColors.nightModeBackground,
    ),

    // خطوط محسنة للقراءة الليلية
    textTheme: DriverTypography.darkTextTheme.copyWith(
      displayLarge: DriverTypography.darkTextTheme.displayLarge?.copyWith(
        color: DriverColors.nightModeText,
        fontWeight: FontWeight.w600,
      ),
      headlineLarge: DriverTypography.darkTextTheme.headlineLarge?.copyWith(
        color: DriverColors.nightModeText,
        fontWeight: FontWeight.w600,
      ),
      bodyLarge: DriverTypography.darkTextTheme.bodyLarge?.copyWith(
        color: DriverColors.nightModeText,
        fontSize: 18, // أكبر للقراءة أثناء القيادة
      ),
    ),

    // شريط تطبيق محسن للقيادة الليلية
    appBarTheme: AppBarTheme(
      backgroundColor: DriverColors.nightModeBackground,
      foregroundColor: DriverColors.nightModeText,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle.light,
    ),

    // أزرار بتباين عالي
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: DriverColors.nightModeAccent,
        foregroundColor: DriverColors.nightModeBackground,
        elevation: 4,
        shadowColor: Colors.black54,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        textStyle: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        minimumSize: const Size(120, 56),
      ),
    ),
  );

  // ========== دوال مساعدة ==========

  /// الحصول على الثيم حسب الوضع
  static ThemeData getTheme(ThemeMode mode, {bool isNightDriving = false}) {
    if (isNightDriving) {
      return nightDrivingTheme;
    }

    switch (mode) {
      case ThemeMode.light:
        return lightTheme;
      case ThemeMode.dark:
        return darkTheme;
      case ThemeMode.system:
        return WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark
            ? darkTheme
            : lightTheme;
    }
  }

  /// الحصول على لون النص المناسب للخلفية
  static Color getTextColorForBackground(Color backgroundColor) {
    return backgroundColor.computeLuminance() > 0.5
        ? Colors.black87
        : Colors.white;
  }

  /// الحصول على لون مع تباين عالي
  static Color getHighContrastColor(Color baseColor, bool isDark) {
    if (isDark) {
      return baseColor.computeLuminance() < 0.5
          ? baseColor
          : Color.fromARGB(
              (baseColor.a * 255).round(),
              (baseColor.r * 255 * 0.7).round(),
              (baseColor.g * 255 * 0.7).round(),
              (baseColor.b * 255 * 0.7).round(),
            );
    } else {
      return baseColor.computeLuminance() > 0.5
          ? baseColor
          : Color.fromARGB(
              (baseColor.a * 255).round(),
              (baseColor.r * 255 * 1.3).clamp(0, 255).round(),
              (baseColor.g * 255 * 1.3).clamp(0, 255).round(),
              (baseColor.b * 255 * 1.3).clamp(0, 255).round(),
            );
    }
  }
}
