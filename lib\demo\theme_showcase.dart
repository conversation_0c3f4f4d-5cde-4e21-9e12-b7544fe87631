import 'package:flutter/material.dart';
import '../constants/driver_colors.dart';
import '../constants/driver_typography.dart';
import '../constants/driver_themes.dart';

/// شاشة عرض نظام الألوان والخطوط لتطبيق السائق
class ThemeShowcaseScreen extends StatefulWidget {
  const ThemeShowcaseScreen({super.key});

  @override
  State<ThemeShowcaseScreen> createState() => _ThemeShowcaseScreenState();
}

class _ThemeShowcaseScreenState extends State<ThemeShowcaseScreen> {
  ThemeMode _themeMode = ThemeMode.light;
  bool _isNightDriving = false;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Driver Theme Showcase',
      theme: DriverThemes.getTheme(_themeMode, isNightDriving: _isNightDriving),
      home: Scaffold(
        appBar: AppBar(
          title: const Text('نظام الألوان والخطوط'),
          actions: [
            PopupMenuButton<ThemeMode>(
              icon: const Icon(Icons.palette),
              onSelected: (mode) {
                setState(() {
                  _themeMode = mode;
                  _isNightDriving = false;
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: ThemeMode.light,
                  child: Text('الوضع الفاتح'),
                ),
                const PopupMenuItem(
                  value: ThemeMode.dark,
                  child: Text('الوضع المظلم'),
                ),
              ],
            ),
            IconButton(
              icon: Icon(_isNightDriving ? Icons.nights_stay : Icons.wb_sunny),
              onPressed: () {
                setState(() {
                  _isNightDriving = !_isNightDriving;
                  if (_isNightDriving) _themeMode = ThemeMode.dark;
                });
              },
              tooltip: _isNightDriving ? 'إيقاف الوضع الليلي' : 'تفعيل الوضع الليلي',
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الوضع الحالي
              _buildCurrentModeInfo(),
              
              const SizedBox(height: 24),
              
              // عرض الألوان الأساسية
              _buildColorsSection(),
              
              const SizedBox(height: 24),
              
              // عرض الخطوط
              _buildTypographySection(),
              
              const SizedBox(height: 24),
              
              // عرض المكونات
              _buildComponentsSection(),
              
              const SizedBox(height: 24),
              
              // عرض ألوان حالة الطلبات
              _buildOrderStatusSection(),
              
              const SizedBox(height: 24),
              
              // عرض ألوان السائق
              _buildDriverStatusSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentModeInfo() {
    String modeText = '';
    if (_isNightDriving) {
      modeText = 'الوضع الليلي للقيادة';
    } else {
      switch (_themeMode) {
        case ThemeMode.light:
          modeText = 'الوضع الفاتح';
          break;
        case ThemeMode.dark:
          modeText = 'الوضع المظلم';
          break;
        case ThemeMode.system:
          modeText = 'وضع النظام';
          break;
      }
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              _isNightDriving 
                  ? Icons.nights_stay 
                  : (_themeMode == ThemeMode.light ? Icons.wb_sunny : Icons.dark_mode),
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Text(
              'الوضع الحالي: $modeText',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الألوان الأساسية',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildColorCard('الأخضر الرئيسي', DriverColors.primaryGreen),
            _buildColorCard('الأزرق المعلوماتي', DriverColors.infoBlue),
            _buildColorCard('البرتقالي التحذيري', DriverColors.warningOrange),
            _buildColorCard('الأحمر للأخطاء', DriverColors.errorRed),
            _buildColorCard('الرمادي المحايد', DriverColors.neutralGray),
          ],
        ),
      ],
    );
  }

  Widget _buildColorCard(String name, Color color) {
    return Container(
      width: 120,
      height: 80,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Text(
              name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypographySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نظام الخطوط',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'العناوين الكبيرة',
                  style: Theme.of(context).textTheme.displayMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'عناوين الصفحات',
                  style: Theme.of(context).textTheme.headlineLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'عناوين الأقسام',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'النص الأساسي للمحتوى والوصف',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'النص الثانوي والتفاصيل',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'التسميات والنصوص الصغيرة',
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                const SizedBox(height: 16),
                Text(
                  'أرقام كبيرة: 1,250.75 د.م',
                  style: DriverTypography.bigNumberStyle.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'معرف: ORD-2024-001',
                  style: DriverTypography.identifierStyle.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildComponentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المكونات',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ElevatedButton(
              onPressed: () {},
              child: const Text('زر مرفوع'),
            ),
            OutlinedButton(
              onPressed: () {},
              child: const Text('زر محدد'),
            ),
            TextButton(
              onPressed: () {},
              child: const Text('زر نصي'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: ListTile(
            leading: const Icon(Icons.delivery_dining),
            title: const Text('عنصر قائمة'),
            subtitle: const Text('وصف العنصر'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {},
          ),
        ),
      ],
    );
  }

  Widget _buildOrderStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ألوان حالة الطلبات',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildStatusChip('في الانتظار', DriverColors.orderPending),
            _buildStatusChip('مقبول', DriverColors.orderAccepted),
            _buildStatusChip('قيد التنفيذ', DriverColors.orderInProgress),
            _buildStatusChip('مكتمل', DriverColors.orderCompleted),
            _buildStatusChip('ملغي', DriverColors.orderCancelled),
          ],
        ),
      ],
    );
  }

  Widget _buildDriverStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ألوان حالة السائق',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildStatusChip('متصل', DriverColors.driverOnline),
            _buildStatusChip('غير متصل', DriverColors.driverOffline),
            _buildStatusChip('مشغول', DriverColors.driverBusy),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusChip(String label, Color color) {
    return Chip(
      label: Text(
        label,
        style: TextStyle(
          color: DriverColors.getContrastColor(color),
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: color,
      side: BorderSide.none,
    );
  }
}
