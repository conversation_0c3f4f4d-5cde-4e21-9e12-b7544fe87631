import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Document status enum
enum DocumentStatus { approved, pending, rejected, notUploaded }

/// Document item model
class DocumentItem {
  final String title;
  final String titleAr;
  final DocumentStatus status;
  final DateTime? expiryDate;
  final bool isRequired;

  DocumentItem({
    required this.title,
    required this.titleAr,
    required this.status,
    this.expiryDate,
    required this.isRequired,
  });
}

/// Manage Documents screen for driver
class ManageDocumentsScreen extends StatefulWidget {
  const ManageDocumentsScreen({super.key});

  @override
  State<ManageDocumentsScreen> createState() => _ManageDocumentsScreenState();
}

class _ManageDocumentsScreenState extends State<ManageDocumentsScreen> {
  final List<DocumentItem> _documents = [
    DocumentItem(
      title: 'Driver License',
      titleAr: 'رخصة القيادة',
      status: DocumentStatus.approved,
      expiryDate: DateTime(2025, 12, 31),
      isRequired: true,
    ),
    DocumentItem(
      title: 'National ID',
      titleAr: 'الهوية الوطنية',
      status: DocumentStatus.approved,
      expiryDate: DateTime(2030, 5, 15),
      isRequired: true,
    ),
    DocumentItem(
      title: 'Vehicle Registration',
      titleAr: 'تسجيل المركبة',
      status: DocumentStatus.pending,
      expiryDate: DateTime(2024, 8, 20),
      isRequired: true,
    ),
    DocumentItem(
      title: 'Insurance Certificate',
      titleAr: 'شهادة التأمين',
      status: DocumentStatus.rejected,
      expiryDate: DateTime(2024, 6, 10),
      isRequired: true,
    ),
    DocumentItem(
      title: 'Medical Certificate',
      titleAr: 'الشهادة الطبية',
      status: DocumentStatus.notUploaded,
      expiryDate: null,
      isRequired: false,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.manageDocuments,
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header Info
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    const Icon(
                      Icons.description,
                      size: 50,
                      color: Color(0xFF11B96F),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      languageProvider.getText('Document Status', 'حالة الوثائق'),
                      style: DriverTypography.getContextualStyle(
                        context,
                        fontSize: DriverTypography.titleMedium,
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatusItem(
                          languageProvider.getText('Required', 'مطلوبة'),
                          '4',
                          Colors.blue,
                          isDark,
                        ),
                        _buildStatusItem(
                          languageProvider.getText('Approved', 'موافق عليها'),
                          '2',
                          const Color(0xFF11B96F),
                          isDark,
                        ),
                        _buildStatusItem(
                          languageProvider.getText('Pending', 'قيد المراجعة'),
                          '1',
                          Colors.orange,
                          isDark,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Documents List
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    languageProvider.getText('Your Documents', 'وثائقك'),
                    style: DriverTypography.getContextualStyle(
                      context,
                      fontSize: DriverTypography.titleMedium,
                      fontWeight: FontWeight.bold,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _documents.length,
                    separatorBuilder: (context, index) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final document = _documents[index];
                      return _buildDocumentCard(document, isDark, languageProvider);
                    },
                  ),
                ],
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String count, Color color, bool isDark) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Text(
              count,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.bodySmall,
            color: isDark ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentCard(DocumentItem document, bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(document.status).withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getStatusColor(document.status).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getDocumentIcon(document.title),
                  color: _getStatusColor(document.status),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            languageProvider.isArabic ? document.titleAr : document.title,
                            style: DriverTypography.getContextualStyle(
                              context,
                              fontSize: DriverTypography.bodyLarge,
                              fontWeight: FontWeight.w600,
                              color: isDark ? Colors.white : Colors.black87,
                            ),
                          ),
                        ),
                        if (document.isRequired)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              languageProvider.getText('Required', 'مطلوبة'),
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: Colors.red,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildStatusChip(document.status, languageProvider),
              const Spacer(),
              if (document.expiryDate != null)
                Text(
                  languageProvider.getText(
                    'Expires: ${_formatDate(document.expiryDate!)}',
                    'تنتهي: ${_formatDate(document.expiryDate!)}'
                  ),
                  style: DriverTypography.getContextualStyle(
                    context,
                    fontSize: DriverTypography.bodySmall,
                    color: _isExpiringSoon(document.expiryDate!) 
                        ? Colors.red 
                        : (isDark ? Colors.grey[400] : Colors.grey[600]),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          _buildDocumentActions(document, isDark, languageProvider),
        ],
      ),
    );
  }

  Widget _buildStatusChip(DocumentStatus status, LanguageProvider languageProvider) {
    String text;
    Color color = _getStatusColor(status);

    switch (status) {
      case DocumentStatus.approved:
        text = languageProvider.getText('Approved', 'موافق عليها');
        break;
      case DocumentStatus.pending:
        text = languageProvider.getText('Pending', 'قيد المراجعة');
        break;
      case DocumentStatus.rejected:
        text = languageProvider.getText('Rejected', 'مرفوضة');
        break;
      case DocumentStatus.notUploaded:
        text = languageProvider.getText('Not Uploaded', 'غير مرفوعة');
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  Widget _buildDocumentActions(DocumentItem document, bool isDark, LanguageProvider languageProvider) {
    return Row(
      children: [
        if (document.status == DocumentStatus.notUploaded || document.status == DocumentStatus.rejected)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _handleUploadDocument(document),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF11B96F),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              icon: const Icon(Icons.upload_file, size: 16),
              label: Text(
                languageProvider.getText('Upload', 'رفع'),
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ),
        if (document.status == DocumentStatus.approved || document.status == DocumentStatus.pending)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _handleViewDocument(document),
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFF11B96F),
                side: const BorderSide(color: Color(0xFF11B96F)),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              icon: const Icon(Icons.visibility, size: 16),
              label: Text(
                languageProvider.getText('View', 'عرض'),
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ),
      ],
    );
  }

  Color _getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.approved:
        return const Color(0xFF11B96F);
      case DocumentStatus.pending:
        return Colors.orange;
      case DocumentStatus.rejected:
        return Colors.red;
      case DocumentStatus.notUploaded:
        return Colors.grey;
    }
  }

  IconData _getDocumentIcon(String title) {
    switch (title.toLowerCase()) {
      case 'driver license':
        return Icons.drive_eta;
      case 'national id':
        return Icons.badge;
      case 'vehicle registration':
        return Icons.directions_car;
      case 'insurance certificate':
        return Icons.security;
      case 'medical certificate':
        return Icons.medical_services;
      default:
        return Icons.description;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  bool _isExpiringSoon(DateTime expiryDate) {
    final now = DateTime.now();
    final difference = expiryDate.difference(now).inDays;
    return difference <= 30;
  }

  void _handleUploadDocument(DocumentItem document) {
    final languageProvider = context.read<LanguageProvider>();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.getText(
            'Upload ${document.title} - Coming Soon',
            'رفع ${document.titleAr} - قريباً'
          )
        ),
      ),
    );
  }

  void _handleViewDocument(DocumentItem document) {
    final languageProvider = context.read<LanguageProvider>();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.getText(
            'View ${document.title} - Coming Soon',
            'عرض ${document.titleAr} - قريباً'
          )
        ),
      ),
    );
  }
}