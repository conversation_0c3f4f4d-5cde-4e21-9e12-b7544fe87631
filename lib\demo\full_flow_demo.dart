import 'package:flutter/material.dart';
import '../constants/driver_themes.dart';
import '../screens/auth/login_screen.dart';

/// تطبيق تجريبي شامل يعرض التدفق الكامل
/// من تسجيل الدخول إلى OTP إلى الشاشة الرئيسية
class FullFlowDemoApp extends StatefulWidget {
  const FullFlowDemoApp({super.key});

  @override
  State<FullFlowDemoApp> createState() => _FullFlowDemoAppState();
}

class _FullFlowDemoAppState extends State<FullFlowDemoApp> {
  ThemeMode _themeMode = ThemeMode.system;
  bool _isNightDriving = false;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wasslti Partner - Full Flow Demo',
      debugShowCheckedModeBanner: false,
      
      // استخدام نظام الثيمات المخصص
      theme: DriverThemes.getTheme(ThemeMode.light, isNightDriving: _isNightDriving),
      darkTheme: DriverThemes.getTheme(ThemeMode.dark, isNightDriving: _isNightDriving),
      themeMode: _themeMode,
      
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Full Flow Demo'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: [
            // زر تبديل الوضع
            PopupMenuButton<String>(
              icon: const Icon(Icons.palette),
              onSelected: (value) {
                setState(() {
                  switch (value) {
                    case 'light':
                      _themeMode = ThemeMode.light;
                      _isNightDriving = false;
                      break;
                    case 'dark':
                      _themeMode = ThemeMode.dark;
                      _isNightDriving = false;
                      break;
                    case 'system':
                      _themeMode = ThemeMode.system;
                      _isNightDriving = false;
                      break;
                    case 'night':
                      _themeMode = ThemeMode.dark;
                      _isNightDriving = true;
                      break;
                  }
                });
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'light',
                  child: Row(
                    children: [
                      Icon(Icons.light_mode, size: 20),
                      SizedBox(width: 8),
                      Text('Light Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'dark',
                  child: Row(
                    children: [
                      Icon(Icons.dark_mode, size: 20),
                      SizedBox(width: 8),
                      Text('Dark Mode'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'system',
                  child: Row(
                    children: [
                      Icon(Icons.settings, size: 20),
                      SizedBox(width: 8),
                      Text('System'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'night',
                  child: Row(
                    children: [
                      Icon(Icons.nights_stay, size: 20),
                      SizedBox(width: 8),
                      Text('Night Driving'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: const LoginScreen(),
        
        // معلومات التدفق
        bottomNavigationBar: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getCurrentModeIcon(),
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getCurrentModeText(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'التدفق الكامل: تسجيل الدخول → OTP → الشاشة الرئيسية',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: const Color(0xFF11B96F),
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCurrentModeIcon() {
    if (_isNightDriving) return Icons.nights_stay;
    
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.settings;
    }
  }

  String _getCurrentModeText() {
    if (_isNightDriving) return 'Night Driving Mode';
    
    switch (_themeMode) {
      case ThemeMode.light:
        return 'Light Mode';
      case ThemeMode.dark:
        return 'Dark Mode';
      case ThemeMode.system:
        return 'System Mode';
    }
  }
}

/// دالة لتشغيل التطبيق التجريبي الشامل
void runFullFlowDemo() {
  runApp(const FullFlowDemoApp());
}

/// مثال على الاستخدام:
/// 
/// ```dart
/// // في main.dart أو أي ملف آخر
/// import 'demo/full_flow_demo.dart';
/// 
/// void main() {
///   runFullFlowDemo();
/// }
/// ```
/// 
/// ### خطوات التجربة:
/// 1. أدخل أي رقم هاتف وكلمة مرور
/// 2. اضغط على "Log In"
/// 3. ستنتقل إلى صفحة OTP
/// 4. أدخل أي 6 أرقام
/// 5. اضغط على "Verify"
/// 6. ستنتقل إلى الشاشة الرئيسية مع الخريطة
/// 7. جرب الأزرار المختلفة والطلب الوارد (يظهر بعد 3 ثوان)
