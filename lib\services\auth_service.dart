import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/driver.dart';
import '../models/driver_location.dart';
import 'storage_service.dart';
import 'api_config.dart';

/// خدمة المصادقة للتعامل مع تسجيل الدخول والخروج
class AuthService {
  static const String _tag = 'AuthService';
  
  // ==================== تسجيل الدخول ====================
  
  /// تسجيل الدخول بالهاتف وكلمة المرور
  static Future<AuthResult> login({
    required String phone,
    required String password,
  }) async {
    try {
      _log('محاولة تسجيل الدخول للهاتف: $phone');
      
      // في الوضع التجريبي، استخدم بيانات وهمية
      if (ApiConfig.useMockData) {
        return await _mockLogin(phone, password);
      }
      
      // طلب حقيقي للخادم
      final url = ApiConfig.buildUrl(ApiConfig.loginEndpoint);
      final response = await http.post(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
        body: jsonEncode({
          'phone': phone,
          'password': password,
        }),
      ).timeout(
        Duration(seconds: ApiConfig.connectionTimeout),
      );
      
      _log('استجابة تسجيل الدخول: ${response.statusCode}');
      
      if (ApiConfig.isSuccessResponse(response.statusCode)) {
        final data = jsonDecode(response.body);
        
        // حفظ رقم الهاتف للمرحلة التالية
        await StorageService.savePhone(phone);
        
        return AuthResult.success(
          message: data['message'] ?? 'تم إرسال رمز التحقق',
          data: data,
        );
      } else {
        final error = jsonDecode(response.body);
        return AuthResult.failure(
          message: error['message'] ?? 'خطأ في تسجيل الدخول',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      _log('خطأ في تسجيل الدخول: $e');
      return AuthResult.failure(
        message: 'خطأ في الاتصال بالخادم',
        error: e,
      );
    }
  }
  
  /// تأكيد رمز OTP
  static Future<AuthResult> verifyOtp({
    required String phone,
    required String otp,
  }) async {
    try {
      _log('تأكيد OTP للهاتف: $phone');
      
      // في الوضع التجريبي، استخدم بيانات وهمية
      if (ApiConfig.useMockData) {
        return await _mockVerifyOtp(phone, otp);
      }
      
      // طلب حقيقي للخادم
      final url = ApiConfig.buildUrl(ApiConfig.verifyOtpEndpoint);
      final response = await http.post(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
        body: jsonEncode({
          'phone': phone,
          'otp': otp,
        }),
      ).timeout(
        Duration(seconds: ApiConfig.connectionTimeout),
      );
      
      _log('استجابة تأكيد OTP: ${response.statusCode}');
      
      if (ApiConfig.isSuccessResponse(response.statusCode)) {
        final data = jsonDecode(response.body);
        
        // استخراج البيانات
        final token = data['token'] as String;
        final refreshToken = data['refresh_token'] as String?;
        final driverData = data['driver'] as Map<String, dynamic>;
        
        // إنشاء كائن السائق
        final driver = Driver.fromJson(driverData);
        
        // حفظ الجلسة
        await StorageService.saveSession(
          authToken: token,
          refreshToken: refreshToken,
          driver: driver,
          phone: phone,
        );
        
        return AuthResult.success(
          message: 'تم تسجيل الدخول بنجاح',
          data: {
            'driver': driver,
            'token': token,
          },
        );
      } else {
        final error = jsonDecode(response.body);
        return AuthResult.failure(
          message: error['message'] ?? 'رمز التحقق غير صحيح',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      _log('خطأ في تأكيد OTP: $e');
      return AuthResult.failure(
        message: 'خطأ في الاتصال بالخادم',
        error: e,
      );
    }
  }
  
  // ==================== إدارة الجلسة ====================
  
  /// التحقق من صحة الجلسة الحالية
  static Future<bool> isValidSession() async {
    try {
      final isValid = await StorageService.isValidSession();
      if (!isValid) return false;
      
      // التحقق من صحة التوكن مع الخادم (اختياري)
      if (!ApiConfig.useMockData) {
        return await _validateTokenWithServer();
      }
      
      return true;
    } catch (e) {
      _log('خطأ في التحقق من الجلسة: $e');
      return false;
    }
  }
  
  /// الحصول على السائق الحالي
  static Future<Driver?> getCurrentDriver() async {
    try {
      return await StorageService.getDriver();
    } catch (e) {
      _log('خطأ في الحصول على بيانات السائق: $e');
      return null;
    }
  }
  
  /// تحديث بيانات السائق
  static Future<AuthResult> updateDriverProfile(Driver updatedDriver) async {
    try {
      // حفظ البيانات محلياً
      await StorageService.saveDriver(updatedDriver);
      
      // إرسال التحديث للخادم (إذا لم يكن في الوضع التجريبي)
      if (!ApiConfig.useMockData) {
        // TODO: تنفيذ تحديث البيانات مع الخادم
      }
      
      return AuthResult.success(
        message: 'تم تحديث البيانات بنجاح',
        data: {'driver': updatedDriver},
      );
    } catch (e) {
      _log('خطأ في تحديث بيانات السائق: $e');
      return AuthResult.failure(
        message: 'خطأ في تحديث البيانات',
        error: e,
      );
    }
  }
  
  /// تسجيل الخروج
  static Future<AuthResult> logout() async {
    try {
      _log('تسجيل الخروج');
      
      // إشعار الخادم بتسجيل الخروج (إذا لم يكن في الوضع التجريبي)
      if (!ApiConfig.useMockData) {
        final token = await StorageService.getAuthToken();
        if (token != null) {
          await _logoutFromServer(token);
        }
      }
      
      // مسح الجلسة المحلية
      await StorageService.clearSession();
      
      return AuthResult.success(
        message: 'تم تسجيل الخروج بنجاح',
      );
    } catch (e) {
      _log('خطأ في تسجيل الخروج: $e');
      // حتى لو فشل الطلب للخادم، امسح الجلسة المحلية
      await StorageService.clearSession();
      return AuthResult.success(
        message: 'تم تسجيل الخروج',
      );
    }
  }
  
  // ==================== الدوال المساعدة ====================
  
  /// التحقق من صحة التوكن مع الخادم
  static Future<bool> _validateTokenWithServer() async {
    try {
      final token = await StorageService.getAuthToken();
      if (token == null) return false;
      
      final url = ApiConfig.buildUrl(ApiConfig.profileEndpoint);
      final response = await http.get(
        Uri.parse(url),
        headers: ApiConfig.getAuthHeaders(token),
      ).timeout(
        Duration(seconds: ApiConfig.connectionTimeout),
      );
      
      return ApiConfig.isSuccessResponse(response.statusCode);
    } catch (e) {
      _log('خطأ في التحقق من التوكن: $e');
      return false;
    }
  }
  
  /// إشعار الخادم بتسجيل الخروج
  static Future<void> _logoutFromServer(String token) async {
    try {
      final url = ApiConfig.buildUrl(ApiConfig.logoutEndpoint);
      await http.post(
        Uri.parse(url),
        headers: ApiConfig.getAuthHeaders(token),
      ).timeout(
        Duration(seconds: ApiConfig.connectionTimeout),
      );
    } catch (e) {
      _log('خطأ في إشعار الخادم بتسجيل الخروج: $e');
    }
  }
  
  // ==================== البيانات التجريبية ====================
  
  /// تسجيل دخول تجريبي
  static Future<AuthResult> _mockLogin(String phone, String password) async {
    _log('Mock Login - Phone: $phone, Password length: ${password.length}');

    await Future.delayed(Duration(seconds: ApiConfig.mockDataDelay));

    // تحقق من صحة البيانات
    if (phone.isEmpty || password.isEmpty) {
      _log('Mock Login Failed - Empty phone or password');
      return AuthResult.failure(
        message: 'يرجى إدخال رقم الهاتف وكلمة المرور',
      );
    }

    // محاكاة أرقام هواتف صحيحة
    final validPhones = [
      '+966501234567',
      '+966551234567',
      '+966561234567',
      '0501234567',
      '0551234567',
      '0561234567',
    ];

    _log('Mock Login - Checking phone: $phone against valid phones: $validPhones');

    if (!validPhones.contains(phone)) {
      _log('Mock Login Failed - Invalid phone number: $phone');
      return AuthResult.failure(
        message: 'رقم الهاتف غير مسجل. جرب: 0501234567',
      );
    }

    if (password.length < 6) {
      _log('Mock Login Failed - Password too short: ${password.length}');
      return AuthResult.failure(
        message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
      );
    }

    _log('Mock Login Success - Phone: $phone');
    return AuthResult.success(
      message: 'تم إرسال رمز التحقق إلى $phone',
    );
  }
  
  /// تأكيد OTP تجريبي
  static Future<AuthResult> _mockVerifyOtp(String phone, String otp) async {
    await Future.delayed(Duration(seconds: ApiConfig.mockDataDelay));
    
    // رمز OTP الصحيح للاختبار
    const validOtp = '123456';
    
    if (otp != validOtp) {
      return AuthResult.failure(
        message: 'رمز التحقق غير صحيح',
      );
    }
    
    // إنشاء سائق تجريبي
    final mockDriver = Driver(
      id: 'driver_${Random().nextInt(1000)}',
      name: 'يوسف أحمد',
      phone: phone,
      email: '<EMAIL>',
      vehicleType: 'دراجة نارية',
      vehiclePlate: 'ABC-123',
      rating: 4.8,
      totalDeliveries: 156,
      isOnline: true,
      currentLocation: const DriverLocation(
        latitude: 24.7136,
        longitude: 46.6753,
        address: 'الرياض، المملكة العربية السعودية',
      ),
      joinDate: DateTime.now().subtract(const Duration(days: 90)),
    );
    
    // توكن تجريبي
    const mockToken = 'mock_jwt_token_for_testing_purposes_only';
    
    return AuthResult.success(
      message: 'تم تسجيل الدخول بنجاح',
      data: {
        'driver': mockDriver,
        'token': mockToken,
      },
    );
  }
  
  /// طباعة رسائل التطوير
  static void _log(String message) {
    if (kDebugMode && ApiConfig.enableApiLogging) {
      print('[$_tag] $message');
    }
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String message;
  final Map<String, dynamic>? data;
  final int? statusCode;
  final dynamic error;
  
  const AuthResult._({
    required this.isSuccess,
    required this.message,
    this.data,
    this.statusCode,
    this.error,
  });
  
  /// نتيجة ناجحة
  factory AuthResult.success({
    required String message,
    Map<String, dynamic>? data,
  }) {
    return AuthResult._(
      isSuccess: true,
      message: message,
      data: data,
    );
  }
  
  /// نتيجة فاشلة
  factory AuthResult.failure({
    required String message,
    int? statusCode,
    dynamic error,
  }) {
    return AuthResult._(
      isSuccess: false,
      message: message,
      statusCode: statusCode,
      error: error,
    );
  }
  
  /// الحصول على السائق من النتيجة
  Driver? get driver {
    if (data == null) return null;
    return data!['driver'] as Driver?;
  }
  
  /// الحصول على التوكن من النتيجة
  String? get token {
    if (data == null) return null;
    return data!['token'] as String?;
  }
}
