import 'package:flutter/foundation.dart';
import '../models/driver.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';

/// مزود حالة المصادقة لإدارة تسجيل الدخول والخروج
class AuthProvider with ChangeNotifier {
  // ==================== الحالة ====================

  Driver? _currentDriver;
  bool _isLoggedIn = false;
  bool _isLoading = false;
  String? _errorMessage;
  String? _lastPhone;
  bool _isWaitingForOtp = false;
  
  // ==================== Getters ====================
  
  /// السائق الحالي
  Driver? get currentDriver => _currentDriver;
  
  /// حالة تسجيل الدخول
  bool get isLoggedIn => _isLoggedIn;
  
  /// حالة التحميل
  bool get isLoading => _isLoading;
  
  /// رسالة الخطأ
  String? get errorMessage => _errorMessage;
  
  /// آخر رقم هاتف تم استخدامه
  String? get lastPhone => _lastPhone;

  /// هل في انتظار تأكيد OTP
  bool get isWaitingForOtp => _isWaitingForOtp;

  /// اسم السائق (للعرض)
  String get driverName => _currentDriver?.name ?? 'السائق';

  /// هل السائق متصل
  bool get isDriverOnline => _currentDriver?.isOnline ?? false;
  
  // ==================== التهيئة ====================
  
  /// تهيئة المزود وفحص الجلسة المحفوظة
  Future<void> initialize() async {
    _setLoading(true);
    
    try {
      // تهيئة خدمة التخزين
      await StorageService.init();
      
      // فحص الجلسة المحفوظة
      await _checkSavedSession();
      
      _clearError();
    } catch (e) {
      _setError('خطأ في تهيئة التطبيق: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// فحص الجلسة المحفوظة
  Future<void> _checkSavedSession() async {
    try {
      final isValidSession = await AuthService.isValidSession();
      
      if (isValidSession) {
        final driver = await StorageService.getDriver();
        final phone = await StorageService.getPhone();
        
        if (driver != null) {
          _currentDriver = driver;
          _isLoggedIn = true;
          _lastPhone = phone;
          
          if (kDebugMode) {
            print('تم استرداد الجلسة المحفوظة للسائق: ${driver.name}');
          }
        }
      } else {
        // مسح الجلسة غير الصحيحة
        await _clearSession();
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في فحص الجلسة المحفوظة: $e');
      }
      await _clearSession();
    }
  }
  
  // ==================== تسجيل الدخول ====================
  
  /// تسجيل الدخول بالهاتف وكلمة المرور
  Future<bool> login({
    required String phone,
    required String password,
  }) async {
    if (kDebugMode) {
      print('AuthProvider: Starting login for phone: $phone');
    }

    _setLoading(true);
    _clearError();

    try {
      final result = await AuthService.login(
        phone: phone,
        password: password,
      );

      if (kDebugMode) {
        print('AuthProvider: Login result - Success: ${result.isSuccess}, Message: ${result.message}');
      }

      if (result.isSuccess) {
        _lastPhone = phone;
        _isWaitingForOtp = true;
        notifyListeners();
        if (kDebugMode) {
          print('AuthProvider: Login successful, ready for OTP');
        }
        return true;
      } else {
        _setError(result.message);
        if (kDebugMode) {
          print('AuthProvider: Login failed - ${result.message}');
        }
        return false;
      }
    } catch (e) {
      _setError('خطأ في تسجيل الدخول: $e');
      if (kDebugMode) {
        print('AuthProvider: Login exception - $e');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  /// تأكيد رمز OTP وإكمال تسجيل الدخول
  Future<bool> verifyOtp({
    required String phone,
    required String otp,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      final result = await AuthService.verifyOtp(
        phone: phone,
        otp: otp,
      );
      
      if (result.isSuccess) {
        _currentDriver = result.driver;
        _isLoggedIn = true;
        _isWaitingForOtp = false;
        _lastPhone = phone;

        if (kDebugMode) {
          print('تم تسجيل الدخول بنجاح للسائق: ${_currentDriver?.name}');
        }

        notifyListeners();
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError('خطأ في تأكيد الرمز: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // ==================== تسجيل الخروج ====================
  
  /// تسجيل الخروج
  Future<bool> logout() async {
    _setLoading(true);
    _clearError();
    
    try {
      final result = await AuthService.logout();
      
      if (result.isSuccess) {
        await _clearSession();
        
        if (kDebugMode) {
          print('تم تسجيل الخروج بنجاح');
        }
        
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError('خطأ في تسجيل الخروج: $e');
      // حتى لو فشل، امسح الجلسة المحلية
      await _clearSession();
      return true;
    } finally {
      _setLoading(false);
    }
  }
  
  // ==================== إدارة الملف الشخصي ====================
  
  /// تحديث بيانات السائق
  Future<bool> updateDriverProfile(Driver updatedDriver) async {
    _setLoading(true);
    _clearError();
    
    try {
      final result = await AuthService.updateDriverProfile(updatedDriver);
      
      if (result.isSuccess) {
        _currentDriver = updatedDriver;
        notifyListeners();
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث البيانات: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  /// تحديث حالة الاتصال للسائق
  Future<void> updateDriverOnlineStatus(bool isOnline) async {
    if (_currentDriver == null) return;
    
    try {
      final updatedDriver = _currentDriver!.copyWith(isOnline: isOnline);
      await updateDriverProfile(updatedDriver);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تحديث حالة الاتصال: $e');
      }
    }
  }
  
  // ==================== إدارة الحالة ====================
  
  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }
  
  /// تعيين رسالة خطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
    
    if (kDebugMode) {
      print('AuthProvider Error: $error');
    }
  }
  
  /// مسح رسالة الخطأ
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }
  
  /// مسح الجلسة المحلية
  Future<void> _clearSession() async {
    _currentDriver = null;
    _isLoggedIn = false;
    _isWaitingForOtp = false;
    _errorMessage = null;

    try {
      await StorageService.clearSession();
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في مسح الجلسة: $e');
      }
    }

    notifyListeners();
  }
  
  /// مسح رسالة الخطأ يدوياً
  void clearError() {
    _clearError();
  }
  
  /// إعادة تحميل بيانات السائق
  Future<void> refreshDriverData() async {
    if (!_isLoggedIn) return;
    
    _setLoading(true);
    
    try {
      final driver = await StorageService.getDriver();
      if (driver != null) {
        _currentDriver = driver;
        notifyListeners();
      }
    } catch (e) {
      _setError('خطأ في تحديث البيانات: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // ==================== أدوات مساعدة ====================
  
  /// التحقق من صحة الجلسة
  Future<bool> validateSession() async {
    try {
      return await AuthService.isValidSession();
    } catch (e) {
      return false;
    }
  }
  
  /// الحصول على معلومات الجلسة للتطوير
  Future<Map<String, dynamic>> getSessionInfo() async {
    if (!kDebugMode) return {};
    
    try {
      final token = await StorageService.getAuthToken();
      final lastLogin = await StorageService.getLastLogin();
      
      return {
        'isLoggedIn': _isLoggedIn,
        'driverName': _currentDriver?.name,
        'driverPhone': _currentDriver?.phone,
        'hasToken': token != null,
        'lastLogin': lastLogin?.toString(),
        'lastPhone': _lastPhone,
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }
  
  /// طباعة معلومات الجلسة للتطوير
  Future<void> debugPrintSession() async {
    if (kDebugMode) {
      final info = await getSessionInfo();
      print('=== AuthProvider Session Info ===');
      info.forEach((key, value) {
        print('$key: $value');
      });
      print('================================');
    }
  }
}
