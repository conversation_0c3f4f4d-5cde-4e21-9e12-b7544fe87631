-- =====================================================
-- WASSLTI DATABASE SCHEMA TEST SCRIPT
-- سكريبت اختبار قاعدة بيانات وصلتي
-- =====================================================

-- Test data insertion to verify schema works correctly

-- Test 1: Insert test admin user
INSERT INTO admin_users (username, email, password_hash, full_name, role, permissions) 
VALUES ('testadmin', '<EMAIL>', 'hashed_password', 'مدير اختبار', 'admin', '["users", "orders"]')
ON CONFLICT (username) DO NOTHING;

-- Test 2: Insert test customer
INSERT INTO users (full_name, email, phone, role, address) 
VALUES ('أحمد محمد', '<EMAIL>', '+212600000001', 'customer', 'الدار البيضاء، المغرب')
ON CONFLICT (phone) DO NOTHING;

-- Test 3: Insert test driver
INSERT INTO drivers (full_name, email, phone, vehicle_type, vehicle_model, vehicle_plate) 
VALUES ('يوسف السائق', '<EMAIL>', '+212600000002', 'motorcycle', 'Honda CB', 'A123456')
ON CONFLICT (phone) DO NOTHING;

-- Test 4: Insert test restaurant
INSERT INTO restaurants (name, email, phone, address, cuisine_type, delivery_fee, minimum_order) 
VALUES ('مطعم الأصالة', '<EMAIL>', '+212600000003', 'شارع محمد الخامس، الرباط', ARRAY['مأكولات عربية', 'مشاوي'], 15.00, 50.00)
ON CONFLICT (email) DO NOTHING;

-- Test 5: Insert test meal
INSERT INTO meals (restaurant_id, category_id, name, description, price, is_available) 
SELECT 
    r.id,
    c.id,
    'كباب لحم',
    'كباب لحم طازج مع الخضار والأرز',
    85.00,
    true
FROM restaurants r, categories c 
WHERE r.email = '<EMAIL>' AND c.name = 'مأكولات عربية'
LIMIT 1;

-- Test 6: Insert test order
INSERT INTO orders (
    user_id, 
    restaurant_id, 
    subtotal, 
    delivery_fee, 
    total_price, 
    delivery_address, 
    payment_method,
    status
) 
SELECT 
    u.id,
    r.id,
    85.00,
    15.00,
    100.00,
    'شارع الحسن الثاني، الدار البيضاء',
    'cash',
    'pending'
FROM users u, restaurants r 
WHERE u.email = '<EMAIL>' AND r.email = '<EMAIL>'
LIMIT 1;

-- Test 7: Insert order item
INSERT INTO order_items (order_id, meal_id, quantity, unit_price, total_price)
SELECT 
    o.id,
    m.id,
    1,
    85.00,
    85.00
FROM orders o, meals m, restaurants r
WHERE o.order_number LIKE 'ORD%' 
AND m.restaurant_id = r.id 
AND r.email = '<EMAIL>'
AND m.name = 'كباب لحم'
LIMIT 1;

-- Test 8: Insert test promotion
INSERT INTO promotions (code, name, description, type, value, minimum_order, start_date, end_date)
VALUES (
    'WELCOME20',
    'خصم الترحيب',
    'خصم 20% للعملاء الجدد',
    'percentage',
    20.00,
    50.00,
    NOW(),
    NOW() + INTERVAL '30 days'
);

-- Test 9: Insert test review
INSERT INTO reviews (order_id, user_id, restaurant_id, rating, comment, food_rating, delivery_rating, service_rating)
SELECT 
    o.id,
    u.id,
    r.id,
    5,
    'طعام ممتاز وخدمة سريعة',
    5,
    5,
    5
FROM orders o, users u, restaurants r
WHERE u.email = '<EMAIL>' 
AND r.email = '<EMAIL>'
AND o.user_id = u.id
AND o.restaurant_id = r.id
LIMIT 1;

-- Test 10: Insert test notification
INSERT INTO notifications (user_id, title, message, type)
SELECT 
    u.id,
    'مرحباً بك في وصلتي',
    'نشكرك لانضمامك إلى منصة وصلتي لتوصيل الطعام',
    'system'
FROM users u 
WHERE u.email = '<EMAIL>'
LIMIT 1;

-- =====================================================
-- VERIFICATION QUERIES
-- استعلامات التحقق
-- =====================================================

-- Check if all tables have data
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'drivers', COUNT(*) FROM drivers
UNION ALL
SELECT 'restaurants', COUNT(*) FROM restaurants
UNION ALL
SELECT 'meals', COUNT(*) FROM meals
UNION ALL
SELECT 'orders', COUNT(*) FROM orders
UNION ALL
SELECT 'order_items', COUNT(*) FROM order_items
UNION ALL
SELECT 'payments', COUNT(*) FROM payments
UNION ALL
SELECT 'wallets', COUNT(*) FROM wallets
UNION ALL
SELECT 'wallet_transactions', COUNT(*) FROM wallet_transactions
UNION ALL
SELECT 'promotions', COUNT(*) FROM promotions
UNION ALL
SELECT 'reviews', COUNT(*) FROM reviews
UNION ALL
SELECT 'notifications', COUNT(*) FROM notifications
UNION ALL
SELECT 'categories', COUNT(*) FROM categories
UNION ALL
SELECT 'admin_users', COUNT(*) FROM admin_users
UNION ALL
SELECT 'system_settings', COUNT(*) FROM system_settings;

-- Check if triggers are working (wallets should be created automatically)
SELECT 
    'Wallet Creation Test' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASSED - Wallets created automatically'
        ELSE 'FAILED - No wallets found'
    END as result
FROM wallets;

-- Check if order number generation is working
SELECT 
    'Order Number Generation Test' as test_name,
    CASE 
        WHEN COUNT(*) > 0 AND order_number LIKE 'ORD%' THEN 'PASSED - Order numbers generated'
        ELSE 'FAILED - Order number generation not working'
    END as result,
    order_number
FROM orders 
WHERE order_number IS NOT NULL
LIMIT 1;

-- Check if rating updates are working
SELECT 
    'Rating Update Test' as test_name,
    CASE 
        WHEN rating > 0 THEN 'PASSED - Restaurant rating updated'
        ELSE 'FAILED - Rating not updated'
    END as result,
    rating,
    total_ratings
FROM restaurants 
WHERE email = '<EMAIL>';

-- Check views are working
SELECT 'Views Test' as test_name, 'order_statistics view' as view_name, COUNT(*) as records
FROM order_statistics
UNION ALL
SELECT 'Views Test', 'restaurant_performance view', COUNT(*)
FROM restaurant_performance
UNION ALL
SELECT 'Views Test', 'driver_performance view', COUNT(*)
FROM driver_performance;

-- Check indexes exist
SELECT 
    'Index Test' as test_name,
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'drivers', 'restaurants', 'orders', 'meals')
ORDER BY tablename, indexname;

-- =====================================================
-- CLEANUP (optional - uncomment to clean test data)
-- تنظيف البيانات الاختبارية
-- =====================================================

/*
-- Uncomment these lines to clean up test data
DELETE FROM wallet_transactions WHERE wallet_id IN (SELECT id FROM wallets WHERE user_id IN (SELECT id FROM users WHERE email = '<EMAIL>'));
DELETE FROM notifications WHERE user_id IN (SELECT id FROM users WHERE email = '<EMAIL>');
DELETE FROM reviews WHERE user_id IN (SELECT id FROM users WHERE email = '<EMAIL>');
DELETE FROM order_items WHERE order_id IN (SELECT id FROM orders WHERE user_id IN (SELECT id FROM users WHERE email = '<EMAIL>'));
DELETE FROM orders WHERE user_id IN (SELECT id FROM users WHERE email = '<EMAIL>');
DELETE FROM meals WHERE restaurant_id IN (SELECT id FROM restaurants WHERE email = '<EMAIL>');
DELETE FROM wallets WHERE restaurant_id IN (SELECT id FROM restaurants WHERE email = '<EMAIL>');
DELETE FROM wallets WHERE driver_id IN (SELECT id FROM drivers WHERE email = '<EMAIL>');
DELETE FROM wallets WHERE user_id IN (SELECT id FROM users WHERE email = '<EMAIL>');
DELETE FROM restaurants WHERE email = '<EMAIL>';
DELETE FROM drivers WHERE email = '<EMAIL>';
DELETE FROM users WHERE email = '<EMAIL>';
DELETE FROM promotions WHERE code = 'WELCOME20';
DELETE FROM admin_users WHERE username = 'testadmin';
*/
