import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/settings_menu_widget.dart';
import '../widgets/settings_content_widget.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String searchQuery = '';
  String selectedSection = 'Account';

  final List<SettingsSection> settingsSections = [
    SettingsSection(
      icon: Icons.person_outline,
      title: 'Account',
      description: 'Account settings offer personalization options for a tailored experience',
    ),
    SettingsSection(
      icon: Icons.notifications_outlined,
      title: 'Notification',
      description: 'Account settings offer personalization options for a tailored experience',
    ),
    SettingsSection(
      icon: Icons.security_outlined,
      title: 'Security',
      description: 'Account settings offer personalization options for a tailored experience',
    ),
    SettingsSection(
      icon: Icons.payment_outlined,
      title: 'Payment',
      description: 'Account settings offer personalization options for a tailored experience',
    ),
    SettingsSection(
      icon: Icons.help_outline,
      title: 'Help',
      description: 'Account settings offer personalization options for a tailored experience',
    ),
    SettingsSection(
      icon: Icons.privacy_tip_outlined,
      title: 'Privacy Policy',
      description: 'Account settings offer personalization options for a tailored experience',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: Row(
                    children: [
                      // Settings Menu
                      Container(
                        width: 400,
                        padding: const EdgeInsets.all(24),
                        child: SettingsMenuWidget(
                          sections: settingsSections,
                          selectedSection: selectedSection,
                          onSectionSelected: (section) {
                            setState(() {
                              selectedSection = section;
                            });
                          },
                        ),
                      ),
                      
                      // Settings Content
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(24),
                          child: SettingsContentWidget(
                            selectedSection: selectedSection,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Text(
                'Settings',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 8),
              const Text('😊', style: TextStyle(fontSize: 20)),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Notifications
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.notifications_outlined,
                    color: AppColors.textSecondary,
                    size: 20,
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // User Profile
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primary,
                child: Text(
                  'JS',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Jhon Smith',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'User',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class SettingsSection {
  final IconData icon;
  final String title;
  final String description;

  SettingsSection({
    required this.icon,
    required this.title,
    required this.description,
  });
}
