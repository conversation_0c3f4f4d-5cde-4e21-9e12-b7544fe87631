import React from 'react';
import { Phone, Mail, MapPin, Facebook, Instagram, Twitter, Apple, PlayCircle, Globe, Shield, Award, Clock } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-accent text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-orange-400/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 py-16 relative z-10">
        {/* Top Section */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-6">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-2xl">و</span>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <span className="text-2xl font-bold">وصلتي</span>
                <div className="text-sm text-gray-400">Wasslti</div>
              </div>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed rtl-font">
              منصة المغرب الرائدة لتوصيل الطعام، نربطك بمطاعمك المفضلة وأشهى الأطباق مع خدمة توصيل سريعة وموثوقة.
            </p>
            
            {/* Trust Badges */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <Shield className="w-5 h-5 text-primary" />
                </div>
                <div className="text-xs text-gray-400 rtl-font">دفع آمن</div>
              </div>
              <div className="text-center">
                <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <Clock className="w-5 h-5 text-primary" />
                </div>
                <div className="text-xs text-gray-400 rtl-font">توصيل سريع</div>
              </div>
              <div className="text-center">
                <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center mx-auto mb-2">
                  <Award className="w-5 h-5 text-primary" />
                </div>
                <div className="text-xs text-gray-400 rtl-font">جودة مضمونة</div>
              </div>
            </div>
            
            {/* Social Media */}
            <div className="flex space-x-4 rtl:space-x-reverse">
              <a 
                href="https://facebook.com/wasslti" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 bg-white/10 rounded-xl flex items-center justify-center text-gray-300 hover:text-primary hover:bg-primary/20 transition-all duration-300 hover:scale-110"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a 
                href="https://instagram.com/wasslti" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 bg-white/10 rounded-xl flex items-center justify-center text-gray-300 hover:text-primary hover:bg-primary/20 transition-all duration-300 hover:scale-110"
              >
                <Instagram className="w-5 h-5" />
              </a>
              <a 
                href="https://twitter.com/wasslti" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 bg-white/10 rounded-xl flex items-center justify-center text-gray-300 hover:text-primary hover:bg-primary/20 transition-all duration-300 hover:scale-110"
              >
                <Twitter className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-bold mb-6 rtl-font">روابط سريعة</h4>
            <ul className="space-y-4">
              {[
                { label: 'من نحن', href: '#about' },
                { label: 'المطاعم', href: '#restaurants' },
                { label: 'خريطة الموقع', href: '/sitemap' },
                { label: 'كن شريكاً', href: '#partner' },
                { label: 'تسجيل السائقين', href: '#drivers' },
                { label: 'الوظائف', href: '#careers' },
                { label: 'المدونة', href: '#blog' },
                { label: 'الأخبار', href: '#news' }
              ].map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    onClick={(e) => {
                      if (link.href.startsWith('/')) {
                        e.preventDefault();
                        window.location.href = link.href;
                      }
                    }}
                    className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center space-x-2 rtl:space-x-reverse group"
                  >
                    <div className="w-1 h-1 bg-primary rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <span className="rtl-font">{link.label}</span>
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-xl font-bold mb-6 rtl-font">الدعم والمساعدة</h4>
            <ul className="space-y-4">
              {[
                { label: 'مركز المساعدة', href: '#help' },
                { label: 'تتبع الطلب', href: '#track' },
                { label: 'شروط الخدمة', href: '#terms' },
                { label: 'سياسة الخصوصية', href: '#privacy' },
                { label: 'سياسة الاسترداد', href: '#refund' },
                { label: 'الأسئلة الشائعة', href: '#faq' },
                { label: 'تواصل معنا', href: '#contact' }
              ].map((link, index) => (
                <li key={index}>
                  <a href={link.href} className="text-gray-300 hover:text-primary transition-colors duration-300 flex items-center space-x-2 rtl:space-x-reverse group">
                    <div className="w-1 h-1 bg-primary rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <span className="rtl-font">{link.label}</span>
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact & Apps */}
          <div>
            <h4 className="text-xl font-bold mb-6 rtl-font">تواصل معنا</h4>
            <div className="space-y-4 mb-8">
              <div className="flex items-center space-x-3 rtl:space-x-reverse text-gray-300">
                <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center">
                  <Phone className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold">+212 5XX-XXXXXX</div>
                  <div className="text-sm text-gray-400 rtl-font">متاح 24/7</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 rtl:space-x-reverse text-gray-300">
                <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center">
                  <Mail className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold"><EMAIL></div>
                  <div className="text-sm text-gray-400 rtl-font">دعم فني سريع</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 rtl:space-x-reverse text-gray-300">
                <div className="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center">
                  <MapPin className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold rtl-font">الدار البيضاء، المغرب</div>
                  <div className="text-sm text-gray-400 rtl-font">المقر الرئيسي</div>
                </div>
              </div>
            </div>
            
            {/* App Download */}
            <div>
              <h5 className="font-semibold mb-4 rtl-font">حمل التطبيق</h5>
              <div className="space-y-3">
                <a 
                  href="https://apps.apple.com/app/wasslti" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 rtl:space-x-reverse text-gray-300 hover:text-primary transition-colors duration-300 group"
                >
                  <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30">
                    <Apple className="w-4 h-4" />
                  </div>
                  <span className="text-sm font-medium">App Store</span>
                </a>
                <a 
                  href="https://play.google.com/store/apps/details?id=com.wasslti" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 rtl:space-x-reverse text-gray-300 hover:text-primary transition-colors duration-300 group"
                >
                  <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30">
                    <PlayCircle className="w-4 h-4" />
                  </div>
                  <span className="text-sm font-medium">Google Play</span>
                </a>
              </div>
            </div>
          </div>
        </div>
        
        {/* Bottom Section */}
        <div className="border-t border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-center md:text-right">
              <p className="text-gray-300 rtl-font">
                &copy; {currentYear} وصلتي. جميع الحقوق محفوظة. | صنع بـ ❤️ في المغرب
              </p>
            </div>
            
            <div className="flex items-center space-x-6 rtl:space-x-reverse">
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-400">
                <Globe className="w-4 h-4" />
                <span className="text-sm rtl-font">متاح في 12 مدينة</span>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-400">
                <Award className="w-4 h-4" />
                <span className="text-sm rtl-font">أفضل تطبيق 2024</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;