# 🗺️ إعداد Google Maps API Key

## 📋 المتطلبات

لتشغيل الخريطة في التطبيق، تحتاج إلى Google Maps API Key.

## 🔧 خطوات الإعداد

### 1. إنشاء مشروع في Google Cloud Console

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. انقر على **Select a project** في أعلى الصفحة
3. انقر على **NEW PROJECT**
4. أدخل اسم المشروع (مثل: "Wasslti Partner")
5. انقر على **CREATE**

### 2. تفعيل Maps SDK for Android

1. في Google Cloud Console، اذهب إلى **APIs & Services > Library**
2. ابحث عن "Maps SDK for Android"
3. انقر على النتيجة الأولى
4. انقر على **ENABLE**

### 3. إنشاء API Key

1. اذهب إلى **APIs & Services > Credentials**
2. انقر على **+ CREATE CREDENTIALS**
3. اختر **API key**
4. انسخ المفتاح الذي تم إنشاؤه

### 4. تقييد API Key (اختياري ولكن مُوصى به)

1. انقر على اسم API Key الذي تم إنشاؤه
2. في قسم **Application restrictions**:
   - اختر **Android apps**
   - انقر على **+ Add an item**
   - أدخل Package name: `com.example.wasslti_partner`
   - أدخل SHA-1 certificate fingerprint (اختياري)
3. في قسم **API restrictions**:
   - اختر **Restrict key**
   - اختر **Maps SDK for Android**
4. انقر على **SAVE**

### 5. إضافة API Key إلى التطبيق

1. افتح ملف `android/app/src/main/AndroidManifest.xml`
2. ابحث عن السطر:
   ```xml
   android:value="YOUR_GOOGLE_MAPS_API_KEY_HERE"
   ```
3. استبدل `YOUR_GOOGLE_MAPS_API_KEY_HERE` بالمفتاح الذي حصلت عليه

**مثال:**
```xml
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" />
```

### 6. تحديث الكود للاستخدام الفعلي

بعد إضافة API Key، قم بتحديث الكود:

1. في `lib/screens/auth/otp_verification_screen.dart`، غيّر:
   ```dart
   import '../home/<USER>';
   ```
   إلى:
   ```dart
   import '../home/<USER>';
   ```

2. وغيّر:
   ```dart
   builder: (context) => const DriverHomeScreenTemp(
   ```
   إلى:
   ```dart
   builder: (context) => const DriverHomeScreen(
   ```

## 🚀 اختبار الإعداد

1. شغّل التطبيق:
   ```bash
   flutter run
   ```

2. انتقل خلال تسجيل الدخول → OTP → الشاشة الرئيسية

3. يجب أن تظهر خريطة Google Maps بدلاً من الشاشة المؤقتة

## ⚠️ ملاحظات مهمة

### الأمان:
- **لا تشارك API Key** في الكود المصدري العام
- استخدم **تقييد API Key** لحماية الاستخدام
- راقب **استخدام API** في Google Cloud Console

### التكلفة:
- Google Maps لديه **حصة مجانية** شهرية
- راجع [أسعار Google Maps](https://cloud.google.com/maps-platform/pricing) للتفاصيل

### استكشاف الأخطاء:
- تأكد من أن **Package name** صحيح
- تأكد من تفعيل **Maps SDK for Android**
- تحقق من **صحة API Key**

## 📱 الشاشة المؤقتة

حالياً، التطبيق يستخدم شاشة مؤقتة (`DriverHomeScreenTemp`) تحاكي الخريطة:
- ✅ جميع الميزات تعمل (الأزرار، النوافذ المنبثقة)
- ✅ التصميم مطابق للشاشة الأصلية
- ✅ رسالة واضحة لإعداد API Key
- ⏳ بانتظار إضافة Google Maps API Key

## 🔄 العودة للخريطة الحقيقية

بعد إعداد API Key، ستحصل على:
- 🗺️ **خريطة Google Maps حقيقية**
- 📍 **موقع السائق مع GPS**
- 🏪 **علامات المطاعم والعملاء**
- 🛣️ **إمكانية التنقل والاتجاهات**

---

## 📞 الدعم

إذا واجهت أي مشاكل في الإعداد:
1. تأكد من اتباع جميع الخطوات
2. راجع [وثائق Google Maps](https://developers.google.com/maps/documentation/android-sdk/start)
3. تحقق من رسائل الخطأ في وحدة التحكم
