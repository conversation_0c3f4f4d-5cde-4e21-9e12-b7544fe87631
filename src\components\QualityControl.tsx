import React, { useState } from 'react';
import {
  Shield,
  Star,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Eye,
  Flag,
  Clock,
  Target,
  Award,
  BarChart3,
  PieChart,
  Filter,
  Search,
  Download,
  RefreshCw,
  Settings,
  User,
  Building,
  Car,
  MessageSquare,
  Phone,
  Mail,
  Calendar,
  MapPin,
  Zap
} from 'lucide-react';

interface QualityMetric {
  id: string;
  name: string;
  category: 'restaurant' | 'driver' | 'customer' | 'system';
  currentValue: number;
  targetValue: number;
  trend: 'up' | 'down' | 'stable';
  status: 'excellent' | 'good' | 'warning' | 'critical';
  lastUpdated: string;
}

interface QualityIssue {
  id: string;
  type: 'food_quality' | 'delivery_delay' | 'customer_complaint' | 'driver_behavior' | 'system_error';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  reportedBy: string;
  reportedAt: string;
  assignedTo?: string;
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  relatedEntity: {
    type: 'restaurant' | 'driver' | 'customer' | 'order';
    id: string;
    name: string;
  };
  resolution?: string;
  resolvedAt?: string;
}

interface PerformanceAlert {
  id: string;
  type: 'rating_drop' | 'delivery_delay' | 'complaint_spike' | 'system_issue';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  message: string;
  affectedEntity: string;
  threshold: number;
  currentValue: number;
  createdAt: string;
  acknowledged: boolean;
}

const QualityControl = () => {
  const [qualityMetrics, setQualityMetrics] = useState<QualityMetric[]>([
    {
      id: 'overall_rating',
      name: 'التقييم العام للمنصة',
      category: 'system',
      currentValue: 4.6,
      targetValue: 4.5,
      trend: 'up',
      status: 'excellent',
      lastUpdated: '2024-01-20T14:30:00Z'
    },
    {
      id: 'delivery_time',
      name: 'متوسط وقت التوصيل',
      category: 'driver',
      currentValue: 28,
      targetValue: 30,
      trend: 'down',
      status: 'good',
      lastUpdated: '2024-01-20T14:25:00Z'
    },
    {
      id: 'food_quality',
      name: 'جودة الطعام',
      category: 'restaurant',
      currentValue: 4.3,
      targetValue: 4.2,
      trend: 'up',
      status: 'good',
      lastUpdated: '2024-01-20T14:20:00Z'
    },
    {
      id: 'customer_satisfaction',
      name: 'رضا العملاء',
      category: 'customer',
      currentValue: 87.5,
      targetValue: 85,
      trend: 'up',
      status: 'excellent',
      lastUpdated: '2024-01-20T14:15:00Z'
    }
  ]);

  const [qualityIssues, setQualityIssues] = useState<QualityIssue[]>([
    {
      id: 'ISS001',
      type: 'food_quality',
      severity: 'high',
      title: 'شكاوى متكررة من جودة الطعام',
      description: 'تلقينا عدة شكاوى حول جودة الطعام من مطعم الأصالة خلال الأسبوع الماضي',
      reportedBy: 'نظام المراقبة التلقائي',
      reportedAt: '2024-01-20T10:00:00Z',
      assignedTo: 'فريق الجودة',
      status: 'investigating',
      relatedEntity: {
        type: 'restaurant',
        id: 'REST001',
        name: 'مطعم الأصالة'
      }
    },
    {
      id: 'ISS002',
      type: 'delivery_delay',
      severity: 'medium',
      title: 'تأخير في التوصيل',
      description: 'السائق أحمد محمد يواجه تأخيرات متكررة في التوصيل',
      reportedBy: 'عميل',
      reportedAt: '2024-01-20T12:30:00Z',
      assignedTo: 'مدير السائقين',
      status: 'open',
      relatedEntity: {
        type: 'driver',
        id: 'DRV001',
        name: 'أحمد محمد'
      }
    }
  ]);

  const [performanceAlerts, setPerformanceAlerts] = useState<PerformanceAlert[]>([
    {
      id: 'ALT001',
      type: 'rating_drop',
      priority: 'high',
      message: 'انخفاض تقييم مطعم البحر الأبيض تحت الحد المطلوب',
      affectedEntity: 'مطعم البحر الأبيض',
      threshold: 4.0,
      currentValue: 3.8,
      createdAt: '2024-01-20T13:00:00Z',
      acknowledged: false
    },
    {
      id: 'ALT002',
      type: 'complaint_spike',
      priority: 'medium',
      message: 'زيادة في عدد الشكاوى بنسبة 25% هذا الأسبوع',
      affectedEntity: 'النظام العام',
      threshold: 10,
      currentValue: 15,
      createdAt: '2024-01-20T11:45:00Z',
      acknowledged: true
    }
  ]);

  const [selectedTab, setSelectedTab] = useState('metrics');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'bg-green-100 text-green-800';
      case 'good': return 'bg-blue-100 text-blue-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent': return <Award className="w-4 h-4" />;
      case 'good': return <CheckCircle className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <XCircle className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-500" />;
      case 'stable': return <Target className="w-4 h-4 text-gray-500" />;
      default: return <Target className="w-4 h-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800';
      case 'medium': return 'bg-blue-100 text-blue-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'restaurant': return <Building className="w-4 h-4" />;
      case 'driver': return <Car className="w-4 h-4" />;
      case 'customer': return <User className="w-4 h-4" />;
      case 'system': return <Shield className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const formatDateTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString('ar-MA'),
      time: date.toLocaleTimeString('ar-MA', { hour12: false })
    };
  };

  const acknowledgeAlert = (alertId: string) => {
    setPerformanceAlerts(prev => prev.map(alert =>
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 rtl-font">مراقبة الجودة والأداء</h2>
          <p className="text-gray-600 rtl-font">مراقبة وتحسين جودة الخدمات والأداء العام للمنصة</p>
        </div>

        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <RefreshCw className="w-4 h-4" />
            <span className="rtl-font">تحديث</span>
          </button>
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Download className="w-4 h-4" />
            <span className="rtl-font">تصدير</span>
          </button>
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            <Settings className="w-4 h-4" />
            <span className="rtl-font">إعدادات</span>
          </button>
        </div>
      </div>

      {/* Performance Alerts */}
      {performanceAlerts.filter(alert => !alert.acknowledged).length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <h3 className="font-semibold text-red-800 rtl-font">تنبيهات الأداء</h3>
            </div>
            <span className="text-sm text-red-600 rtl-font">
              {performanceAlerts.filter(alert => !alert.acknowledged).length} تنبيه جديد
            </span>
          </div>

          <div className="space-y-3">
            {performanceAlerts.filter(alert => !alert.acknowledged).map((alert) => (
              <div key={alert.id} className="flex items-center justify-between bg-white p-4 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(alert.priority)}`}>
                      {alert.priority === 'urgent' ? 'عاجل' :
                       alert.priority === 'high' ? 'عالي' :
                       alert.priority === 'medium' ? 'متوسط' : 'منخفض'}
                    </span>
                    <span className="text-sm text-gray-600 rtl-font">{alert.affectedEntity}</span>
                  </div>
                  <p className="text-sm text-gray-800 rtl-font">{alert.message}</p>
                  <p className="text-xs text-gray-500 rtl-font">
                    الحد المطلوب: {alert.threshold} | القيمة الحالية: {alert.currentValue}
                  </p>
                </div>
                <button
                  onClick={() => acknowledgeAlert(alert.id)}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors rtl-font"
                >
                  تأكيد
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quality Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {qualityMetrics.map((metric) => (
          <div key={metric.id} className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                {getCategoryIcon(metric.category)}
                <span className="text-sm text-gray-600 rtl-font">
                  {metric.category === 'restaurant' ? 'مطاعم' :
                   metric.category === 'driver' ? 'سائقين' :
                   metric.category === 'customer' ? 'عملاء' : 'نظام'}
                </span>
              </div>
              {getTrendIcon(metric.trend)}
            </div>

            <h3 className="font-semibold text-gray-900 mb-2 rtl-font">{metric.name}</h3>

            <div className="flex items-end space-x-2 rtl:space-x-reverse mb-3">
              <span className="text-2xl font-bold text-gray-900">
                {metric.name.includes('وقت') ? `${metric.currentValue}د` :
                 metric.name.includes('رضا') ? `${metric.currentValue}%` :
                 metric.currentValue}
              </span>
              <span className="text-sm text-gray-500 rtl-font">
                الهدف: {metric.name.includes('وقت') ? `${metric.targetValue}د` :
                        metric.name.includes('رضا') ? `${metric.targetValue}%` :
                        metric.targetValue}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className={`inline-flex items-center space-x-1 rtl:space-x-reverse px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(metric.status)}`}>
                {getStatusIcon(metric.status)}
                <span className="rtl-font">
                  {metric.status === 'excellent' ? 'ممتاز' :
                   metric.status === 'good' ? 'جيد' :
                   metric.status === 'warning' ? 'تحذير' : 'حرج'}
                </span>
              </span>
              <span className="text-xs text-gray-500">
                {formatDateTime(metric.lastUpdated).time}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 rtl:space-x-reverse px-6">
            <button
              onClick={() => setSelectedTab('metrics')}
              className={`py-4 px-2 border-b-2 font-medium text-sm ${
                selectedTab === 'metrics'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              } rtl-font`}
            >
              مؤشرات الجودة
            </button>
            <button
              onClick={() => setSelectedTab('issues')}
              className={`py-4 px-2 border-b-2 font-medium text-sm ${
                selectedTab === 'issues'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              } rtl-font`}
            >
              قضايا الجودة
            </button>
            <button
              onClick={() => setSelectedTab('reports')}
              className={`py-4 px-2 border-b-2 font-medium text-sm ${
                selectedTab === 'reports'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              } rtl-font`}
            >
              تقارير الأداء
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* Quality Issues Tab */}
          {selectedTab === 'issues' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-800 rtl-font">قضايا الجودة</h3>
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="open">مفتوح</option>
                    <option value="investigating">قيد التحقيق</option>
                    <option value="resolved">تم الحل</option>
                    <option value="closed">مغلق</option>
                  </select>
                </div>
              </div>

              <div className="space-y-4">
                {qualityIssues.map((issue) => (
                  <div key={issue.id} className="border border-gray-200 rounded-xl p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(issue.severity)}`}>
                            {issue.severity === 'critical' ? 'حرج' :
                             issue.severity === 'high' ? 'عالي' :
                             issue.severity === 'medium' ? 'متوسط' : 'منخفض'}
                          </span>
                          <span className="text-sm text-gray-600 rtl-font">#{issue.id}</span>
                          <span className="text-sm text-gray-600 rtl-font">
                            {issue.relatedEntity.type === 'restaurant' ? 'مطعم' :
                             issue.relatedEntity.type === 'driver' ? 'سائق' :
                             issue.relatedEntity.type === 'customer' ? 'عميل' : 'طلب'}: {issue.relatedEntity.name}
                          </span>
                        </div>
                        <h4 className="font-semibold text-gray-900 mb-2 rtl-font">{issue.title}</h4>
                        <p className="text-gray-700 mb-3 rtl-font">{issue.description}</p>
                        <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-600">
                          <span className="rtl-font">بلغ بواسطة: {issue.reportedBy}</span>
                          <span>{formatDateTime(issue.reportedAt).date}</span>
                          {issue.assignedTo && (
                            <span className="rtl-font">مُكلف إلى: {issue.assignedTo}</span>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          issue.status === 'open' ? 'bg-red-100 text-red-800' :
                          issue.status === 'investigating' ? 'bg-yellow-100 text-yellow-800' :
                          issue.status === 'resolved' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {issue.status === 'open' ? 'مفتوح' :
                           issue.status === 'investigating' ? 'قيد التحقيق' :
                           issue.status === 'resolved' ? 'تم الحل' : 'مغلق'}
                        </span>
                        <button className="text-blue-600 hover:text-blue-800" title="عرض التفاصيل">
                          <Eye className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {issue.resolution && (
                      <div className="mt-4 p-4 bg-green-50 rounded-lg">
                        <h5 className="font-medium text-green-800 mb-2 rtl-font">الحل:</h5>
                        <p className="text-green-700 rtl-font">{issue.resolution}</p>
                        {issue.resolvedAt && (
                          <p className="text-sm text-green-600 mt-2">
                            تم الحل في: {formatDateTime(issue.resolvedAt).date}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Performance Reports Tab */}
          {selectedTab === 'reports' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-800 rtl-font">تقارير الأداء</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium text-gray-800 rtl-font">توزيع التقييمات</h4>
                    <BarChart3 className="w-5 h-5 text-gray-400" />
                  </div>
                  <div className="space-y-3">
                    {[5, 4, 3, 2, 1].map((rating) => (
                      <div key={rating} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <span className="text-sm text-gray-600">{rating}</span>
                          <Star className="w-4 h-4 text-yellow-500" />
                        </div>
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-yellow-500 h-2 rounded-full"
                              style={{ width: `${rating === 5 ? 60 : rating === 4 ? 25 : rating === 3 ? 10 : rating === 2 ? 3 : 2}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium">
                            {rating === 5 ? '60%' : rating === 4 ? '25%' : rating === 3 ? '10%' : rating === 2 ? '3%' : '2%'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium text-gray-800 rtl-font">أداء الشهر</h4>
                    <PieChart className="w-5 h-5 text-gray-400" />
                  </div>
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">87.5%</div>
                      <div className="text-sm text-gray-600 rtl-font">معدل النجاح العام</div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-lg font-semibold text-blue-600">4.6</div>
                        <div className="text-xs text-gray-600 rtl-font">متوسط التقييم</div>
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-purple-600">28د</div>
                        <div className="text-xs text-gray-600 rtl-font">وقت التوصيل</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QualityControl;