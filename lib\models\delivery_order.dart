import 'order_status.dart';

/// Delivery order model containing all order information
class DeliveryOrder {
  final String id;
  final String restaurantName;
  final String restaurantAddress;
  final String customerName;
  final String customerAddress;
  final String customerPhone;
  final List<OrderItem> items;
  final double totalAmount;
  final double deliveryFee;
  final String estimatedTime;
  final double distance;
  OrderStatus status;
  final DateTime createdAt;
  DateTime? acceptedAt;
  DateTime? pickedUpAt;
  DateTime? deliveredAt;

  DeliveryOrder({
    required this.id,
    required this.restaurantName,
    required this.restaurantAddress,
    required this.customerName,
    required this.customerAddress,
    required this.customerPhone,
    required this.items,
    required this.totalAmount,
    required this.deliveryFee,
    required this.estimatedTime,
    required this.distance,
    this.status = OrderStatus.incoming,
    required this.createdAt,
    this.acceptedAt,
    this.pickedUpAt,
    this.deliveredAt,
  });

  /// Update order status and set appropriate timestamp
  void updateStatus(OrderStatus newStatus) {
    status = newStatus;
    final now = DateTime.now();
    
    switch (newStatus) {
      case OrderStatus.accepted:
        acceptedAt = now;
        break;
      case OrderStatus.pickedUp:
        pickedUpAt = now;
        break;
      case OrderStatus.delivered:
        deliveredAt = now;
        break;
      default:
        break;
    }
  }

  /// Get formatted total amount
  String get formattedTotal => '${totalAmount.toStringAsFixed(2)} SAR';
  
  /// Get formatted delivery fee
  String get formattedDeliveryFee => '${deliveryFee.toStringAsFixed(2)} SAR';
  
  /// Get formatted distance
  String get formattedDistance => '${distance.toStringAsFixed(1)} km';

  /// Get order summary for display
  String get orderSummary {
    if (items.length == 1) {
      return items.first.name;
    } else if (items.length <= 3) {
      return items.map((item) => item.name).join(', ');
    } else {
      return '${items.take(2).map((item) => item.name).join(', ')} +${items.length - 2} more';
    }
  }

  /// Create a sample order for demonstration
  static DeliveryOrder createSample() {
    return DeliveryOrder(
      id: 'ORD-${DateTime.now().millisecondsSinceEpoch}',
      restaurantName: 'Pizza Palace',
      restaurantAddress: 'King Fahd Road, Riyadh',
      customerName: 'Ahmed Al-Rashid',
      customerAddress: 'Al-Malaz District, Riyadh',
      customerPhone: '+966501234567',
      items: [
        OrderItem(name: 'Margherita Pizza', quantity: 1, price: 45.0),
        OrderItem(name: 'Caesar Salad', quantity: 1, price: 25.0),
        OrderItem(name: 'Coca Cola', quantity: 2, price: 8.0),
      ],
      totalAmount: 86.0,
      deliveryFee: 15.0,
      estimatedTime: '25 min',
      distance: 3.2,
      createdAt: DateTime.now(),
    );
  }
}

/// Individual item in an order
class OrderItem {
  final String name;
  final int quantity;
  final double price;

  OrderItem({
    required this.name,
    required this.quantity,
    required this.price,
  });

  /// Get formatted price
  String get formattedPrice => '${price.toStringAsFixed(2)} SAR';
  
  /// Get total price for this item
  double get totalPrice => price * quantity;
  
  /// Get formatted total price
  String get formattedTotalPrice => '${totalPrice.toStringAsFixed(2)} SAR';
}
