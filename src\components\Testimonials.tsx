import React, { useState, useEffect } from 'react';
import { Star, Quote, ChevronLeft, ChevronRight, Award, ThumbsUp } from 'lucide-react';

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: "فاطمة الزهراء",
      nameEn: "Fatima Al-Zahra",
      location: "الدار البيضاء",
      locationEn: "Casablanca",
      rating: 5,
      comment: "أفضل تطبيق لتوصيل الطعام في المغرب! الطعام دائماً طازج والتوصيل سريع جداً. تنوع المطاعم رائع ولا يمكن مقاومته.",
      commentEn: "The best food delivery app in Morocco! Always fresh, always on time. The variety of restaurants is incredible.",
      avatar: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      orderCount: 47,
      memberSince: "2023"
    },
    {
      id: 2,
      name: "أحمد بنعلي",
      nameEn: "Ahmed Benali",
      location: "الرباط",
      locationEn: "Rabat",
      rating: 5,
      comment: "وصلتي غير طريقة طلب الطعام بالنسبة لي. التطبيق سهل الاستخدام والتوصيل سريع بشكل لا يصدق! خدمة العملاء ممتازة.",
      commentEn: "Wasslti has changed how I order food. The app is so easy to use and the delivery is incredibly fast!",
      avatar: "https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      orderCount: 32,
      memberSince: "2023"
    },
    {
      id: 3,
      name: "عائشة منصوري",
      nameEn: "Aicha Mansouri",
      location: "مراكش",
      locationEn: "Marrakech",
      rating: 5,
      comment: "خدمة ممتازة! أحب كيف يمكنني تتبع طلبي في الوقت الفعلي. جودة الطعام دائماً في المستوى الأعلى وبرنامج الولاء رائع.",
      commentEn: "Excellent service! I love how I can track my order in real-time. The quality of food is always top-notch.",
      avatar: "https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      orderCount: 63,
      memberSince: "2022"
    },
    {
      id: 4,
      name: "يوسف الإدريسي",
      nameEn: "Youssef Idrissi",
      location: "فاس",
      locationEn: "Fez",
      rating: 5,
      comment: "تنوع مذهل من المطاعم والأطباق. برنامج الولاء رائع أيضاً - لقد وفرت الكثير من المال! التطبيق سريع وموثوق.",
      commentEn: "Amazing variety of restaurants and cuisines. The loyalty program is great too - I've saved so much money!",
      avatar: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      orderCount: 89,
      memberSince: "2022"
    },
    {
      id: 5,
      name: "سارة الحسني",
      nameEn: "Sara Al-Hasani",
      location: "طنجة",
      locationEn: "Tangier",
      rating: 5,
      comment: "كطالبة، وصلتي منقذي الحقيقي! أسعار معقولة، توصيل سريع، وخيارات دفع متنوعة. لا أستطيع العيش بدونه الآن.",
      commentEn: "As a student, Wasslti is my lifesaver! Reasonable prices, fast delivery, and various payment options.",
      avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      orderCount: 28,
      memberSince: "2023"
    },
    {
      id: 6,
      name: "محمد الكرامي",
      nameEn: "Mohammed Al-Karami",
      location: "أكادير",
      locationEn: "Agadir",
      rating: 5,
      comment: "خدمة احترافية من الطلب إلى التوصيل. السائقون مهذبون والطعام يصل ساخناً دائماً. أنصح به بشدة لكل العائلات.",
      commentEn: "Professional service from order to delivery. Drivers are polite and food always arrives hot.",
      avatar: "https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      orderCount: 156,
      memberSince: "2021"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const currentReview = testimonials[currentTestimonial];

  return (
    <section className="py-24 bg-gradient-to-br from-background via-white to-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-72 h-72 bg-orange-400/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <ThumbsUp className="w-4 h-4" />
            <span className="rtl-font">آراء العملاء</span>
          </div>
          <h2 className="text-5xl font-bold text-accent mb-6 rtl-font">
            ماذا يقول
            <br />
            <span className="text-primary">عملاؤنا السعداء</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed rtl-font">
            انضم إلى آلاف العملاء الراضين الذين يثقون في وصلتي لتلبية احتياجاتهم من توصيل الطعام.
          </p>
        </div>

        {/* Main Testimonial */}
        <div className="max-w-4xl mx-auto mb-16">
          <div className="bg-white rounded-3xl shadow-2xl p-8 md:p-12 relative overflow-hidden">
            {/* Background Quote */}
            <Quote className="absolute top-8 right-8 w-24 h-24 text-primary/10 transform rotate-12" />
            
            <div className="relative z-10">
              <div className="flex flex-col md:flex-row items-center gap-8">
                {/* Avatar */}
                <div className="relative">
                  <img 
                    src={currentReview.avatar} 
                    alt={currentReview.name}
                    className="w-24 h-24 rounded-full object-cover shadow-xl ring-4 ring-primary/20"
                  />
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                    <Award className="w-4 h-4 text-white" />
                  </div>
                </div>
                
                {/* Content */}
                <div className="flex-1 text-center md:text-right">
                  <div className="flex items-center justify-center md:justify-end mb-4">
                    {[...Array(currentReview.rating)].map((_, i) => (
                      <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  
                  <blockquote className="text-xl md:text-2xl text-gray-700 leading-relaxed mb-6 rtl-font">
                    "{currentReview.comment}"
                  </blockquote>
                  
                  <div className="flex items-center justify-center md:justify-end space-x-4 rtl:space-x-reverse">
                    <div className="text-right">
                      <h4 className="font-bold text-accent text-lg rtl-font">{currentReview.name}</h4>
                      <p className="text-gray-500 rtl-font">{currentReview.location}</p>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-400 mt-1">
                        <span>{currentReview.orderCount} طلب</span>
                        <span>•</span>
                        <span>عضو منذ {currentReview.memberSince}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Navigation */}
          <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mt-8">
            <button 
              onClick={prevTestimonial}
              className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-primary hover:shadow-xl transition-all duration-300"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
            
            <div className="flex space-x-2 rtl:space-x-reverse">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentTestimonial ? 'bg-primary scale-125' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
            
            <button 
              onClick={nextTestimonial}
              className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-primary hover:shadow-xl transition-all duration-300"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Star className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-primary mb-2">4.9</div>
            <div className="text-gray-600 rtl-font">متوسط التقييم</div>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <ThumbsUp className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-primary mb-2">98%</div>
            <div className="text-gray-600 rtl-font">رضا العملاء</div>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Award className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-primary mb-2">50K+</div>
            <div className="text-gray-600 rtl-font">تقييم إيجابي</div>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Quote className="w-8 h-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-primary mb-2">1M+</div>
            <div className="text-gray-600 rtl-font">عميل سعيد</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;