import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/favorite_item_card.dart';
import '../widgets/category_filter_widget.dart';

class FavoriteMenuScreen extends StatefulWidget {
  const FavoriteMenuScreen({super.key});

  @override
  State<FavoriteMenuScreen> createState() => _FavoriteMenuScreenState();
}

class _FavoriteMenuScreenState extends State<FavoriteMenuScreen> {
  String searchQuery = '';
  String selectedCategory = 'All';

  final List<String> categories = [
    'All',
    'Appetizers',
    'Main Course',
    'Desserts',
    'Beverages',
    'Salads'
  ];

  final List<FavoriteMenuItem> favoriteItems = [
    FavoriteMenuItem(
      id: '1',
      name: 'Grilled Chicken Breast',
      category: 'Main Course',
      price: 18.99,
      rating: 4.8,
      image: 'assets/images/chicken.png',
      description: 'Tender grilled chicken breast with herbs',
      preparationTime: '15 min',
      isAvailable: true,
      ingredients: ['Chicken', 'Herbs', 'Olive Oil', 'Garlic'],
    ),
    FavoriteMenuItem(
      id: '2',
      name: 'Caesar Salad',
      category: 'Salads',
      price: 12.99,
      rating: 4.6,
      image: 'assets/images/caesar_salad.png',
      description: 'Fresh romaine lettuce with caesar dressing',
      preparationTime: '8 min',
      isAvailable: true,
      ingredients: ['Romaine Lettuce', 'Parmesan', 'Croutons', 'Caesar Dressing'],
    ),
    FavoriteMenuItem(
      id: '3',
      name: 'Chocolate Lava Cake',
      category: 'Desserts',
      price: 8.99,
      rating: 4.9,
      image: 'assets/images/lava_cake.png',
      description: 'Warm chocolate cake with molten center',
      preparationTime: '12 min',
      isAvailable: false,
      ingredients: ['Dark Chocolate', 'Butter', 'Eggs', 'Flour'],
    ),
    FavoriteMenuItem(
      id: '4',
      name: 'Mozzarella Sticks',
      category: 'Appetizers',
      price: 9.99,
      rating: 4.5,
      image: 'assets/images/mozzarella_sticks.png',
      description: 'Crispy breaded mozzarella sticks',
      preparationTime: '10 min',
      isAvailable: true,
      ingredients: ['Mozzarella', 'Breadcrumbs', 'Flour', 'Eggs'],
    ),
    FavoriteMenuItem(
      id: '5',
      name: 'Fresh Orange Juice',
      category: 'Beverages',
      price: 4.99,
      rating: 4.7,
      image: 'assets/images/orange_juice.png',
      description: 'Freshly squeezed orange juice',
      preparationTime: '3 min',
      isAvailable: true,
      ingredients: ['Fresh Oranges'],
    ),
    FavoriteMenuItem(
      id: '6',
      name: 'Beef Burger',
      category: 'Main Course',
      price: 16.99,
      rating: 4.8,
      image: 'assets/images/beef_burger.png',
      description: 'Juicy beef burger with fresh vegetables',
      preparationTime: '18 min',
      isAvailable: true,
      ingredients: ['Beef Patty', 'Bun', 'Lettuce', 'Tomato', 'Cheese'],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: Column(
                    children: [
                      // Search and Filters
                      _buildSearchAndFilters(),
                      
                      // Favorite Items Grid
                      Expanded(
                        child: _buildFavoriteItemsGrid(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Icon(
                Icons.favorite,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Favorite Menu',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search favorite items...',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Add to Favorites Button
          ElevatedButton.icon(
            onPressed: () => _showAddToFavoritesDialog(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: const Icon(Icons.add, size: 18),
            label: Text(
              'Add Favorite',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(24),
      color: AppColors.backgroundLight,
      child: Row(
        children: [
          Text(
            'Categories:',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: CategoryFilterWidget(
              categories: categories,
              selectedCategory: selectedCategory,
              onCategorySelected: (category) {
                setState(() {
                  selectedCategory = category;
                });
              },
            ),
          ),
          
          // Stats
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: AppColors.inputBorder),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.favorite,
                  color: AppColors.primary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${favoriteItems.length} Favorites',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteItemsGrid() {
    final filteredItems = _getFilteredItems();
    
    if (filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No favorite items found',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add items to your favorites to see them here',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 0.85,
          crossAxisSpacing: 20,
          mainAxisSpacing: 20,
        ),
        itemCount: filteredItems.length,
        itemBuilder: (context, index) {
          final item = filteredItems[index];
          return FavoriteItemCard(
            item: item,
            onRemoveFromFavorites: () => _removeFromFavorites(item.id),
            onAddToMenu: () => _addToMenu(item),
          );
        },
      ),
    );
  }

  List<FavoriteMenuItem> _getFilteredItems() {
    List<FavoriteMenuItem> filtered = favoriteItems;

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((item) =>
          item.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().contains(searchQuery.toLowerCase())).toList();
    }

    // Apply category filter
    if (selectedCategory != 'All') {
      filtered = filtered.where((item) => item.category == selectedCategory).toList();
    }

    return filtered;
  }

  void _removeFromFavorites(String itemId) {
    setState(() {
      favoriteItems.removeWhere((item) => item.id == itemId);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Item removed from favorites',
          style: GoogleFonts.inter(),
        ),
        backgroundColor: AppColors.error,
      ),
    );
  }

  void _addToMenu(FavoriteMenuItem item) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${item.name} added to today\'s menu',
          style: GoogleFonts.inter(),
        ),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showAddToFavoritesDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Add to Favorites',
          style: GoogleFonts.inter(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Browse your menu items and mark them as favorites to add them here.',
          style: GoogleFonts.inter(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'OK',
              style: GoogleFonts.inter(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class FavoriteMenuItem {
  final String id;
  final String name;
  final String category;
  final double price;
  final double rating;
  final String image;
  final String description;
  final String preparationTime;
  final bool isAvailable;
  final List<String> ingredients;

  FavoriteMenuItem({
    required this.id,
    required this.name,
    required this.category,
    required this.price,
    required this.rating,
    required this.image,
    required this.description,
    required this.preparationTime,
    required this.isAvailable,
    required this.ingredients,
  });
}
