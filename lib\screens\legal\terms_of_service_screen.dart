import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Terms of Service screen
class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Terms of Service', 'شروط الخدمة'),
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Center(
                  child: Column(
                    children: [
                      const Icon(
                        Icons.description,
                        size: 60,
                        color: Color(0xFF11B96F),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        languageProvider.getText('Terms of Service', 'شروط الخدمة'),
                        style: DriverTypography.getContextualStyle(
                          context,
                          fontSize: DriverTypography.titleLarge,
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        languageProvider.getText(
                          'Last updated: December 7, 2024',
                          'آخر تحديث: 7 ديسمبر 2024'
                        ),
                        style: DriverTypography.getContextualStyle(
                          context,
                          fontSize: DriverTypography.bodyMedium,
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 30),
                
                // Terms content
                _buildSection(
                  title: languageProvider.getText('1. Acceptance of Terms', '1. قبول الشروط'),
                  content: languageProvider.getText(
                    'By using the Wasslti Partner application, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our service.',
                    'باستخدام تطبيق شريك وصلتي، فإنك توافق على الالتزام بشروط الخدمة هذه. إذا كنت لا توافق على هذه الشروط، يرجى عدم استخدام خدمتنا.'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('2. Driver Requirements', '2. متطلبات السائق'),
                  content: languageProvider.getText(
                    'To become a Wasslti Partner driver, you must:\n• Be at least 21 years old\n• Have a valid driver\'s license\n• Own or have access to a suitable vehicle\n• Pass background checks\n• Maintain required insurance coverage',
                    'لتصبح سائق شريك وصلتي، يجب أن:\n• تكون عمرك 21 سنة على الأقل\n• تمتلك رخصة قيادة سارية\n• تمتلك أو تحصل على مركبة مناسبة\n• تجتاز فحوصات الخلفية\n• تحافظ على تغطية التأمين المطلوبة'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('3. Service Obligations', '3. التزامات الخدمة'),
                  content: languageProvider.getText(
                    'As a driver, you agree to:\n• Provide safe and reliable transportation\n• Maintain professional conduct\n• Follow all traffic laws and regulations\n• Keep your vehicle clean and well-maintained\n• Respect customer privacy and property',
                    'كسائق، توافق على:\n• توفير نقل آمن وموثوق\n• الحفاظ على السلوك المهني\n• اتباع جميع قوانين وأنظمة المرور\n• الحفاظ على نظافة مركبتك وصيانتها\n• احترام خصوصية العملاء وممتلكاتهم'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('4. Payment Terms', '4. شروط الدفع'),
                  content: languageProvider.getText(
                    'Payment for completed trips will be processed according to our payment schedule. Wasslti reserves the right to deduct applicable fees, taxes, and commissions from driver earnings.',
                    'سيتم معالجة الدفع للرحلات المكتملة وفقاً لجدول الدفع الخاص بنا. تحتفظ وصلتي بالحق في خصم الرسوم والضرائب والعمولات المطبقة من أرباح السائق.'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('5. Termination', '5. الإنهاء'),
                  content: languageProvider.getText(
                    'Either party may terminate this agreement at any time with or without cause. Upon termination, you must immediately stop using the Wasslti Partner application and return any company property.',
                    'يجوز لأي من الطرفين إنهاء هذه الاتفاقية في أي وقت مع أو بدون سبب. عند الإنهاء، يجب عليك التوقف فوراً عن استخدام تطبيق شريك وصلتي وإرجاع أي ممتلكات للشركة.'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('6. Limitation of Liability', '6. تحديد المسؤولية'),
                  content: languageProvider.getText(
                    'Wasslti\'s liability is limited to the maximum extent permitted by law. We are not responsible for any indirect, incidental, or consequential damages arising from your use of our service.',
                    'مسؤولية وصلتي محدودة إلى أقصى حد يسمح به القانون. نحن لسنا مسؤولين عن أي أضرار غير مباشرة أو عرضية أو تبعية تنشأ من استخدامك لخدمتنا.'
                  ),
                  isDark: isDark,
                ),
                
                _buildSection(
                  title: languageProvider.getText('7. Contact Information', '7. معلومات الاتصال'),
                  content: languageProvider.getText(
                    'If you have any questions about these Terms of Service, please contact us at:\n\nEmail: <EMAIL>\nPhone: +966 123 456 789\nAddress: Riyadh, Saudi Arabia',
                    'إذا كان لديك أي أسئلة حول شروط الخدمة هذه، يرجى الاتصال بنا على:\n\nالبريد الإلكتروني: <EMAIL>\nالهاتف: +966 123 456 789\nالعنوان: الرياض، المملكة العربية السعودية'
                  ),
                  isDark: isDark,
                ),
                
                const SizedBox(height: 30),
                
                // Agreement checkbox
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0xFF11B96F).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: const Color(0xFF11B96F),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          languageProvider.getText(
                            'By continuing to use Wasslti Partner, you acknowledge that you have read, understood, and agree to these Terms of Service.',
                            'من خلال الاستمرار في استخدام شريك وصلتي، فإنك تقر بأنك قد قرأت وفهمت ووافقت على شروط الخدمة هذه.'
                          ),
                          style: DriverTypography.getContextualStyle(
                            context,
                            fontSize: DriverTypography.bodyMedium,
                            color: const Color(0xFF11B96F),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    required bool isDark,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: DriverTypography.titleMedium,
              fontWeight: FontWeight.bold,
              color: Color(0xFF11B96F),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: TextStyle(
              fontSize: DriverTypography.bodyMedium,
              color: isDark ? Colors.grey[300] : Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
