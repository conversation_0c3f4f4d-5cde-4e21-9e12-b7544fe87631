import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/driver_status.dart';
import '../providers/language_provider.dart';

/// Simple floating top bar for driver home screen
/// Contains settings button, driver status, and support button
class FloatingTopBar extends StatefulWidget {
  final DriverStatus driverStatus;
  final VoidCallback onStatusTap;
  final VoidCallback onSettingsTap;
  final VoidCallback onSupportTap;

  const FloatingTopBar({
    super.key,
    required this.driverStatus,
    required this.onStatusTap,
    required this.onSettingsTap,
    required this.onSupportTap,
  });

  @override
  State<FloatingTopBar> createState() => _FloatingTopBarState();
}

class _FloatingTopBarState extends State<FloatingTopBar> {

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // اليسار - زر الإعدادات
          _buildSettingsButton(isDark),

          // الوسط - حالة السائق
          Expanded(
            child: Center(
              child: _buildStatusCard(isDark),
            ),
          ),

          // اليمين - زر الدعم
          _buildSupportButton(isDark),
        ],
      ),
    );
  }

  /// زر الإعدادات (ترس)
  Widget _buildSettingsButton(bool isDark) {
    return GestureDetector(
      onTap: () {
        print('🔧 Settings button tapped');
        widget.onSettingsTap();
      },
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          Icons.settings,
          color: const Color(0xFF11B96F),
          size: 24,
        ),
      ),
    );
  }

  /// كارت حالة السائق
  Widget _buildStatusCard(bool isDark) {
    return GestureDetector(
      onTap: widget.onStatusTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر الحالة
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: _getStatusColor(),
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            
            const SizedBox(width: 8),
            
            // نص الحالة
            Text(
              _getStatusText(),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// زر الدعم
  Widget _buildSupportButton(bool isDark) {
    return GestureDetector(
      onTap: widget.onSupportTap,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          Icons.headset_mic,
          color: const Color(0xFF11B96F),
          size: 24,
        ),
      ),
    );
  }

  /// الحصول على لون حالة السائق
  Color _getStatusColor() {
    switch (widget.driverStatus) {
      case DriverStatus.working:
        return const Color(0xFF11B96F); // أخضر
      case DriverStatus.resting:
        return const Color(0xFFFFA726); // برتقالي
      case DriverStatus.stopped:
        return const Color(0xFFEF5350); // أحمر
    }
  }

  /// الحصول على نص حالة السائق
  String _getStatusText() {
    final languageProvider = context.read<LanguageProvider>();
    switch (widget.driverStatus) {
      case DriverStatus.working:
        return languageProvider.statusWorking;
      case DriverStatus.resting:
        return languageProvider.statusResting;
      case DriverStatus.stopped:
        return languageProvider.statusStopped;
    }
  }
}
