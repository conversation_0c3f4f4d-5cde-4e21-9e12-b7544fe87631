import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class OrderHistoryWidget extends StatefulWidget {
  const OrderHistoryWidget({super.key});

  @override
  State<OrderHistoryWidget> createState() => _OrderHistoryWidgetState();
}

class _OrderHistoryWidgetState extends State<OrderHistoryWidget> {
  String selectedStatus = 'All';
  
  final List<String> statusFilters = ['All', 'Pending', 'Confirmed', 'Delivered', 'Cancelled'];
  
  final List<BulkOrder> orders = [
    BulkOrder(
      id: 'BO-001',
      date: DateTime.now().subtract(const Duration(days: 1)),
      status: BulkOrderStatus.delivered,
      items: [
        BulkOrderItem(name: 'Fresh Tomatoes', quantity: '10kg', price: 25.99),
        BulkOrderItem(name: 'Premium Beef', quantity: '5kg', price: 89.99),
      ],
      total: 115.98,
    ),
    BulkOrder(
      id: 'BO-002',
      date: DateTime.now().subtract(const Duration(days: 3)),
      status: BulkOrderStatus.confirmed,
      items: [
        BulkOrderItem(name: 'Food Containers', quantity: '100 pieces', price: 15.99),
        BulkOrderItem(name: 'Chef Knives Set', quantity: '5 pieces', price: 45.99),
      ],
      total: 61.98,
    ),
    BulkOrder(
      id: 'BO-003',
      date: DateTime.now().subtract(const Duration(days: 7)),
      status: BulkOrderStatus.pending,
      items: [
        BulkOrderItem(name: 'Fresh Onions', quantity: '8kg', price: 18.99),
      ],
      total: 18.99,
    ),
    BulkOrder(
      id: 'BO-004',
      date: DateTime.now().subtract(const Duration(days: 14)),
      status: BulkOrderStatus.cancelled,
      items: [
        BulkOrderItem(name: 'Soft Drinks', quantity: '24 cans', price: 12.99),
      ],
      total: 12.99,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFF0F231A),
      child: Column(
        children: [
          // Filters
          _buildFilters(),
          
          // Orders List
          Expanded(
            child: _buildOrdersList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          Text(
            'Filter by Status:',
            style: GoogleFonts.tajawal(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: statusFilters.map((status) {
                  final isSelected = selectedStatus == status;
                  return Container(
                    margin: const EdgeInsets.only(right: 12),
                    child: FilterChip(
                      label: Text(
                        status,
                        style: GoogleFonts.tajawal(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: isSelected ? const Color(0xFF0F231A) : Colors.white,
                        ),
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          selectedStatus = status;
                        });
                      },
                      backgroundColor: const Color(0xFF1B3B2E),
                      selectedColor: const Color(0xFF11B96F),
                      checkmarkColor: const Color(0xFF0F231A),
                      side: BorderSide(
                        color: isSelected 
                            ? const Color(0xFF11B96F) 
                            : const Color(0xFF11B96F).withValues(alpha: 0.3),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    final filteredOrders = _getFilteredOrders();
    
    if (filteredOrders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 16),
            Text(
              'No orders found',
              style: GoogleFonts.tajawal(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your bulk supply orders will appear here',
              style: GoogleFonts.tajawal(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      itemCount: filteredOrders.length,
      itemBuilder: (context, index) {
        final order = filteredOrders[index];
        return _buildOrderCard(order);
      },
    );
  }

  Widget _buildOrderCard(BulkOrder order) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1B3B2E),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF11B96F).withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order ${order.id}',
                      style: GoogleFonts.tajawal(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatDate(order.date),
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        color: Colors.grey[400],
                      ),
                    ),
                  ],
                ),
                
                // Status Badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(order.status).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getStatusColor(order.status),
                    ),
                  ),
                  child: Text(
                    _getStatusText(order.status),
                    style: GoogleFonts.tajawal(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: _getStatusColor(order.status),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Order Items
            Column(
              children: order.items.map((item) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF0F231A),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.name,
                            style: GoogleFonts.tajawal(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            item.quantity,
                            style: GoogleFonts.tajawal(
                              fontSize: 12,
                              color: const Color(0xFF11B96F),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '\$${item.price.toStringAsFixed(2)}',
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // Footer
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total: \$${order.total.toStringAsFixed(2)}',
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF11B96F),
                  ),
                ),
                
                if (order.status == BulkOrderStatus.delivered)
                  ElevatedButton(
                    onPressed: () => _reorderItems(order),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF11B96F),
                      foregroundColor: const Color(0xFF0F231A),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.refresh, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          'Reorder',
                          style: GoogleFonts.tajawal(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<BulkOrder> _getFilteredOrders() {
    if (selectedStatus == 'All') {
      return orders;
    }
    
    final status = _getStatusFromString(selectedStatus);
    return orders.where((order) => order.status == status).toList();
  }

  BulkOrderStatus _getStatusFromString(String status) {
    switch (status) {
      case 'Pending':
        return BulkOrderStatus.pending;
      case 'Confirmed':
        return BulkOrderStatus.confirmed;
      case 'Delivered':
        return BulkOrderStatus.delivered;
      case 'Cancelled':
        return BulkOrderStatus.cancelled;
      default:
        return BulkOrderStatus.pending;
    }
  }

  Color _getStatusColor(BulkOrderStatus status) {
    switch (status) {
      case BulkOrderStatus.pending:
        return Colors.orange;
      case BulkOrderStatus.confirmed:
        return Colors.blue;
      case BulkOrderStatus.delivered:
        return const Color(0xFF11B96F);
      case BulkOrderStatus.cancelled:
        return Colors.red;
    }
  }

  String _getStatusText(BulkOrderStatus status) {
    switch (status) {
      case BulkOrderStatus.pending:
        return 'Pending';
      case BulkOrderStatus.confirmed:
        return 'Confirmed';
      case BulkOrderStatus.delivered:
        return 'Delivered';
      case BulkOrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '$difference days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _reorderItems(BulkOrder order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1B3B2E),
        title: Text(
          'Reorder Items',
          style: GoogleFonts.tajawal(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Do you want to add all items from order ${order.id} to your cart?',
          style: GoogleFonts.tajawal(
            color: Colors.grey[300],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.tajawal(
                color: Colors.grey[400],
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Handle reorder logic here
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Items added to cart successfully!',
                    style: GoogleFonts.tajawal(),
                  ),
                  backgroundColor: const Color(0xFF11B96F),
                ),
              );
            },
            child: Text(
              'Reorder',
              style: GoogleFonts.tajawal(
                color: const Color(0xFF11B96F),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

enum BulkOrderStatus { pending, confirmed, delivered, cancelled }

class BulkOrder {
  final String id;
  final DateTime date;
  final BulkOrderStatus status;
  final List<BulkOrderItem> items;
  final double total;

  BulkOrder({
    required this.id,
    required this.date,
    required this.status,
    required this.items,
    required this.total,
  });
}

class BulkOrderItem {
  final String name;
  final String quantity;
  final double price;

  BulkOrderItem({
    required this.name,
    required this.quantity,
    required this.price,
  });
}
