import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Third-party licenses and attributions screen
class LicensesScreen extends StatefulWidget {
  const LicensesScreen({super.key});

  @override
  State<LicensesScreen> createState() => _LicensesScreenState();
}

class _LicensesScreenState extends State<LicensesScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Licenses', 'التراخيص'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Header
                _buildHeader(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Flutter Licenses
                _buildFlutterLicenses(isDark, languageProvider),
                
                const SizedBox(height: 16),
                
                // Third-party Packages
                _buildThirdPartyPackages(isDark, languageProvider),
                
                const SizedBox(height: 16),
                
                // Assets and Resources
                _buildAssetsLicenses(isDark, languageProvider),
                
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.indigo[800]!,
            Colors.indigo[600]!,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.indigo.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.copyright,
            size: 60,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('Third-Party Licenses', 'تراخيص الطرف الثالث'),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Open source libraries and resources used in this application',
              'المكتبات والموارد مفتوحة المصدر المستخدمة في هذا التطبيق'
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.favorite,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  languageProvider.getText('Built with Open Source', 'مبني بالمصادر المفتوحة'),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlutterLicenses(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.flutter_dash,
                  color: Colors.blue[600],
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                languageProvider.getText('Flutter Framework', 'إطار عمل Flutter'),
                style: TextStyle(
                  fontSize: DriverTypography.titleMedium,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          _buildLicenseItem(
            name: 'Flutter',
            version: '3.16.0',
            license: 'BSD 3-Clause License',
            description: languageProvider.getText(
              'Google\'s UI toolkit for building natively compiled applications',
              'مجموعة أدوات واجهة المستخدم من Google لبناء التطبيقات المترجمة محلياً'
            ),
            url: 'https://flutter.dev',
            isDark: isDark,
          ),
          
          _buildLicenseItem(
            name: 'Dart',
            version: '3.2.0',
            license: 'BSD 3-Clause License',
            description: languageProvider.getText(
              'Client-optimized programming language for fast apps',
              'لغة برمجة محسنة للعميل للتطبيقات السريعة'
            ),
            url: 'https://dart.dev',
            isDark: isDark,
          ),
        ],
      ),
    );
  }

  Widget _buildThirdPartyPackages(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.extension,
                  color: Colors.green[600],
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                languageProvider.getText('Third-Party Packages', 'حزم الطرف الثالث'),
                style: TextStyle(
                  fontSize: DriverTypography.titleMedium,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          _buildLicenseItem(
            name: 'provider',
            version: '^6.1.1',
            license: 'MIT License',
            description: languageProvider.getText(
              'State management library for Flutter applications',
              'مكتبة إدارة الحالة لتطبيقات Flutter'
            ),
            url: 'https://pub.dev/packages/provider',
            isDark: isDark,
          ),
          
          _buildLicenseItem(
            name: 'shared_preferences',
            version: '^2.2.2',
            license: 'BSD 3-Clause License',
            description: languageProvider.getText(
              'Flutter plugin for reading and writing simple key-value pairs',
              'إضافة Flutter لقراءة وكتابة أزواج المفاتيح والقيم البسيطة'
            ),
            url: 'https://pub.dev/packages/shared_preferences',
            isDark: isDark,
          ),
          
          _buildLicenseItem(
            name: 'geolocator',
            version: '^10.1.0',
            license: 'MIT License',
            description: languageProvider.getText(
              'Geolocation plugin for Flutter applications',
              'إضافة تحديد الموقع الجغرافي لتطبيقات Flutter'
            ),
            url: 'https://pub.dev/packages/geolocator',
            isDark: isDark,
          ),
          
          _buildLicenseItem(
            name: 'url_launcher',
            version: '^6.2.2',
            license: 'BSD 3-Clause License',
            description: languageProvider.getText(
              'Flutter plugin for launching URLs in mobile platform',
              'إضافة Flutter لفتح الروابط في المنصات المحمولة'
            ),
            url: 'https://pub.dev/packages/url_launcher',
            isDark: isDark,
          ),
        ],
      ),
    );
  }

  Widget _buildAssetsLicenses(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.image,
                  color: Colors.purple[600],
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                languageProvider.getText('Assets & Resources', 'الأصول والموارد'),
                style: TextStyle(
                  fontSize: DriverTypography.titleMedium,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          _buildLicenseItem(
            name: 'Material Design Icons',
            version: '7.0.96',
            license: 'Apache License 2.0',
            description: languageProvider.getText(
              'Material Design icons by Google',
              'أيقونات Material Design من Google'
            ),
            url: 'https://fonts.google.com/icons',
            isDark: isDark,
          ),
          
          _buildLicenseItem(
            name: 'Roboto Font',
            version: '2.137',
            license: 'Apache License 2.0',
            description: languageProvider.getText(
              'Google\'s signature family of fonts',
              'عائلة الخطوط المميزة من Google'
            ),
            url: 'https://fonts.google.com/specimen/Roboto',
            isDark: isDark,
          ),
          
          const SizedBox(height: 20),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.indigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.indigo.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info,
                      color: Colors.indigo[600],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      languageProvider.getText('License Information', 'معلومات الترخيص'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.indigo[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  languageProvider.getText(
                    'All third-party libraries and resources are used in accordance with their respective licenses. Full license texts are available in the source code repository.',
                    'جميع مكتبات وموارد الطرف الثالث مستخدمة وفقاً لتراخيصها المعنية. نصوص التراخيص الكاملة متاحة في مستودع الكود المصدري.'
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.indigo[600],
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  languageProvider.getText(
                    'For questions about licensing, contact: <EMAIL>',
                    'للأسئلة حول الترخيص، اتصل بـ: <EMAIL>'
                  ),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.indigo[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLicenseItem({
    required String name,
    required String version,
    required String license,
    required String description,
    required String url,
    required bool isDark,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF0F231A) : const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  version,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF11B96F),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              fontSize: 14,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.gavel,
                size: 16,
                color: isDark ? Colors.grey[500] : Colors.grey[500],
              ),
              const SizedBox(width: 6),
              Text(
                license,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
