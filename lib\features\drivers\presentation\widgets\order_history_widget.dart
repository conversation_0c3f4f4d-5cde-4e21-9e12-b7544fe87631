import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';

class OrderHistoryWidget extends StatelessWidget {
  const OrderHistoryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Order History',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Table Header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'Customer',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Total',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Address',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Status',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 40), // For actions
              ],
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Order Rows
          _buildOrderRow(
            orderId: 'Order #1',
            date: 'April 15, 2022',
            customerName: 'Calvin',
            customerInfo: 'User since 2023',
            avatar: 'C',
            total: '\$14.81',
            address: '8558 Green Rd.',
            status: 'Delivered',
            statusColor: Colors.orange,
          ),
          
          _buildOrderRow(
            orderId: 'Order #2',
            date: 'October 25, 2019',
            customerName: 'Marvin',
            customerInfo: 'User since 2023',
            avatar: 'M',
            total: '\$17.50',
            address: '3605 Parker Rd.',
            status: 'Completed',
            statusColor: AppColors.success,
          ),
          
          _buildOrderRow(
            orderId: 'Order #3',
            date: 'May 25, 2019',
            customerName: 'Brandon',
            customerInfo: 'User since 2023',
            avatar: 'B',
            total: '\$14.81',
            address: '2715 Ash Dr.',
            status: 'Cancelled',
            statusColor: AppColors.error,
          ),
          
          _buildOrderRow(
            orderId: 'Order #4',
            date: 'May 25, 2019',
            customerName: 'Randall',
            customerInfo: 'User since 2023',
            avatar: 'R',
            total: '\$50.50',
            address: '2715 Ash Dr.',
            status: 'Completed',
            statusColor: AppColors.success,
          ),
          
          _buildOrderRow(
            orderId: 'Order #5',
            date: 'May 12, 2019',
            customerName: 'Daniel',
            customerInfo: 'User since 2023',
            avatar: 'D',
            total: '\$10.50',
            address: '8558 Green Rd.',
            status: 'Delivered',
            statusColor: Colors.orange,
          ),
          
          _buildOrderRow(
            orderId: 'Order #6',
            date: 'February 9, 2019',
            customerName: 'Eduardo',
            customerInfo: 'User since 2023',
            avatar: 'E',
            total: '\$17.50',
            address: '7529 E. Pecan St.',
            status: 'Delivered',
            statusColor: Colors.orange,
          ),
          
          _buildOrderRow(
            orderId: 'Order #7',
            date: 'March 15, 2017',
            customerName: 'Floyd',
            customerInfo: 'User since 2023',
            avatar: 'F',
            total: '\$14.81',
            address: '2464 Royal Ln.',
            status: 'Completed',
            statusColor: AppColors.success,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderRow({
    required String orderId,
    required String date,
    required String customerName,
    required String customerInfo,
    required String avatar,
    required String total,
    required String address,
    required String status,
    required Color statusColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Customer Info
          Expanded(
            flex: 2,
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: AppColors.primary,
                  child: Text(
                    avatar,
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      customerName,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      customerInfo,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Total
          Expanded(
            flex: 1,
            child: Text(
              total,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          
          // Address
          Expanded(
            flex: 2,
            child: Text(
              address,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          // Status
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                status,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: statusColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          
          // Actions
          SizedBox(
            width: 40,
            child: IconButton(
              onPressed: () {},
              icon: Icon(
                Icons.more_vert,
                color: AppColors.textSecondary,
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
