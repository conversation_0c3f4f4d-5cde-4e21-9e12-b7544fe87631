import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'constants/driver_themes.dart';
import 'providers/auth_provider.dart';
import 'providers/language_provider.dart';
import 'utils/settings_helper.dart';

/// Test settings navigation with language switching
void main() {
  runApp(const TestSettingsNavigationApp());
}

class TestSettingsNavigationApp extends StatelessWidget {
  const TestSettingsNavigationApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
      ],
      child: MaterialApp(
        title: 'Test Settings Navigation',
        debugShowCheckedModeBanner: false,
        theme: DriverThemes.lightTheme,
        darkTheme: DriverThemes.darkTheme,
        themeMode: ThemeMode.system,
        home: const TestSettingsNavigationHome(),
      ),
    );
  }
}

class TestSettingsNavigationHome extends StatefulWidget {
  const TestSettingsNavigationHome({super.key});

  @override
  State<TestSettingsNavigationHome> createState() => _TestSettingsNavigationHomeState();
}

class _TestSettingsNavigationHomeState extends State<TestSettingsNavigationHome> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LanguageProvider>().initializeLanguage();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: Text(
          languageProvider.getText('Settings Navigation Test', 'اختبار التنقل للإعدادات'),
        ),
        backgroundColor: const Color(0xFF11B96F),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.settings,
                size: 80,
                color: Color(0xFF11B96F),
              ),
              const SizedBox(height: 20),
              Text(
                languageProvider.getText(
                  'Settings Navigation Test',
                  'اختبار التنقل للإعدادات'
                ),
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                languageProvider.getText(
                  'Current Language: ${languageProvider.currentLanguage}',
                  'اللغة الحالية: ${languageProvider.currentLanguage}'
                ),
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              
              // Change Language Button
              SizedBox(
                width: double.infinity,
                height: 60,
                child: ElevatedButton.icon(
                  onPressed: () {
                    final newLang = languageProvider.isEnglish ? 'العربية' : 'English';
                    languageProvider.changeLanguage(newLang);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: const Icon(Icons.language),
                  label: Text(
                    languageProvider.getText(
                      'Switch to Arabic',
                      'التبديل للإنجليزية'
                    ),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Open Settings Button
              SizedBox(
                width: double.infinity,
                height: 60,
                child: ElevatedButton.icon(
                  onPressed: () {
                    SettingsHelper.navigateToSettingsWithProvider(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF11B96F),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: const Icon(Icons.settings),
                  label: Text(
                    languageProvider.getText(
                      'Open Settings',
                      'فتح الإعدادات'
                    ),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Instructions
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDark ? const Color(0xFF1B3B2E) : Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      languageProvider.getText(
                        'Test Instructions:',
                        'تعليمات الاختبار:'
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      languageProvider.getText(
                        '1. Change language here\n2. Open settings\n3. Settings should open in the correct language\n4. Change language in settings\n5. Exit and return to settings\n6. Settings should remember the language',
                        '1. غير اللغة هنا\n2. افتح الإعدادات\n3. يجب أن تفتح الإعدادات باللغة الصحيحة\n4. غير اللغة في الإعدادات\n5. اخرج وعد للإعدادات\n6. يجب أن تتذكر الإعدادات اللغة'
                      ),
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
