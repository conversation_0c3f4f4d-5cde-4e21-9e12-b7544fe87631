<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>وصلتي - توصيل الطعام في المغرب | Wasslti - Food Delivery in Morocco</title>
    <meta name="description" content="اطلب طعامك المفضل أونلاين من أفضل المطاعم في المغرب. توصيل سريع، أسعار رائعة، ونكهات أصيلة تصل إلى باب منزلك. Order food online from the best restaurants in Morocco." />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="وصلتي - توصيل الطعام في المغرب" />
    <meta property="og:description" content="اطلب طعامك المفضل أونلاين من أفضل المطاعم في المغرب. توصيل سريع وآمن." />
    <meta property="og:type" content="website" />
    <meta property="og:locale" content="ar_MA" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="وصلتي - توصيل الطعام في المغرب" />
    <meta name="twitter:description" content="اطلب طعامك المفضل أونلاين من أفضل المطاعم في المغرب." />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Tajawal:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "وصلتي",
      "alternateName": "Wasslti",
      "url": "https://wasslti.com",
      "logo": "https://wasslti.com/logo.png",
      "description": "منصة المغرب الرائدة لتوصيل الطعام",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "MA",
        "addressLocality": "الدار البيضاء"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+212-5XX-XXXXXX",
        "contactType": "customer service",
        "availableLanguage": ["Arabic", "French", "English"]
      }
    }
    </script>
    
    <!-- RTL and Font Styles -->
    <style>
      :root {
        --primary: #11B96F;
        --background: #F7F9F8;
        --accent: #0F231A;
      }
      
      [dir="rtl"] {
        direction: rtl;
      }
      
      [dir="ltr"] {
        direction: ltr;
      }
      
      .rtl-font {
        font-family: 'Tajawal', 'Inter', sans-serif;
        font-feature-settings: 'liga' 1, 'kern' 1;
      }
      
      body {
        font-family: 'Inter', 'Tajawal', sans-serif;
        line-height: 1.6;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      /* Smooth scrolling */
      html {
        scroll-behavior: smooth;
      }
      
      /* Loading animation */
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--background);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading after page loads */
      .loaded .loading {
        display: none;
      }
      
      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      
      ::-webkit-scrollbar-thumb {
        background: var(--primary);
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #0ea05e;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div class="loading">
      <div class="loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Page Load Script -->
    <script>
      window.addEventListener('load', function() {
        document.body.classList.add('loaded');
      });
    </script>
  </body>
</html>