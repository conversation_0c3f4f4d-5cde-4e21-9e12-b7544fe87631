import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';
import 'terms_of_service_screen.dart';
import 'privacy_policy_screen.dart';

/// Legal information screen
class LegalScreen extends StatelessWidget {
  const LegalScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Legal Information', 'المعلومات القانونية'),
            style: DriverTypography.getContextualStyle(
              context,
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header
              _buildHeader(isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // Legal Documents
              _buildLegalDocuments(context, isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // App Information
              _buildAppInformation(isDark, languageProvider),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.gavel,
            size: 60,
            color: Color(0xFF11B96F),
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('Legal Information', 'المعلومات القانونية'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Important legal documents and policies for Wasslti Partner drivers.',
              'الوثائق والسياسات القانونية المهمة لسائقي شريك وصلتي.'
            ),
            style: TextStyle(
              fontSize: DriverTypography.bodyMedium,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLegalDocuments(BuildContext context, bool isDark, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageProvider.getText('Legal Documents', 'الوثائق القانونية'),
          style: DriverTypography.getContextualStyle(
            context,
            fontSize: DriverTypography.titleMedium,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        
        // Terms of Service
        _buildLegalItem(
          context: context,
          icon: Icons.description,
          title: languageProvider.getText('Terms of Service', 'شروط الخدمة'),
          subtitle: languageProvider.getText(
            'Terms and conditions for using Wasslti Partner',
            'الشروط والأحكام لاستخدام شريك وصلتي'
          ),
          onTap: () => Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const TermsOfServiceScreen(),
            ),
          ),
          isDark: isDark,
        ),
        
        const SizedBox(height: 12),
        
        // Privacy Policy
        _buildLegalItem(
          context: context,
          icon: Icons.privacy_tip,
          title: languageProvider.getText('Privacy Policy', 'سياسة الخصوصية'),
          subtitle: languageProvider.getText(
            'How we collect, use, and protect your data',
            'كيف نجمع ونستخدم ونحمي بياناتك'
          ),
          onTap: () => Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const PrivacyPolicyScreen(),
            ),
          ),
          isDark: isDark,
        ),
        
        const SizedBox(height: 12),
        
        // Driver Agreement
        _buildLegalItem(
          context: context,
          icon: Icons.handshake,
          title: languageProvider.getText('Driver Agreement', 'اتفاقية السائق'),
          subtitle: languageProvider.getText(
            'Partnership agreement for drivers',
            'اتفاقية الشراكة للسائقين'
          ),
          onTap: () => _showComingSoon(context, languageProvider),
          isDark: isDark,
        ),
        
        const SizedBox(height: 12),
        
        // Code of Conduct
        _buildLegalItem(
          context: context,
          icon: Icons.rule,
          title: languageProvider.getText('Code of Conduct', 'قواعد السلوك'),
          subtitle: languageProvider.getText(
            'Professional standards and behavior guidelines',
            'المعايير المهنية وإرشادات السلوك'
          ),
          onTap: () => _showComingSoon(context, languageProvider),
          isDark: isDark,
        ),
      ],
    );
  }

  Widget _buildLegalItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: const Color(0xFF11B96F).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: const Color(0xFF11B96F),
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: DriverTypography.getContextualStyle(
                      context,
                      fontSize: DriverTypography.bodyLarge,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: DriverTypography.getContextualStyle(
                      context,
                      fontSize: DriverTypography.bodyMedium,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: const Color(0xFF11B96F),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppInformation(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageProvider.getText('App Information', 'معلومات التطبيق'),
            style: const TextStyle(
              fontSize: DriverTypography.titleMedium,
              fontWeight: FontWeight.bold,
              color: Color(0xFF11B96F),
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            languageProvider.getText('App Name', 'اسم التطبيق'),
            'Wasslti Partner',
            isDark,
          ),
          _buildInfoRow(
            languageProvider.getText('Version', 'الإصدار'),
            '1.0.0',
            isDark,
          ),
          _buildInfoRow(
            languageProvider.getText('Developer', 'المطور'),
            'Wasslti Technologies',
            isDark,
          ),
          _buildInfoRow(
            languageProvider.getText('Last Updated', 'آخر تحديث'),
            '2024-12-07',
            isDark,
          ),
          _buildInfoRow(
            languageProvider.getText('License', 'الترخيص'),
            languageProvider.getText('Proprietary', 'ملكية خاصة'),
            isDark,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: DriverTypography.bodyMedium,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: DriverTypography.bodyMedium,
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  void _showComingSoon(BuildContext context, LanguageProvider languageProvider) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageProvider.getText('Coming Soon', 'قريباً')
        ),
        backgroundColor: const Color(0xFF11B96F),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
