import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider for managing app language
class LanguageProvider extends ChangeNotifier {
  static const String _languageKey = 'app_language';
  
  // Default to English
  String _currentLanguage = 'English';
  Locale _currentLocale = const Locale('en');
  
  String get currentLanguage => _currentLanguage;
  Locale get currentLocale => _currentLocale;
  bool get isArabic => _currentLanguage == 'العربية';
  bool get isEnglish => _currentLanguage == 'English';
  
  /// Initialize language from storage
  Future<void> initializeLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey);
      
      if (savedLanguage != null) {
        await changeLanguage(savedLanguage);
      } else {
        // Default to English if no saved preference
        await changeLanguage('English');
      }
    } catch (e) {
      print('Error initializing language: $e');
      // Fallback to English
      _currentLanguage = 'English';
      _currentLocale = const Locale('en');
      notifyListeners();
    }
  }
  
  /// Change app language
  Future<void> changeLanguage(String language) async {
    try {
      _currentLanguage = language;
      
      switch (language) {
        case 'العربية':
          _currentLocale = const Locale('ar');
          break;
        case 'English':
        default:
          _currentLocale = const Locale('en');
          break;
      }
      
      // Save to storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, language);
      
      notifyListeners();
    } catch (e) {
      print('Error changing language: $e');
    }
  }
  
  /// Get available languages
  List<String> get availableLanguages => ['English', 'العربية'];
  
  /// Get text direction based on current language
  TextDirection get textDirection {
    return isArabic ? TextDirection.rtl : TextDirection.ltr;
  }
  
  /// Get localized text
  String getText(String englishText, String arabicText) {
    return isArabic ? arabicText : englishText;
  }
  
  /// Common texts
  String get settingsTitle => getText('Settings', 'الإعدادات');
  String get accountSettings => getText('Account Settings', 'إعدادات الحساب');
  String get editProfile => getText('Edit Profile', 'تعديل الملف الشخصي');
  String get changePassword => getText('Change Password', 'تغيير كلمة السر');
  String get manageDocuments => getText('Manage Documents', 'إدارة الوثائق');
  String get availability => getText('Availability', 'التوافر');
  String get onlineOffline => getText('Online / Offline', 'متصل / غير متصل');
  String get preferredZones => getText('Preferred Delivery Zones', 'مناطق التوصيل المفضلة');
  String get notifications => getText('Notifications', 'الإشعارات');
  String get pushNotifications => getText('Push Notifications', 'الإشعارات الفورية');
  String get soundVibration => getText('Sound & Vibration', 'الصوت والاهتزاز');
  String get security => getText('Security', 'الأمان');
  String get biometricLogin => getText('Biometric Login', 'المصادقة البيومترية');
  String get sessionHistory => getText('Session History', 'سجل الجلسات');
  String get paymentEarnings => getText('Payment & Earnings', 'الدفع والأرباح');
  String get earningsOverview => getText('Earnings Overview', 'نظرة عامة على الأرباح');
  String get addBankAccount => getText('Add Bank Account', 'إضافة حساب بنكي');
  String get appPreferences => getText('App Preferences', 'تفضيلات التطبيق');
  String get language => getText('Language', 'اللغة');
  String get themeMode => getText('Theme Mode', 'وضع المظهر');
  String get mapType => getText('Map Type', 'نوع الخريطة');
  String get support => getText('Support', 'الدعم');
  String get helpCenter => getText('Help Center', 'مركز المساعدة');
  String get contactSupport => getText('Contact Support', 'تواصل مع الدعم');
  String get legal => getText('Legal', 'القانونية');
  String get termsConditions => getText('Terms & Conditions', 'الشروط والأحكام');
  String get privacyPolicy => getText('Privacy Policy', 'سياسة الخصوصية');
  String get logout => getText('Logout', 'تسجيل الخروج');
  
  // Driver status texts
  String get statusWorking => getText('Working', 'يعمل');
  String get statusResting => getText('Resting', 'استراحة');
  String get statusStopped => getText('Stopped', 'متوقف');
  
  // Theme options
  String get lightMode => getText('Light', 'فاتح');
  String get darkMode => getText('Dark', 'مظلم');
  String get systemMode => getText('System', 'النظام');
  
  // Common actions
  String get save => getText('Save', 'حفظ');
  String get cancel => getText('Cancel', 'إلغاء');
  String get ok => getText('OK', 'موافق');
  String get yes => getText('Yes', 'نعم');
  String get no => getText('No', 'لا');
  String get enable => getText('Enable', 'تفعيل');
  String get disable => getText('Disable', 'إلغاء تفعيل');
  
  // Messages
  String get logoutConfirmation => getText(
    'Are you sure you want to logout?', 
    'هل أنت متأكد من تسجيل الخروج؟'
  );
  String get comingSoon => getText('Coming Soon', 'قريباً');
}
