import React, { useState, useEffect } from 'react';
import { Menu, X, Phone, MapPin, User, Bell, MessageCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import LanguageSwitcher from './LanguageSwitcher';
import AuthModal from './AuthModal';
import ContactModal from './ContactModal';
import { useModal } from '../hooks/useModal';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const navigate = useNavigate();
  const { t, isRTL } = useLanguage();
  
  // Modals
  const loginModal = useModal();
  const signupModal = useModal();
  const contactModal = useModal();
  
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const menuItems = [
    { key: 'header.home', href: '#home', action: null },
    { key: 'header.about', href: '#about', action: () => {
        const aboutSection = document.getElementById('about');
        if (aboutSection) {
          aboutSection.scrollIntoView({ behavior: 'smooth' });
        }
      } },
    { key: 'header.restaurants', href: '#restaurants', action: null },
    { key: 'header.drivers', href: '#drivers', action: null },
    { key: 'header.support', href: '/contact', action: () => navigate('/contact') },
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
      isScrolled 
        ? 'bg-white/95 backdrop-blur-lg shadow-xl border-b border-gray-100' 
        : 'bg-transparent'
    }`}>
      
      {/* Modals */}
      <AuthModal 
        isOpen={loginModal.isOpen} 
        onClose={loginModal.closeModal} 
        initialMode="login" 
      />
      <AuthModal 
        isOpen={signupModal.isOpen} 
        onClose={signupModal.closeModal} 
        initialMode="signup" 
      />
      <ContactModal 
        isOpen={contactModal.isOpen} 
        onClose={contactModal.closeModal} 
      />

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-2xl">و</span>
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-400 rounded-full animate-pulse"></div>
            </div>
            <div>
              <span className="text-2xl font-bold text-accent">وصلتي</span>
              <div className="text-xs text-gray-500">Wasslti</div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            {menuItems.map((item) => (
              item.href ? (
                <a
                  key={item.href}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    setIsMenuOpen(false);
                    if (item.action) {
                      item.action();
                    } else {
                      const element = document.querySelector(item.href);
                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth' });
                      }
                    }
                  }}
                  className="relative group text-gray-700 hover:text-primary transition-all duration-300 font-medium py-2"
                >
                  <span className={isRTL ? 'rtl-font' : ''}>{t(item.key)}</span>
                  <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></div>
                </a>
              ) : (
                <button
                  key={item.href || item.key}
                  onClick={item.action || (() => {})}
                  className="relative group text-gray-700 hover:text-primary transition-all duration-300 font-medium py-2"
                >
                  <span className={isRTL ? 'rtl-font' : ''}>{t(item.key)}</span>
                  <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></div>
                </button>
              )
            ))}
          </nav>

          {/* Right Side Actions */}
          <div className="hidden lg:flex items-center space-x-4 rtl:space-x-reverse">
            <LanguageSwitcher />
            
            <div className="w-px h-6 bg-gray-300"></div>
            
            <button 
              onClick={() => alert('الإشعارات قريباً...')}
              className="relative p-2 text-gray-700 hover:text-primary transition-colors rounded-lg hover:bg-gray-50"
            >
              <Bell className="w-5 h-5" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
            </button>
            
            <button 
              onClick={loginModal.openModal}
              className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-primary transition-colors p-2 rounded-lg hover:bg-gray-50"
            >
              <User className="w-5 h-5" />
              <span className="font-medium">{t('header.login')}</span>
            </button>
            
            <button 
              onClick={signupModal.openModal}
              className="bg-gradient-to-r from-primary to-primary/90 text-white px-6 py-2.5 rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300 font-semibold"
            >
              {t('header.signup')}
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden p-2 text-gray-700 hover:text-primary transition-colors"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden mt-4 pb-4 border-t border-gray-200 bg-white/95 backdrop-blur-lg rounded-2xl shadow-xl">
            <nav className="flex flex-col space-y-2 pt-4 px-4">
              {menuItems.map((item) => (
                item.href && !item.action ? (
                  <a
                    key={item.href}
                    href={item.href}
                    className="text-gray-700 hover:text-primary hover:bg-gray-50 transition-all duration-200 font-medium py-3 px-4 rounded-lg"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className={isRTL ? 'rtl-font' : ''}>{t(item.key)}</span>
                  </a>
                ) : (
                  <button
                    key={item.href || item.key}
                    onClick={() => {
                      if (item.action) {
                        item.action();
                      }
                      setIsMenuOpen(false);
                    }}
                    className="text-gray-700 hover:text-primary hover:bg-gray-50 transition-all duration-200 font-medium py-3 px-4 rounded-lg text-right"
                  >
                    <span className={isRTL ? 'rtl-font' : ''}>{t(item.key)}</span>
                  </button>
                )
              ))}
              <div className="flex flex-col space-y-3 pt-4 border-t border-gray-200">
                <div className="px-4">
                  <LanguageSwitcher />
                </div>
                <button 
                  onClick={() => {
                    loginModal.openModal();
                    setIsMenuOpen(false);
                  }}
                  className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 hover:text-primary transition-colors py-2 px-4 rounded-lg hover:bg-gray-50"
                >
                  <User className="w-5 h-5" />
                  <span>{t('header.login')}</span>
                </button>
                <button 
                  onClick={() => {
                    signupModal.openModal();
                    setIsMenuOpen(false);
                  }}
                  className="bg-gradient-to-r from-primary to-primary/90 text-white px-6 py-3 rounded-xl font-semibold mx-4"
                >
                  {t('header.signup')}
                </button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;