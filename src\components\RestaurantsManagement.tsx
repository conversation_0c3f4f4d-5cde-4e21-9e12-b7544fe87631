import React, { useState } from 'react';
import { 
  ChefHat, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Star, 
  MapPin, 
  Phone, 
  Mail,
  DollarSign,
  Package,
  Users,
  TrendingUp,
  AlertTriangle,
  Plus,
  RefreshCw,
  FileText,
  Percent,
  CreditCard,
  Calendar,
  BarChart3,
  ArrowUp,
  ArrowDown,
  Coffee,
  ShoppingBag,
  Utensils,
  MessageCircle
} from 'lucide-react';

interface Restaurant {
  id: string;
  name: string;
  nameEn: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  cuisine: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  isOpen: boolean;
  rating: number;
  reviewCount: number;
  totalOrders: number;
  activeOrders: number;
  avgPrepTime: number;
  revenue: number;
  commission: number;
  pendingPayment: number;
  lastPaymentDate: string;
  logo: string;
  coverImage: string;
  openingHours: string;
  deliveryTime: string;
  minimumOrder: number;
  deliveryFee: number;
  createdAt: string;
  lastActive: string;
  documents: {
    businessLicense: boolean;
    foodSafety: boolean;
    taxCertificate: boolean;
    menuPhotos: boolean;
    ownerID: boolean;
  };
  reviews: {
    id: string;
    customerName: string;
    rating: number;
    comment: string;
    date: string;
  }[];
  popularItems: {
    name: string;
    price: number;
    orderCount: number;
  }[];
  paymentHistory: {
    id: string;
    amount: number;
    date: string;
    status: 'paid' | 'pending' | 'failed';
    method: string;
  }[];
}

interface RestaurantApplication {
  id: string;
  name: string;
  nameEn: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  cuisine: string;
  ownerName: string;
  submittedAt: string;
  documents: {
    businessLicense: boolean;
    foodSafety: boolean;
    taxCertificate: boolean;
    menuPhotos: boolean;
    ownerID: boolean;
  };
  notes: string;
  logo: string;
}

const RestaurantsManagement = () => {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([
    {
      id: '1',
      name: 'مطعم الأصالة',
      nameEn: 'Al Asala Restaurant',
      email: '<EMAIL>',
      phone: '+212 5XX-XXXXXX',
      address: 'شارع الحسن الثاني، الدار البيضاء',
      city: 'الدار البيضاء',
      cuisine: 'مغربي تقليدي',
      status: 'approved',
      isOpen: true,
      rating: 4.8,
      reviewCount: 324,
      totalOrders: 1250,
      activeOrders: 8,
      avgPrepTime: 25,
      revenue: 125000,
      commission: 15,
      pendingPayment: 8500,
      lastPaymentDate: '2024-01-15',
      logo: 'https://images.pexels.com/photos/1581384/pexels-photo-1581384.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
      coverImage: 'https://images.pexels.com/photos/1581384/pexels-photo-1581384.jpeg?auto=compress&cs=tinysrgb&w=400&h=200&fit=crop',
      openingHours: '10:00 - 23:00',
      deliveryTime: '30-45 دقيقة',
      minimumOrder: 50,
      deliveryFee: 15,
      createdAt: '2024-01-15',
      lastActive: '2024-01-20 16:30',
      documents: {
        businessLicense: true,
        foodSafety: true,
        taxCertificate: true,
        menuPhotos: true,
        ownerID: true
      },
      reviews: [
        {
          id: '1',
          customerName: 'أحمد محمد',
          rating: 5,
          comment: 'طعام رائع وخدمة ممتازة، سأطلب مرة أخرى بالتأكيد',
          date: '2024-01-18'
        },
        {
          id: '2',
          customerName: 'فاطمة الزهراء',
          rating: 4,
          comment: 'الطعام لذيذ لكن التوصيل تأخر قليلاً',
          date: '2024-01-15'
        }
      ],
      popularItems: [
        { name: 'طاجين الدجاج', price: 85, orderCount: 230 },
        { name: 'كسكس باللحم', price: 120, orderCount: 180 },
        { name: 'بسطيلة', price: 95, orderCount: 150 }
      ],
      paymentHistory: [
        { id: 'p1', amount: 12500, date: '2024-01-15', status: 'paid', method: 'تحويل بنكي' },
        { id: 'p2', amount: 8500, date: '2024-01-01', status: 'pending', method: 'تحويل بنكي' }
      ]
    },
    {
      id: '2',
      name: 'برجر ستيشن',
      nameEn: 'Burger Station',
      email: '<EMAIL>',
      phone: '+212 5XX-XXXXXX',
      address: 'حي الرياض، الرباط',
      city: 'الرباط',
      cuisine: 'وجبات سريعة',
      status: 'approved',
      isOpen: true,
      rating: 4.5,
      reviewCount: 189,
      totalOrders: 890,
      activeOrders: 12,
      avgPrepTime: 18,
      revenue: 67000,
      commission: 12,
      pendingPayment: 4200,
      lastPaymentDate: '2024-01-10',
      logo: 'https://images.pexels.com/photos/1435904/pexels-photo-1435904.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
      coverImage: 'https://images.pexels.com/photos/1435904/pexels-photo-1435904.jpeg?auto=compress&cs=tinysrgb&w=400&h=200&fit=crop',
      openingHours: '11:00 - 24:00',
      deliveryTime: '20-30 دقيقة',
      minimumOrder: 30,
      deliveryFee: 10,
      createdAt: '2024-01-10',
      lastActive: '2024-01-20 14:15',
      documents: {
        businessLicense: true,
        foodSafety: false,
        taxCertificate: true,
        menuPhotos: true,
        ownerID: true
      },
      reviews: [
        {
          id: '1',
          customerName: 'محمد العلوي',
          rating: 5,
          comment: 'أفضل برجر في المدينة!',
          date: '2024-01-19'
        },
        {
          id: '2',
          customerName: 'سارة الحسني',
          rating: 4,
          comment: 'طعم رائع وتوصيل سريع',
          date: '2024-01-17'
        }
      ],
      popularItems: [
        { name: 'برجر كلاسيك', price: 45, orderCount: 320 },
        { name: 'تشيز برجر', price: 55, orderCount: 280 },
        { name: 'بطاطس مقلية', price: 25, orderCount: 450 }
      ],
      paymentHistory: [
        { id: 'p1', amount: 6500, date: '2024-01-10', status: 'paid', method: 'تحويل بنكي' },
        { id: 'p2', amount: 4200, date: '2024-01-01', status: 'pending', method: 'تحويل بنكي' }
      ]
    },
    {
      id: '3',
      name: 'حلويات الدار البيضاء',
      nameEn: 'Casablanca Sweets',
      email: '<EMAIL>',
      phone: '+212 5XX-XXXXXX',
      address: 'المعاريف، الدار البيضاء',
      city: 'الدار البيضاء',
      cuisine: 'حلويات ومعجنات',
      status: 'pending',
      isOpen: false,
      rating: 0,
      reviewCount: 0,
      totalOrders: 0,
      activeOrders: 0,
      avgPrepTime: 0,
      revenue: 0,
      commission: 10,
      pendingPayment: 0,
      lastPaymentDate: '-',
      logo: 'https://images.pexels.com/photos/1055272/pexels-photo-1055272.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
      coverImage: 'https://images.pexels.com/photos/1055272/pexels-photo-1055272.jpeg?auto=compress&cs=tinysrgb&w=400&h=200&fit=crop',
      openingHours: '08:00 - 22:00',
      deliveryTime: '25-35 دقيقة',
      minimumOrder: 40,
      deliveryFee: 12,
      createdAt: '2024-01-18',
      lastActive: 'لم يتم التفعيل بعد',
      documents: {
        businessLicense: true,
        foodSafety: true,
        taxCertificate: false,
        menuPhotos: true,
        ownerID: true
      },
      reviews: [],
      popularItems: [],
      paymentHistory: []
    }
  ]);

  const [applications, setApplications] = useState<RestaurantApplication[]>([
    {
      id: 'app1',
      name: 'مطعم البحر الأزرق',
      nameEn: 'Blue Sea Restaurant',
      email: '<EMAIL>',
      phone: '+212 6XX-XXXXXX',
      address: 'شارع محمد الخامس، طنجة',
      city: 'طنجة',
      cuisine: 'مأكولات بحرية',
      ownerName: 'خالد المرابط',
      submittedAt: '2024-01-19',
      documents: {
        businessLicense: true,
        foodSafety: true,
        taxCertificate: true,
        menuPhotos: true,
        ownerID: true
      },
      notes: 'مطعم متخصص في المأكولات البحرية الطازجة',
      logo: 'https://images.pexels.com/photos/1058277/pexels-photo-1058277.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop'
    },
    {
      id: 'app2',
      name: 'بيتزا إكسبرس',
      nameEn: 'Pizza Express',
      email: '<EMAIL>',
      phone: '+212 6XX-XXXXXX',
      address: 'شارع الزرقطوني، الدار البيضاء',
      city: 'الدار البيضاء',
      cuisine: 'بيتزا وإيطالي',
      ownerName: 'سمير العلوي',
      submittedAt: '2024-01-20',
      documents: {
        businessLicense: true,
        foodSafety: false,
        taxCertificate: true,
        menuPhotos: true,
        ownerID: true
      },
      notes: 'متخصصون في البيتزا الإيطالية الأصلية',
      logo: 'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedCity, setSelectedCity] = useState('all');
  const [selectedCuisine, setSelectedCuisine] = useState('all');
  const [activeTab, setActiveTab] = useState('restaurants');
  const [selectedRestaurant, setSelectedRestaurant] = useState<Restaurant | null>(null);
  const [isRestaurantModalOpen, setIsRestaurantModalOpen] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<RestaurantApplication | null>(null);
  const [isApplicationModalOpen, setIsApplicationModalOpen] = useState(false);
  const [isCommissionModalOpen, setIsCommissionModalOpen] = useState(false);

  const statuses = [
    { id: 'all', name: 'جميع الحالات', color: 'text-gray-600' },
    { id: 'pending', name: 'في الانتظار', color: 'text-yellow-600' },
    { id: 'approved', name: 'مقبول', color: 'text-green-600' },
    { id: 'rejected', name: 'مرفوض', color: 'text-red-600' },
    { id: 'suspended', name: 'موقوف', color: 'text-orange-600' }
  ];

  const cities = [
    { id: 'all', name: 'جميع المدن' },
    { id: 'الدار البيضاء', name: 'الدار البيضاء' },
    { id: 'الرباط', name: 'الرباط' },
    { id: 'مراكش', name: 'مراكش' },
    { id: 'فاس', name: 'فاس' },
    { id: 'طنجة', name: 'طنجة' }
  ];

  const cuisines = [
    { id: 'all', name: 'جميع المأكولات' },
    { id: 'مغربي تقليدي', name: 'مغربي تقليدي' },
    { id: 'وجبات سريعة', name: 'وجبات سريعة' },
    { id: 'حلويات ومعجنات', name: 'حلويات ومعجنات' },
    { id: 'مأكولات آسيوية', name: 'مأكولات آسيوية' },
    { id: 'مأكولات بحرية', name: 'مأكولات بحرية' },
    { id: 'بيتزا وإيطالي', name: 'بيتزا وإيطالي' }
  ];

  const filteredRestaurants = restaurants.filter(restaurant => {
    const matchesSearch = restaurant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         restaurant.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         restaurant.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || restaurant.status === selectedStatus;
    const matchesCity = selectedCity === 'all' || restaurant.city === selectedCity;
    const matchesCuisine = selectedCuisine === 'all' || restaurant.cuisine === selectedCuisine;
    
    return matchesSearch && matchesStatus && matchesCity && matchesCuisine;
  });

  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCity = selectedCity === 'all' || app.city === selectedCity;
    const matchesCuisine = selectedCuisine === 'all' || app.cuisine === selectedCuisine;
    
    return matchesSearch && matchesCity && matchesCuisine;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'suspended': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    const statusObj = statuses.find(s => s.id === status);
    return statusObj ? statusObj.name : status;
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleApproveRestaurant = (restaurantId: string) => {
    setRestaurants(prev => prev.map(restaurant => 
      restaurant.id === restaurantId ? { ...restaurant, status: 'approved' as any } : restaurant
    ));
  };

  const handleRejectRestaurant = (restaurantId: string) => {
    if (confirm('هل أنت متأكد من رفض هذا المطعم؟')) {
      setRestaurants(prev => prev.map(restaurant => 
        restaurant.id === restaurantId ? { ...restaurant, status: 'rejected' as any } : restaurant
      ));
    }
  };

  const handleSuspendRestaurant = (restaurantId: string) => {
    if (confirm('هل أنت متأكد من توقيف هذا المطعم؟')) {
      setRestaurants(prev => prev.map(restaurant => 
        restaurant.id === restaurantId ? { ...restaurant, status: 'suspended' as any } : restaurant
      ));
    }
  };

  const handleDeleteRestaurant = (restaurantId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المطعم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      setRestaurants(prev => prev.filter(restaurant => restaurant.id !== restaurantId));
    }
  };

  const handleViewRestaurant = (restaurant: Restaurant) => {
    setSelectedRestaurant(restaurant);
    setIsRestaurantModalOpen(true);
  };

  const handleViewApplication = (application: RestaurantApplication) => {
    setSelectedApplication(application);
    setIsApplicationModalOpen(true);
  };

  const handleApproveApplication = (applicationId: string) => {
    const application = applications.find(app => app.id === applicationId);
    if (application) {
      // Create a new restaurant from the application
      const newRestaurant: Restaurant = {
        id: `new-${Date.now()}`,
        name: application.name,
        nameEn: application.nameEn,
        email: application.email,
        phone: application.phone,
        address: application.address,
        city: application.city,
        cuisine: application.cuisine,
        status: 'approved',
        isOpen: false,
        rating: 0,
        reviewCount: 0,
        totalOrders: 0,
        activeOrders: 0,
        avgPrepTime: 0,
        revenue: 0,
        commission: 10, // Default commission
        pendingPayment: 0,
        lastPaymentDate: '-',
        logo: application.logo,
        coverImage: application.logo, // Using logo as cover temporarily
        openingHours: '09:00 - 22:00', // Default hours
        deliveryTime: '30-45 دقيقة',
        minimumOrder: 50,
        deliveryFee: 15,
        createdAt: new Date().toISOString().split('T')[0],
        lastActive: 'لم يتم التفعيل بعد',
        documents: application.documents,
        reviews: [],
        popularItems: [],
        paymentHistory: []
      };
      
      // Add the new restaurant
      setRestaurants(prev => [...prev, newRestaurant]);
      
      // Remove the application
      setApplications(prev => prev.filter(app => app.id !== applicationId));
      
      // Close modal if open
      if (isApplicationModalOpen && selectedApplication?.id === applicationId) {
        setIsApplicationModalOpen(false);
        setSelectedApplication(null);
      }
      
      alert('تمت الموافقة على المطعم بنجاح!');
    }
  };

  const handleRejectApplication = (applicationId: string, reason: string = '') => {
    if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
      // Remove the application
      setApplications(prev => prev.filter(app => app.id !== applicationId));
      
      // Close modal if open
      if (isApplicationModalOpen && selectedApplication?.id === applicationId) {
        setIsApplicationModalOpen(false);
        setSelectedApplication(null);
      }
      
      alert(`تم رفض طلب المطعم. ${reason ? `السبب: ${reason}` : ''}`);
    }
  };

  const handleUpdateCommission = (restaurantId: string, newCommission: number) => {
    setRestaurants(prev => prev.map(restaurant => 
      restaurant.id === restaurantId ? { ...restaurant, commission: newCommission } : restaurant
    ));
    
    alert(`تم تحديث نسبة العمولة إلى ${newCommission}%`);
  };

  const handleToggleRestaurantStatus = (restaurantId: string) => {
    setRestaurants(prev => prev.map(restaurant => 
      restaurant.id === restaurantId ? { ...restaurant, isOpen: !restaurant.isOpen } : restaurant
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white rtl-font">
            إدارة المطاعم
          </h1>
          <p className="text-gray-600 dark:text-gray-400 rtl-font">
            إدارة ومتابعة جميع المطاعم الشريكة
          </p>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
            <Download className="w-4 h-4" />
            <span>تصدير</span>
          </button>
          <button className="bg-primary text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary/90 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
            <Plus className="w-5 h-5" />
            <span>إضافة مطعم</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إجمالي المطاعم</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">{restaurants.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
              <ChefHat className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">المطاعم النشطة</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {restaurants.filter(r => r.status === 'approved' && r.isOpen).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">طلبات الانضمام</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {applications.length}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إجمالي الإيرادات</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {restaurants.reduce((sum, r) => sum + r.revenue, 0).toLocaleString()} درهم
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex justify-center mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-2 shadow-lg">
          <div className="flex space-x-2 rtl:space-x-reverse">
            <button
              onClick={() => setActiveTab('restaurants')}
              className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'restaurants'
                  ? 'bg-primary text-white shadow-lg'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <ChefHat className="w-5 h-5" />
              <span className="rtl-font">المطاعم النشطة</span>
            </button>
            <button
              onClick={() => setActiveTab('applications')}
              className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'applications'
                  ? 'bg-primary text-white shadow-lg'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <FileText className="w-5 h-5" />
              <span className="rtl-font">طلبات الانضمام</span>
              {applications.length > 0 && (
                <span className="w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs ml-2">
                  {applications.length}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab('payments')}
              className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'payments'
                  ? 'bg-primary text-white shadow-lg'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <DollarSign className="w-5 h-5" />
              <span className="rtl-font">المدفوعات والعمولات</span>
            </button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="البحث عن مطعم..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
            />
          </div>

          {/* Status Filter - Only show for restaurants tab */}
          {activeTab === 'restaurants' && (
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
            >
              {statuses.map(status => (
                <option key={status.id} value={status.id}>{status.name}</option>
              ))}
            </select>
          )}

          {/* City Filter */}
          <select
            value={selectedCity}
            onChange={(e) => setSelectedCity(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {cities.map(city => (
              <option key={city.id} value={city.id}>{city.name}</option>
            ))}
          </select>

          {/* Cuisine Filter */}
          <select
            value={selectedCuisine}
            onChange={(e) => setSelectedCuisine(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {cuisines.map(cuisine => (
              <option key={cuisine.id} value={cuisine.id}>{cuisine.name}</option>
            ))}
          </select>

          {/* Refresh Button */}
          <button className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse">
            <RefreshCw className="w-4 h-4" />
            <span>تحديث</span>
          </button>
        </div>
      </div>

      {/* Active Restaurants Tab */}
      {activeTab === 'restaurants' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredRestaurants.map((restaurant) => (
            <div key={restaurant.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
              {/* Restaurant Header */}
              <div className="relative">
                <img 
                  src={restaurant.coverImage} 
                  alt={restaurant.name}
                  className="w-full h-32 object-cover"
                />
                <div className="absolute top-3 right-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(restaurant.status)}`}>
                    {getStatusText(restaurant.status)}
                  </span>
                </div>
                <div className="absolute top-3 left-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                    restaurant.isOpen 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' 
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                  }`}>
                    {restaurant.isOpen ? 'مفتوح' : 'مغلق'}
                  </span>
                </div>
                <div className="absolute -bottom-6 right-4">
                  <img 
                    src={restaurant.logo} 
                    alt={restaurant.name}
                    className="w-12 h-12 rounded-xl object-cover border-4 border-white dark:border-gray-800 shadow-lg"
                  />
                </div>
              </div>

              {/* Restaurant Info */}
              <div className="p-6 pt-8">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
                      {restaurant.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                      {restaurant.cuisine}
                    </p>
                  </div>
                  {restaurant.rating > 0 && (
                    <div className="flex items-center space-x-1 rtl:space-x-reverse">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-semibold">{restaurant.rating}</span>
                      <span className="text-xs text-gray-500">({restaurant.reviewCount})</span>
                    </div>
                  )}
                </div>

                {/* Active Orders & Prep Time */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="bg-blue-50 dark:bg-blue-900/10 rounded-xl p-3 text-center">
                    <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {restaurant.activeOrders}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 rtl-font">
                      طلب جاري
                    </div>
                  </div>
                  <div className="bg-orange-50 dark:bg-orange-900/10 rounded-xl p-3 text-center">
                    <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
                      {restaurant.avgPrepTime} دقيقة
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 rtl-font">
                      وقت التحضير
                    </div>
                  </div>
                </div>

                {/* Restaurant Details */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                    <MapPin className="w-4 h-4" />
                    <span className="rtl-font">{restaurant.city}</span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                    <Clock className="w-4 h-4" />
                    <span>{restaurant.openingHours}</span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                    <Package className="w-4 h-4" />
                    <span>{restaurant.totalOrders} طلب</span>
                  </div>
                </div>

                {/* Documents Status */}
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                    حالة الوثائق:
                  </h4>
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <div className={`w-3 h-3 rounded-full ${restaurant.documents.businessLicense ? 'bg-green-500' : 'bg-red-500'}`} title="رخصة العمل"></div>
                    <div className={`w-3 h-3 rounded-full ${restaurant.documents.foodSafety ? 'bg-green-500' : 'bg-red-500'}`} title="شهادة سلامة الغذاء"></div>
                    <div className={`w-3 h-3 rounded-full ${restaurant.documents.taxCertificate ? 'bg-green-500' : 'bg-red-500'}`} title="شهادة ضريبية"></div>
                    <div className={`w-3 h-3 rounded-full ${restaurant.documents.menuPhotos ? 'bg-green-500' : 'bg-red-500'}`} title="صور القائمة"></div>
                    <div className={`w-3 h-3 rounded-full ${restaurant.documents.ownerID ? 'bg-green-500' : 'bg-red-500'}`} title="هوية المالك"></div>
                  </div>
                </div>

                {/* Revenue Info */}
                {restaurant.status === 'approved' && (
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-3 mb-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">الإيرادات:</span>
                      <span className="font-semibold text-gray-800 dark:text-white">
                        {restaurant.revenue.toLocaleString()} درهم
                      </span>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">العمولة:</span>
                      <span className="text-sm font-medium text-primary">
                        {restaurant.commission}%
                      </span>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">مستحقات:</span>
                      <span className="text-sm font-medium text-orange-600">
                        {restaurant.pendingPayment.toLocaleString()} درهم
                      </span>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex space-x-2 rtl:space-x-reverse">
                  <button 
                    onClick={() => handleViewRestaurant(restaurant)}
                    className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-3 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                  >
                    <Eye className="w-4 h-4" />
                    <span className="text-sm font-medium">عرض</span>
                  </button>

                  {restaurant.status === 'pending' && (
                    <>
                      <button 
                        onClick={() => handleApproveRestaurant(restaurant.id)}
                        className="flex-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 py-2 px-3 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                      >
                        <CheckCircle className="w-4 h-4" />
                        <span className="text-sm font-medium">قبول</span>
                      </button>
                      <button 
                        onClick={() => handleRejectRestaurant(restaurant.id)}
                        className="flex-1 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 py-2 px-3 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                      >
                        <XCircle className="w-4 h-4" />
                        <span className="text-sm font-medium">رفض</span>
                      </button>
                    </>
                  )}

                  {restaurant.status === 'approved' && (
                    <>
                      <button 
                        onClick={() => handleToggleRestaurantStatus(restaurant.id)}
                        className={`flex-1 ${
                          restaurant.isOpen 
                            ? 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/40' 
                            : 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/40'
                        } py-2 px-3 rounded-lg transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse`}
                      >
                        {restaurant.isOpen ? <XCircle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                        <span className="text-sm font-medium">{restaurant.isOpen ? 'إغلاق' : 'فتح'}</span>
                      </button>
                      <button 
                        onClick={() => {
                          setSelectedRestaurant(restaurant);
                          setIsCommissionModalOpen(true);
                        }}
                        className="flex-1 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-400 py-2 px-3 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-900/40 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                      >
                        <Percent className="w-4 h-4" />
                        <span className="text-sm font-medium">العمولة</span>
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}

          {filteredRestaurants.length === 0 && (
            <div className="col-span-full bg-white dark:bg-gray-800 rounded-2xl p-12 text-center shadow-lg">
              <ChefHat className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2 rtl-font">
                لا توجد مطاعم
              </h3>
              <p className="text-gray-500 dark:text-gray-500 rtl-font">
                لم يتم العثور على مطاعم تطابق معايير البحث
              </p>
            </div>
          )}
        </div>
      )}

      {/* Applications Tab */}
      {activeTab === 'applications' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredApplications.map((application) => (
            <div key={application.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <img 
                    src={application.logo} 
                    alt={application.name}
                    className="w-16 h-16 rounded-xl object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
                      {application.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {application.email}
                    </p>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                      <span className="text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 px-2 py-1 rounded-full">
                        {application.cuisine}
                      </span>
                      <span className="text-xs bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-400 px-2 py-1 rounded-full">
                        {application.city}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                      طلب جديد
                    </span>
                    <p className="text-xs text-gray-500 mt-1">
                      {application.submittedAt}
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {/* Documents Status */}
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                    حالة الوثائق:
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div className={`w-3 h-3 rounded-full ${application.documents.businessLicense ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">رخصة العمل</span>
                    </div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div className={`w-3 h-3 rounded-full ${application.documents.foodSafety ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">شهادة سلامة الغذاء</span>
                    </div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div className={`w-3 h-3 rounded-full ${application.documents.taxCertificate ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">شهادة ضريبية</span>
                    </div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div className={`w-3 h-3 rounded-full ${application.documents.menuPhotos ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">صور القائمة</span>
                    </div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <div className={`w-3 h-3 rounded-full ${application.documents.ownerID ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">هوية المالك</span>
                    </div>
                  </div>
                </div>

                {/* Owner Info */}
                <div className="mb-4">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                    <Users className="w-4 h-4" />
                    <span className="rtl-font">المالك: {application.ownerName}</span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400 mt-1">
                    <Phone className="w-4 h-4" />
                    <span>{application.phone}</span>
                  </div>
                </div>

                {/* Notes */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-3 mb-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                    {application.notes}
                  </p>
                </div>

                {/* Actions */}
                <div className="flex space-x-2 rtl:space-x-reverse">
                  <button 
                    onClick={() => handleViewApplication(application)}
                    className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-3 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                  >
                    <Eye className="w-4 h-4" />
                    <span className="text-sm font-medium">عرض التفاصيل</span>
                  </button>
                  <button 
                    onClick={() => handleApproveApplication(application.id)}
                    className="flex-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 py-2 px-3 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                  >
                    <CheckCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">قبول</span>
                  </button>
                  <button 
                    onClick={() => handleRejectApplication(application.id)}
                    className="flex-1 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 py-2 px-3 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors flex items-center justify-center space-x-1 rtl:space-x-reverse"
                  >
                    <XCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">رفض</span>
                  </button>
                </div>
              </div>
            </div>
          ))}

          {filteredApplications.length === 0 && (
            <div className="col-span-full bg-white dark:bg-gray-800 rounded-2xl p-12 text-center shadow-lg">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2 rtl-font">
                لا توجد طلبات انضمام
              </h3>
              <p className="text-gray-500 dark:text-gray-500 rtl-font">
                لم يتم العثور على طلبات انضمام جديدة
              </p>
            </div>
          )}
        </div>
      )}

      {/* Payments & Commissions Tab */}
      {activeTab === 'payments' && (
        <div className="space-y-6">
          {/* Commission Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إجمالي العمولات</p>
                  <p className="text-2xl font-bold text-gray-800 dark:text-white">
                    {restaurants.reduce((sum, r) => sum + (r.revenue * r.commission / 100), 0).toLocaleString()} درهم
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                  <Percent className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">المدفوعات المستحقة</p>
                  <p className="text-2xl font-bold text-gray-800 dark:text-white">
                    {restaurants.reduce((sum, r) => sum + r.pendingPayment, 0).toLocaleString()} درهم
                  </p>
                </div>
                <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center">
                  <CreditCard className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">متوسط العمولة</p>
                  <p className="text-2xl font-bold text-gray-800 dark:text-white">
                    {(restaurants.reduce((sum, r) => sum + r.commission, 0) / (restaurants.length || 1)).toFixed(1)}%
                  </p>
                </div>
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">آخر تحديث</p>
                  <p className="text-lg font-bold text-gray-800 dark:text-white">
                    {new Date().toLocaleDateString('ar-MA')}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
            </div>
          </div>

          {/* Payments Table */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-bold text-gray-800 dark:text-white rtl-font">
                المدفوعات والعمولات
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                      المطعم
                    </th>
                    <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                      الإيرادات
                    </th>
                    <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                      نسبة العمولة
                    </th>
                    <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                      إجمالي العمولة
                    </th>
                    <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                      المستحقات
                    </th>
                    <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                      آخر دفعة
                    </th>
                    <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                  {filteredRestaurants
                    .filter(r => r.status === 'approved')
                    .map((restaurant) => (
                    <tr key={restaurant.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <img 
                            src={restaurant.logo} 
                            alt={restaurant.name}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                          <div>
                            <h4 className="font-semibold text-gray-800 dark:text-white rtl-font">
                              {restaurant.name}
                            </h4>
                            <p className="text-xs text-gray-500">{restaurant.city}</p>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="font-semibold text-gray-800 dark:text-white">
                          {restaurant.revenue.toLocaleString()} درهم
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="font-semibold text-primary">
                          {restaurant.commission}%
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="font-semibold text-gray-800 dark:text-white">
                          {(restaurant.revenue * restaurant.commission / 100).toLocaleString()} درهم
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="font-semibold text-orange-600">
                          {restaurant.pendingPayment.toLocaleString()} درهم
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {restaurant.lastPaymentDate}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <button 
                            onClick={() => {
                              setSelectedRestaurant(restaurant);
                              setIsCommissionModalOpen(true);
                            }}
                            className="w-8 h-8 flex items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 hover:bg-purple-200 dark:hover:bg-purple-900/40 transition-colors"
                            title="تعديل العمولة"
                          >
                            <Percent className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => alert(`سيتم تسجيل دفعة جديدة لـ ${restaurant.name}`)}
                            className="w-8 h-8 flex items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors"
                            title="تسجيل دفعة"
                          >
                            <DollarSign className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => alert(`سيتم فتح التقرير المالي لـ ${restaurant.name}`)}
                            className="w-8 h-8 flex items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors"
                            title="التقرير المالي"
                          >
                            <FileText className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredRestaurants.filter(r => r.status === 'approved').length === 0 && (
              <div className="text-center py-12">
                <DollarSign className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2 rtl-font">
                  لا توجد مدفوعات
                </h3>
                <p className="text-gray-500 dark:text-gray-500 rtl-font">
                  لم يتم العثور على مطاعم نشطة لعرض المدفوعات
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Restaurant Details Modal */}
      {isRestaurantModalOpen && selectedRestaurant && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsRestaurantModalOpen(false)} />
          
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="relative w-full max-w-5xl bg-white dark:bg-gray-800 rounded-3xl shadow-2xl">
              {/* Header */}
              <div className="relative h-48 rounded-t-3xl overflow-hidden">
                <img 
                  src={selectedRestaurant.coverImage} 
                  alt={selectedRestaurant.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6 flex items-center justify-between">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <img 
                      src={selectedRestaurant.logo} 
                      alt={selectedRestaurant.name}
                      className="w-20 h-20 rounded-2xl object-cover border-4 border-white shadow-xl"
                    />
                    <div>
                      <h2 className="text-2xl font-bold text-white rtl-font">
                        {selectedRestaurant.name}
                      </h2>
                      <p className="text-white/80 rtl-font">{selectedRestaurant.cuisine}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedRestaurant.status)}`}>
                      {getStatusText(selectedRestaurant.status)}
                    </span>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      selectedRestaurant.isOpen 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' 
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                    }`}>
                      {selectedRestaurant.isOpen ? 'مفتوح' : 'مغلق'}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => setIsRestaurantModalOpen(false)}
                  className="absolute top-4 left-4 w-10 h-10 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Left Column - Restaurant Info */}
                  <div className="lg:col-span-1 space-y-6">
                    {/* Contact Info */}
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-4">
                      <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 rtl-font">
                        معلومات الاتصال
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <Mail className="w-5 h-5 text-gray-500" />
                          <span className="text-gray-600 dark:text-gray-300">{selectedRestaurant.email}</span>
                        </div>
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <Phone className="w-5 h-5 text-gray-500" />
                          <span className="text-gray-600 dark:text-gray-300">{selectedRestaurant.phone}</span>
                        </div>
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <MapPin className="w-5 h-5 text-gray-500" />
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">{selectedRestaurant.address}</span>
                        </div>
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <Clock className="w-5 h-5 text-gray-500" />
                          <span className="text-gray-600 dark:text-gray-300">{selectedRestaurant.openingHours}</span>
                        </div>
                      </div>
                    </div>

                    {/* Documents */}
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-4">
                      <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 rtl-font">
                        الوثائق
                      </h3>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">رخصة العمل</span>
                          {selectedRestaurant.documents.businessLicense ? (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          ) : (
                            <XCircle className="w-5 h-5 text-red-500" />
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">شهادة سلامة الغذاء</span>
                          {selectedRestaurant.documents.foodSafety ? (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          ) : (
                            <XCircle className="w-5 h-5 text-red-500" />
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">شهادة ضريبية</span>
                          {selectedRestaurant.documents.taxCertificate ? (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          ) : (
                            <XCircle className="w-5 h-5 text-red-500" />
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">صور القائمة</span>
                          {selectedRestaurant.documents.menuPhotos ? (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          ) : (
                            <XCircle className="w-5 h-5 text-red-500" />
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">هوية المالك</span>
                          {selectedRestaurant.documents.ownerID ? (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          ) : (
                            <XCircle className="w-5 h-5 text-red-500" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Financial Info */}
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-4">
                      <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 rtl-font">
                        المعلومات المالية
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">إجمالي الإيرادات</span>
                          <span className="font-bold text-gray-800 dark:text-white">
                            {selectedRestaurant.revenue.toLocaleString()} درهم
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">نسبة العمولة</span>
                          <span className="font-bold text-primary">
                            {selectedRestaurant.commission}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">إجمالي العمولة</span>
                          <span className="font-bold text-gray-800 dark:text-white">
                            {(selectedRestaurant.revenue * selectedRestaurant.commission / 100).toLocaleString()} درهم
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">المستحقات</span>
                          <span className="font-bold text-orange-600">
                            {selectedRestaurant.pendingPayment.toLocaleString()} درهم
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600 dark:text-gray-300 rtl-font">آخر دفعة</span>
                          <span className="text-gray-600 dark:text-gray-300">
                            {selectedRestaurant.lastPaymentDate}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Performance & Reviews */}
                  <div className="lg:col-span-2 space-y-6">
                    {/* Performance Stats */}
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-4">
                      <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 rtl-font">
                        أداء المطعم
                      </h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="bg-white dark:bg-gray-800 rounded-xl p-3 text-center">
                          <div className="text-2xl font-bold text-gray-800 dark:text-white">
                            {selectedRestaurant.totalOrders}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                            إجمالي الطلبات
                          </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-xl p-3 text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {selectedRestaurant.activeOrders}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                            طلبات جارية
                          </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-xl p-3 text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {selectedRestaurant.avgPrepTime} دقيقة
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                            وقت التحضير
                          </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-xl p-3 text-center">
                          <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse">
                            <Star className="w-5 h-5 text-yellow-400 fill-current" />
                            <span className="text-2xl font-bold text-gray-800 dark:text-white">
                              {selectedRestaurant.rating}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                            التقييم ({selectedRestaurant.reviewCount})
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Popular Items */}
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-4">
                      <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 rtl-font">
                        الأطباق الأكثر طلباً
                      </h3>
                      {selectedRestaurant.popularItems.length > 0 ? (
                        <div className="space-y-3">
                          {selectedRestaurant.popularItems.map((item, index) => (
                            <div key={index} className="flex items-center justify-between bg-white dark:bg-gray-800 p-3 rounded-xl">
                              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                                  <Utensils className="w-4 h-4 text-primary" />
                                </div>
                                <div>
                                  <h4 className="font-semibold text-gray-800 dark:text-white rtl-font">
                                    {item.name}
                                  </h4>
                                  <p className="text-sm text-gray-500">{item.price} درهم</p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                <ShoppingBag className="w-4 h-4 text-gray-400" />
                                <span className="font-semibold text-gray-800 dark:text-white">
                                  {item.orderCount}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6">
                          <p className="text-gray-500 dark:text-gray-400 rtl-font">
                            لا توجد بيانات متاحة
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Reviews */}
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
                          التقييمات والمراجعات
                        </h3>
                        {selectedRestaurant.reviews.length > 0 && (
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Star className="w-5 h-5 text-yellow-400 fill-current" />
                            <span className="font-bold text-gray-800 dark:text-white">
                              {selectedRestaurant.rating}
                            </span>
                            <span className="text-sm text-gray-500">
                              ({selectedRestaurant.reviewCount})
                            </span>
                          </div>
                        )}
                      </div>
                      
                      {selectedRestaurant.reviews.length > 0 ? (
                        <div className="space-y-4">
                          {selectedRestaurant.reviews.map((review) => (
                            <div key={review.id} className="bg-white dark:bg-gray-800 rounded-xl p-4">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-semibold text-gray-800 dark:text-white rtl-font">
                                  {review.customerName}
                                </h4>
                                <div className="flex">
                                  {[...Array(5)].map((_, i) => (
                                    <Star 
                                      key={i} 
                                      className={`w-4 h-4 ${
                                        i < review.rating 
                                          ? 'text-yellow-400 fill-current' 
                                          : 'text-gray-300 dark:text-gray-600'
                                      }`} 
                                    />
                                  ))}
                                </div>
                              </div>
                              <p className="text-gray-600 dark:text-gray-400 text-sm rtl-font">
                                {review.comment}
                              </p>
                              <div className="flex items-center justify-between mt-2">
                                <span className="text-xs text-gray-500">{review.date}</span>
                                <div className="flex space-x-2 rtl:space-x-reverse">
                                  <button className="text-blue-600 dark:text-blue-400 text-xs hover:underline">
                                    الرد
                                  </button>
                                  <button className="text-red-600 dark:text-red-400 text-xs hover:underline">
                                    إزالة
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6">
                          <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                          <p className="text-gray-500 dark:text-gray-400 rtl-font">
                            لا توجد تقييمات بعد
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Payment History */}
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-4">
                      <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 rtl-font">
                        سجل المدفوعات
                      </h3>
                      
                      {selectedRestaurant.paymentHistory.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="w-full">
                            <thead className="bg-white dark:bg-gray-800">
                              <tr>
                                <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                                  رقم الدفعة
                                </th>
                                <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                                  المبلغ
                                </th>
                                <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                                  التاريخ
                                </th>
                                <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                                  الحالة
                                </th>
                                <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                                  طريقة الدفع
                                </th>
                              </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-100 dark:divide-gray-700">
                              {selectedRestaurant.paymentHistory.map((payment) => (
                                <tr key={payment.id} className="bg-white dark:bg-gray-800">
                                  <td className="px-4 py-3 text-sm text-gray-800 dark:text-white">
                                    #{payment.id}
                                  </td>
                                  <td className="px-4 py-3 text-sm font-semibold text-gray-800 dark:text-white">
                                    {payment.amount.toLocaleString()} درهم
                                  </td>
                                  <td className="px-4 py-3 text-sm text-gray-600 dark:text-gray-400">
                                    {payment.date}
                                  </td>
                                  <td className="px-4 py-3">
                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(payment.status)}`}>
                                      {payment.status === 'paid' ? 'مدفوع' : payment.status === 'pending' ? 'في الانتظار' : 'فشل'}
                                    </span>
                                  </td>
                                  <td className="px-4 py-3 text-sm text-gray-600 dark:text-gray-400 rtl-font">
                                    {payment.method}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-6">
                          <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                          <p className="text-gray-500 dark:text-gray-400 rtl-font">
                            لا توجد مدفوعات مسجلة
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 rtl:space-x-reverse mt-6">
                  <button
                    onClick={() => setIsRestaurantModalOpen(false)}
                    className="px-6 py-3 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    إغلاق
                  </button>
                  
                  {selectedRestaurant.status === 'approved' && (
                    <>
                      <button
                        onClick={() => handleToggleRestaurantStatus(selectedRestaurant.id)}
                        className={`px-6 py-3 rounded-xl transition-colors ${
                          selectedRestaurant.isOpen 
                            ? 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/40' 
                            : 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/40'
                        }`}
                      >
                        {selectedRestaurant.isOpen ? 'إغلاق المطعم' : 'فتح المطعم'}
                      </button>
                      <button
                        onClick={() => {
                          setIsRestaurantModalOpen(false);
                          setIsCommissionModalOpen(true);
                        }}
                        className="px-6 py-3 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-400 rounded-xl hover:bg-purple-200 dark:hover:bg-purple-900/40 transition-colors"
                      >
                        تعديل العمولة
                      </button>
                    </>
                  )}
                  
                  {selectedRestaurant.status === 'pending' && (
                    <>
                      <button
                        onClick={() => {
                          handleApproveRestaurant(selectedRestaurant.id);
                          setIsRestaurantModalOpen(false);
                        }}
                        className="px-6 py-3 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded-xl hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors"
                      >
                        قبول المطعم
                      </button>
                      <button
                        onClick={() => {
                          handleRejectRestaurant(selectedRestaurant.id);
                          setIsRestaurantModalOpen(false);
                        }}
                        className="px-6 py-3 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-xl hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors"
                      >
                        رفض المطعم
                      </button>
                    </>
                  )}
                  
                  {selectedRestaurant.status === 'suspended' && (
                    <button
                      onClick={() => {
                        handleApproveRestaurant(selectedRestaurant.id);
                        setIsRestaurantModalOpen(false);
                      }}
                      className="px-6 py-3 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded-xl hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors"
                    >
                      إعادة تفعيل المطعم
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Application Details Modal */}
      {isApplicationModalOpen && selectedApplication && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsApplicationModalOpen(false)} />
          
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="relative w-full max-w-3xl bg-white dark:bg-gray-800 rounded-3xl shadow-2xl">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">
                    طلب انضمام مطعم جديد
                  </h3>
                  <button
                    onClick={() => setIsApplicationModalOpen(false)}
                    className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <XCircle className="w-5 h-5 text-gray-500" />
                  </button>
                </div>
              </div>
              
              <div className="p-6">
                <div className="flex items-center space-x-4 rtl:space-x-reverse mb-6">
                  <img 
                    src={selectedApplication.logo} 
                    alt={selectedApplication.name}
                    className="w-20 h-20 rounded-xl object-cover"
                  />
                  <div>
                    <h4 className="text-xl font-bold text-gray-800 dark:text-white rtl-font">
                      {selectedApplication.name}
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      {selectedApplication.nameEn}
                    </p>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                      <span className="text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 px-2 py-1 rounded-full">
                        {selectedApplication.cuisine}
                      </span>
                      <span className="text-xs bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-400 px-2 py-1 rounded-full">
                        {selectedApplication.city}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  {/* Contact Info */}
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                    <h5 className="font-semibold text-gray-800 dark:text-white mb-3 rtl-font">
                      معلومات الاتصال
                    </h5>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Users className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-300 rtl-font">
                          المالك: {selectedApplication.ownerName}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Mail className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-300">
                          {selectedApplication.email}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Phone className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-300">
                          {selectedApplication.phone}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <MapPin className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-300 rtl-font">
                          {selectedApplication.address}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Documents */}
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                    <h5 className="font-semibold text-gray-800 dark:text-white mb-3 rtl-font">
                      الوثائق المقدمة
                    </h5>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-300 rtl-font">رخصة العمل</span>
                        {selectedApplication.documents.businessLicense ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-500" />
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-300 rtl-font">شهادة سلامة الغذاء</span>
                        {selectedApplication.documents.foodSafety ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-500" />
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-300 rtl-font">شهادة ضريبية</span>
                        {selectedApplication.documents.taxCertificate ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-500" />
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-300 rtl-font">صور القائمة</span>
                        {selectedApplication.documents.menuPhotos ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-500" />
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-300 rtl-font">هوية المالك</span>
                        {selectedApplication.documents.ownerID ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-500" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Notes */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 mb-6">
                  <h5 className="font-semibold text-gray-800 dark:text-white mb-3 rtl-font">
                    ملاحظات
                  </h5>
                  <p className="text-gray-600 dark:text-gray-300 rtl-font">
                    {selectedApplication.notes}
                  </p>
                </div>

                {/* Rejection Reason */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                    سبب الرفض (اختياري)
                  </label>
                  <textarea
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                    placeholder="أدخل سبب الرفض هنا..."
                  ></textarea>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 rtl:space-x-reverse">
                  <button
                    onClick={() => setIsApplicationModalOpen(false)}
                    className="px-6 py-3 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    إغلاق
                  </button>
                  <button
                    onClick={() => handleRejectApplication(selectedApplication.id)}
                    className="px-6 py-3 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-xl hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors"
                  >
                    رفض
                  </button>
                  <button
                    onClick={() => handleApproveApplication(selectedApplication.id)}
                    className="px-6 py-3 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded-xl hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors"
                  >
                    قبول
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Commission Modal */}
      {isCommissionModalOpen && selectedRestaurant && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsCommissionModalOpen(false)} />
          
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="relative w-full max-w-md bg-white dark:bg-gray-800 rounded-3xl shadow-2xl">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white rtl-font">
                    تعديل نسبة العمولة
                  </h3>
                  <button
                    onClick={() => setIsCommissionModalOpen(false)}
                    className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <XCircle className="w-5 h-5 text-gray-500" />
                  </button>
                </div>
              </div>
              
              <div className="p-6">
                <div className="flex items-center space-x-4 rtl:space-x-reverse mb-6">
                  <img 
                    src={selectedRestaurant.logo} 
                    alt={selectedRestaurant.name}
                    className="w-16 h-16 rounded-xl object-cover"
                  />
                  <div>
                    <h4 className="font-bold text-gray-800 dark:text-white rtl-font">
                      {selectedRestaurant.name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {selectedRestaurant.city} - {selectedRestaurant.cuisine}
                    </p>
                  </div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                    نسبة العمولة الحالية
                  </label>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-300 rtl-font">النسبة الحالية</span>
                    <span className="text-xl font-bold text-primary">{selectedRestaurant.commission}%</span>
                  </div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                    النسبة الجديدة
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    defaultValue={selectedRestaurant.commission}
                    className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                  />
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 rtl-font">
                    أدخل قيمة بين 0 و 100
                  </p>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                    سبب التغيير (اختياري)
                  </label>
                  <textarea
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                    placeholder="أدخل سبب تغيير نسبة العمولة..."
                  ></textarea>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 rtl:space-x-reverse">
                  <button
                    onClick={() => setIsCommissionModalOpen(false)}
                    className="px-6 py-3 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={() => {
                      // Get the new commission value from the input
                      const newCommission = parseFloat((document.querySelector('input[type="number"]') as HTMLInputElement).value);
                      if (newCommission >= 0 && newCommission <= 100) {
                        handleUpdateCommission(selectedRestaurant.id, newCommission);
                        setIsCommissionModalOpen(false);
                      } else {
                        alert('يرجى إدخال قيمة صحيحة بين 0 و 100');
                      }
                    }}
                    className="px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary/90 transition-colors"
                  >
                    تحديث العمولة
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RestaurantsManagement;