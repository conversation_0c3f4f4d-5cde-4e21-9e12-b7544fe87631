import React, { useState } from 'react';
import { ChevronDown, ChevronUp, HelpCircle, MessageCircle, Phone, Mail } from 'lucide-react';

const FAQ = () => {
  const [openItem, setOpenItem] = useState<number | null>(0);

  const faqs = [
    {
      question: "كم يستغرق التوصيل؟",
      questionEn: "How long does delivery take?",
      answer: "معظم الطلبات يتم توصيلها خلال 15-30 دقيقة، حسب موقعك ووقت تحضير المطعم. يمكنك تتبع طلبك في الوقت الفعلي من خلال تطبيقنا.",
      answerEn: "Most orders are delivered within 15-30 minutes, depending on your location and the restaurant's preparation time. You can track your order in real-time through our app.",
      category: "delivery"
    },
    {
      question: "ما هي طرق الدفع المقبولة؟",
      questionEn: "What payment methods do you accept?",
      answer: "نقبل جميع البطاقات الائتمانية الرئيسية، الدفع عند التسليم، وطرق الدفع المحمول بما في ذلك CIH Mobile و Inwi Money و Orange Money.",
      answerEn: "We accept all major credit cards, cash on delivery, and mobile payment methods including CIH Mobile, Inwi Money, and Orange Money.",
      category: "payment"
    },
    {
      question: "هل هناك رسوم توصيل؟",
      questionEn: "Are there any delivery fees?",
      answer: "رسوم التوصيل تختلف حسب المطعم والموقع، عادة تتراوح من 10-25 درهم. العديد من المطاعم تقدم توصيل مجاني على الطلبات فوق مبلغ معين.",
      answerEn: "Delivery fees vary by restaurant and location, typically ranging from 10-25 MAD. Many restaurants offer free delivery on orders above a certain amount.",
      category: "delivery"
    },
    {
      question: "هل يمكنني تتبع طلبي؟",
      questionEn: "Can I track my order?",
      answer: "نعم! يمكنك تتبع طلبك في الوقت الفعلي من خلال تطبيقنا المحمول أو الموقع الإلكتروني. ستتلقى إشعارات في كل خطوة من العملية.",
      answerEn: "Yes! You can track your order in real-time through our mobile app or website. You'll receive notifications at every step of the process.",
      category: "tracking"
    },
    {
      question: "ماذا لو لم أكن راضياً عن طلبي؟",
      questionEn: "What if I'm not satisfied with my order?",
      answer: "نحن نقدم ضمان رضا 100%. إذا لم تكن راضياً عن طلبك، تواصل مع فريق الدعم خلال 24 ساعة للحصول على استرداد أو استبدال.",
      answerEn: "We offer a 100% satisfaction guarantee. If you're not happy with your order, contact our support team within 24 hours for a refund or replacement.",
      category: "support"
    },
    {
      question: "هل تقومون بالتوصيل في جميع مناطق المغرب؟",
      questionEn: "Do you deliver to all areas in Morocco?",
      answer: "نحن نخدم حالياً المدن الكبرى بما في ذلك الدار البيضاء، الرباط، مراكش، فاس، وطنجة. نحن نتوسع لمدن أخرى قريباً!",
      answerEn: "We currently serve major cities including Casablanca, Rabat, Marrakech, Fez, and Tangier. We're expanding to more cities soon!",
      category: "delivery"
    },
    {
      question: "كيف يمكنني إلغاء طلبي؟",
      questionEn: "How can I cancel my order?",
      answer: "يمكنك إلغاء طلبك مجاناً خلال 5 دقائق من تأكيد الطلب. بعد ذلك، قد تطبق رسوم إلغاء حسب حالة الطلب.",
      answerEn: "You can cancel your order for free within 5 minutes of confirmation. After that, cancellation fees may apply depending on the order status.",
      category: "orders"
    },
    {
      question: "هل يمكنني جدولة طلب للمستقبل؟",
      questionEn: "Can I schedule an order for later?",
      answer: "نعم! يمكنك جدولة طلبك حتى 7 أيام مقدماً. اختر الوقت والتاريخ المناسب لك عند إتمام الطلب.",
      answerEn: "Yes! You can schedule your order up to 7 days in advance. Choose your preferred time and date when completing your order.",
      category: "orders"
    }
  ];

  const categories = [
    { id: 'all', label: 'الكل', icon: <HelpCircle className="w-4 h-4" /> },
    { id: 'delivery', label: 'التوصيل', icon: <HelpCircle className="w-4 h-4" /> },
    { id: 'payment', label: 'الدفع', icon: <HelpCircle className="w-4 h-4" /> },
    { id: 'orders', label: 'الطلبات', icon: <HelpCircle className="w-4 h-4" /> },
    { id: 'support', label: 'الدعم', icon: <HelpCircle className="w-4 h-4" /> }
  ];

  const [activeCategory, setActiveCategory] = useState('all');

  const filteredFaqs = activeCategory === 'all' 
    ? faqs 
    : faqs.filter(faq => faq.category === activeCategory);

  return (
    <section className="py-24 bg-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-72 h-72 bg-blue-400/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <HelpCircle className="w-4 h-4" />
            <span className="rtl-font">الأسئلة الشائعة</span>
          </div>
          <h2 className="text-5xl font-bold text-accent mb-6 rtl-font">
            الأسئلة
            <br />
            <span className="text-primary">الشائعة</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed rtl-font">
            اعثر على إجابات للأسئلة الشائعة حول خدمة توصيل الطعام من وصلتي.
          </p>
        </div>

        {/* Category Filters */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 rounded-2xl font-semibold transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-primary text-white shadow-lg scale-105'
                  : 'bg-white text-gray-700 hover:bg-gray-50 hover:scale-105 shadow-md'
              }`}
            >
              {category.icon}
              <span className="rtl-font">{category.label}</span>
            </button>
          ))}
        </div>

        <div className="max-w-4xl mx-auto">
          {/* FAQ Items */}
          <div className="space-y-4 mb-16">
            {filteredFaqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                <button
                  className="w-full p-6 text-right hover:bg-gray-50 transition-colors duration-200 flex items-center justify-between"
                  onClick={() => setOpenItem(openItem === index ? null : index)}
                >
                  <h3 className="text-lg font-bold text-accent pr-4 rtl-font">{faq.question}</h3>
                  <div className="flex-shrink-0">
                    {openItem === index ? (
                      <ChevronUp className="w-6 h-6 text-primary" />
                    ) : (
                      <ChevronDown className="w-6 h-6 text-primary" />
                    )}
                  </div>
                </button>
                
                {openItem === index && (
                  <div className="px-6 pb-6 -mt-2">
                    <div className="bg-gray-50 rounded-xl p-4">
                      <p className="text-gray-700 leading-relaxed rtl-font">{faq.answer}</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Contact Support */}
          <div className="bg-white rounded-3xl shadow-2xl p-8 text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <MessageCircle className="w-8 h-8 text-primary" />
            </div>
            
            <h3 className="text-2xl font-bold text-accent mb-4 rtl-font">لا تزال لديك أسئلة؟</h3>
            <p className="text-gray-600 mb-8 rtl-font">فريق الدعم الخاص بنا متاح 24/7 لمساعدتك</p>
            
            <div className="grid md:grid-cols-3 gap-4">
              <button 
                onClick={() => alert('سيتم فتح الدردشة المباشرة...')}
                className="flex items-center justify-center space-x-3 rtl:space-x-reverse bg-primary text-white px-6 py-4 rounded-2xl font-semibold hover:bg-primary/90 hover:scale-105 transition-all duration-300"
              >
                <MessageCircle className="w-5 h-5" />
                <span className="rtl-font">دردشة مباشرة</span>
              </button>
              
              <button 
                onClick={() => window.open('tel:+2125XXXXXXX')}
                className="flex items-center justify-center space-x-3 rtl:space-x-reverse bg-gray-100 text-gray-700 px-6 py-4 rounded-2xl font-semibold hover:bg-gray-200 hover:scale-105 transition-all duration-300"
              >
                <Phone className="w-5 h-5" />
                <span className="rtl-font">اتصل بنا</span>
              </button>
              
              <button 
                onClick={() => window.open('mailto:<EMAIL>')}
                className="flex items-center justify-center space-x-3 rtl:space-x-reverse bg-gray-100 text-gray-700 px-6 py-4 rounded-2xl font-semibold hover:bg-gray-200 hover:scale-105 transition-all duration-300"
              >
                <Mail className="w-5 h-5" />
                <span className="rtl-font">أرسل إيميل</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;