import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Advanced Theme Provider with multiple theme options
class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'selected_theme';

  DriverThemeMode _currentTheme = DriverThemeMode.system;
  bool _isInitialized = false;

  DriverThemeMode get currentTheme => _currentTheme;
  bool get isInitialized => _isInitialized;

  /// Initialize theme from saved preferences
  Future<void> initializeTheme() async {
    if (_isInitialized) return;
    
    final prefs = await SharedPreferences.getInstance();
    
    // Load saved theme
    final savedTheme = prefs.getString(_themeKey);
    if (savedTheme != null) {
      _currentTheme = DriverThemeMode.values.firstWhere(
        (theme) => theme.name == savedTheme,
        orElse: () => DriverThemeMode.system,
      );
    }
    
    // Custom colors removed
    
    _isInitialized = true;
    notifyListeners();
  }

  /// Change theme and save to preferences
  Future<void> changeTheme(DriverThemeMode theme) async {
    if (_currentTheme == theme) return;
    
    _currentTheme = theme;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_themeKey, theme.name);
    
    notifyListeners();
  }



  /// Get effective theme mode for MaterialApp
  ThemeMode get effectiveThemeMode {
    switch (_currentTheme) {
      case DriverThemeMode.light:
        return ThemeMode.light;
      case DriverThemeMode.dark:
        return ThemeMode.dark;
      case DriverThemeMode.system:
        return ThemeMode.system;
      case DriverThemeMode.amoled:
        return ThemeMode.dark;
    }
  }

  /// Get current theme data
  ThemeData getThemeData(Brightness brightness) {
    switch (_currentTheme) {
      case DriverThemeMode.light:
        return _buildLightTheme();
      case DriverThemeMode.dark:
        return _buildDarkTheme();
      case DriverThemeMode.amoled:
        return _buildAmoledTheme();
      case DriverThemeMode.system:
        return brightness == Brightness.dark
            ? _buildDarkTheme()
            : _buildLightTheme();
    }
  }

  /// Build light theme
  ThemeData _buildLightTheme() {
    const primaryColor = Color(0xFF11B96F);
    const backgroundColor = Color(0xFFF5F5F5);
    const surfaceColor = Colors.white;
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      cardColor: surfaceColor,
      appBarTheme: const AppBarTheme(
        backgroundColor: backgroundColor,
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: true,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      cardTheme: CardTheme(
        color: surfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return Colors.grey[400];
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor.withValues(alpha: 0.5);
          }
          return Colors.grey[300];
        }),
      ),
    );
  }

  /// Build dark theme
  ThemeData _buildDarkTheme() {
    const primaryColor = Color(0xFF11B96F);
    const backgroundColor = Color(0xFF0F231A);
    const surfaceColor = Color(0xFF1B3B2E);
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      cardColor: surfaceColor,
      appBarTheme: const AppBarTheme(
        backgroundColor: backgroundColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      cardTheme: CardTheme(
        color: surfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[700]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[700]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return Colors.grey[600];
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor.withValues(alpha: 0.5);
          }
          return Colors.grey[800];
        }),
      ),
    );
  }

  /// Build AMOLED theme (Pure black)
  ThemeData _buildAmoledTheme() {
    const primaryColor = Color(0xFF11B96F);
    const backgroundColor = Colors.black;
    const surfaceColor = Color(0xFF111111);
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      cardColor: surfaceColor,
      appBarTheme: const AppBarTheme(
        backgroundColor: backgroundColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      cardTheme: CardTheme(
        color: surfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return Colors.grey[600];
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryColor.withValues(alpha: 0.5);
          }
          return Colors.grey[900];
        }),
      ),
    );
  }



  /// Create MaterialColor from Color
  MaterialColor _createMaterialColor(Color color) {
    final strengths = <double>[.05];
    final swatch = <int, Color>{};
    final int r = (color.r * 255).round(), g = (color.g * 255).round(), b = (color.b * 255).round();

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }

    for (final strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }

    return MaterialColor(color.toARGB32(), swatch);
  }



  /// Get theme name for display
  String getThemeName(DriverThemeMode theme) {
    switch (theme) {
      case DriverThemeMode.light:
        return 'Light';
      case DriverThemeMode.dark:
        return 'Dark';
      case DriverThemeMode.amoled:
        return 'AMOLED';
      case DriverThemeMode.system:
        return 'System';
    }
  }

  /// Get theme description
  String getThemeDescription(DriverThemeMode theme) {
    switch (theme) {
      case DriverThemeMode.light:
        return 'Clean and bright interface';
      case DriverThemeMode.dark:
        return 'Easy on the eyes in low light';
      case DriverThemeMode.amoled:
        return 'Pure black for OLED displays';
      case DriverThemeMode.system:
        return 'Follows system settings';
    }
  }

  /// Get theme icon
  IconData getThemeIcon(DriverThemeMode theme) {
    switch (theme) {
      case DriverThemeMode.light:
        return Icons.light_mode;
      case DriverThemeMode.dark:
        return Icons.dark_mode;
      case DriverThemeMode.amoled:
        return Icons.brightness_2;
      case DriverThemeMode.system:
        return Icons.settings_brightness;
    }
  }
}

/// Available theme modes
enum DriverThemeMode {
  light,
  dark,
  amoled,
  system,
}
