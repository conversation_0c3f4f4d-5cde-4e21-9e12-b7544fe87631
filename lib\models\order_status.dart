import 'package:flutter/material.dart';

/// Order status enumeration for tracking delivery lifecycle
enum OrderStatus {
  incoming,           // New order received
  accepted,          // Driver accepted the order
  goingToRestaurant, // Driver heading to restaurant
  atRestaurant,      // Driver arrived at restaurant
  pickedUp,          // Order picked up from restaurant
  goingToCustomer,   // Driver heading to customer
  delivered,         // Order delivered successfully
  cancelled,         // Order cancelled
}

extension OrderStatusExtension on OrderStatus {
  /// Display text for the order status
  String get displayText {
    switch (this) {
      case OrderStatus.incoming:
        return 'New Order';
      case OrderStatus.accepted:
        return 'Order Accepted';
      case OrderStatus.goingToRestaurant:
        return 'Going to Restaurant';
      case OrderStatus.atRestaurant:
        return 'At Restaurant';
      case OrderStatus.pickedUp:
        return 'Order Picked Up';
      case OrderStatus.goingToCustomer:
        return 'Going to Customer';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Description for the order status
  String get description {
    switch (this) {
      case OrderStatus.incoming:
        return 'New delivery order received';
      case OrderStatus.accepted:
        return 'You accepted this order';
      case OrderStatus.goingToRestaurant:
        return 'Navigate to restaurant to pick up order';
      case OrderStatus.atRestaurant:
        return 'Confirm arrival and wait for order';
      case OrderStatus.pickedUp:
        return 'Order collected, heading to customer';
      case OrderStatus.goingToCustomer:
        return 'Navigate to customer location';
      case OrderStatus.delivered:
        return 'Order successfully delivered';
      case OrderStatus.cancelled:
        return 'Order was cancelled';
    }
  }

  /// Color associated with the order status
  Color get color {
    switch (this) {
      case OrderStatus.incoming:
        return const Color(0xFF2196F3); // Blue
      case OrderStatus.accepted:
        return const Color(0xFF11B96F); // Green
      case OrderStatus.goingToRestaurant:
        return const Color(0xFFFF9500); // Orange
      case OrderStatus.atRestaurant:
        return const Color(0xFFFF9500); // Orange
      case OrderStatus.pickedUp:
        return const Color(0xFF11B96F); // Green
      case OrderStatus.goingToCustomer:
        return const Color(0xFF2196F3); // Blue
      case OrderStatus.delivered:
        return const Color(0xFF4CAF50); // Success Green
      case OrderStatus.cancelled:
        return const Color(0xFFFF3B30); // Red
    }
  }

  /// Icon associated with the order status
  IconData get icon {
    switch (this) {
      case OrderStatus.incoming:
        return Icons.notification_important;
      case OrderStatus.accepted:
        return Icons.check_circle;
      case OrderStatus.goingToRestaurant:
        return Icons.directions_car;
      case OrderStatus.atRestaurant:
        return Icons.restaurant;
      case OrderStatus.pickedUp:
        return Icons.shopping_bag;
      case OrderStatus.goingToCustomer:
        return Icons.delivery_dining;
      case OrderStatus.delivered:
        return Icons.done_all;
      case OrderStatus.cancelled:
        return Icons.cancel;
    }
  }

  /// Next possible status in the workflow
  OrderStatus? get nextStatus {
    switch (this) {
      case OrderStatus.incoming:
        return OrderStatus.accepted;
      case OrderStatus.accepted:
        return OrderStatus.goingToRestaurant;
      case OrderStatus.goingToRestaurant:
        return OrderStatus.atRestaurant;
      case OrderStatus.atRestaurant:
        return OrderStatus.pickedUp;
      case OrderStatus.pickedUp:
        return OrderStatus.goingToCustomer;
      case OrderStatus.goingToCustomer:
        return OrderStatus.delivered;
      case OrderStatus.delivered:
        return null; // Final status
      case OrderStatus.cancelled:
        return null; // Final status
    }
  }

  /// Action button text for current status
  String get actionButtonText {
    switch (this) {
      case OrderStatus.incoming:
        return 'Accept Order';
      case OrderStatus.accepted:
        return 'Start Navigation';
      case OrderStatus.goingToRestaurant:
        return 'Arrived at Restaurant';
      case OrderStatus.atRestaurant:
        return 'Picked Up Order';
      case OrderStatus.pickedUp:
        return 'Navigate to Customer';
      case OrderStatus.goingToCustomer:
        return 'Delivered';
      case OrderStatus.delivered:
        return 'Complete';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Whether this status allows cancellation
  bool get canCancel {
    return this != OrderStatus.delivered && this != OrderStatus.cancelled;
  }
}
