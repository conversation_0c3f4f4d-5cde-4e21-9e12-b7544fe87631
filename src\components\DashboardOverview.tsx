import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Car, 
  Clock, 
  Star, 
  ShoppingBag, 
  DollarSign,
  MapPin,
  Activity,
  Award,
  ChefHat,
  Eye,
  RefreshCw,
  Calendar,
  Filter,
  Download,
  BarChart3,
  <PERSON>Chart,
  Target,
  Zap,
  Heart,
  ThumbsUp
} from 'lucide-react';

interface KPI {
  title: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}

interface Driver {
  id: string;
  name: string;
  lat: number;
  lng: number;
  status: 'available' | 'busy' | 'offline';
  rating: number;
  deliveries: number;
}

interface Order {
  id: string;
  restaurantLat: number;
  restaurantLng: number;
  customerLat: number;
  customerLng: number;
  status: 'preparing' | 'pickup' | 'delivery';
  estimatedTime: number;
}

const DashboardOverview = () => {
  const [timeFilter, setTimeFilter] = useState('today');
  const [isLoading, setIsLoading] = useState(false);

  // Mock data for KPIs
  const kpis: KPI[] = [
    {
      title: 'إجمالي الطلبات',
      value: '1,247',
      change: '+12.5%',
      changeType: 'increase',
      icon: <ShoppingBag className="w-6 h-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'إجمالي الإيرادات',
      value: '45,280 درهم',
      change: '+23.1%',
      changeType: 'increase',
      icon: <DollarSign className="w-6 h-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'العملاء النشطين',
      value: '8,942',
      change: '+8.7%',
      changeType: 'increase',
      icon: <Users className="w-6 h-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'السائقين المتاحين',
      value: '234',
      change: '+5.2%',
      changeType: 'increase',
      icon: <Car className="w-6 h-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      title: 'متوسط وقت التوصيل',
      value: '18 دقيقة',
      change: '-2.3%',
      changeType: 'increase',
      icon: <Clock className="w-6 h-6" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50'
    },
    {
      title: 'معدل رضا العملاء',
      value: '4.8/5',
      change: '+0.2',
      changeType: 'increase',
      icon: <Star className="w-6 h-6" />,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50'
    }
  ];

  // Mock data for drivers
  const drivers: Driver[] = [
    { id: '1', name: 'محمد الإدريسي', lat: 33.5731, lng: -7.5898, status: 'available', rating: 4.8, deliveries: 12 },
    { id: '2', name: 'عبد الرحمن بنعلي', lat: 34.0209, lng: -6.8416, status: 'busy', rating: 4.6, deliveries: 8 },
    { id: '3', name: 'يوسف الحسني', lat: 31.6295, lng: -7.9811, status: 'available', rating: 4.9, deliveries: 15 },
    { id: '4', name: 'أحمد المرابط', lat: 34.2654, lng: -6.5802, status: 'busy', rating: 4.7, deliveries: 10 },
    { id: '5', name: 'سعيد الكرامي', lat: 35.7595, lng: -5.8340, status: 'available', rating: 4.5, deliveries: 7 }
  ];

  // Mock data for orders
  const activeOrders: Order[] = [
    { id: '1', restaurantLat: 33.5731, restaurantLng: -7.5898, customerLat: 33.5831, customerLng: -7.5798, status: 'delivery', estimatedTime: 15 },
    { id: '2', restaurantLat: 34.0209, restaurantLng: -6.8416, customerLat: 34.0309, customerLng: -6.8316, status: 'pickup', estimatedTime: 8 },
    { id: '3', restaurantLat: 31.6295, restaurantLng: -7.9811, customerLat: 31.6395, customerLng: -7.9711, status: 'preparing', estimatedTime: 25 }
  ];

  // Top performing restaurants
  const topRestaurants = [
    { name: 'مطعم الأصالة', orders: 156, rating: 4.9, revenue: '12,450 درهم' },
    { name: 'برجر ستيشن', orders: 134, rating: 4.7, revenue: '9,870 درهم' },
    { name: 'حلويات الدار البيضاء', orders: 98, rating: 4.8, revenue: '7,650 درهم' },
    { name: 'مشاوي مراكش', orders: 87, rating: 4.6, revenue: '6,890 درهم' },
    { name: 'بيسترو الأطلس', orders: 76, rating: 4.5, revenue: '5,430 درهم' }
  ];

  // Most ordered dishes
  const topDishes = [
    { name: 'طاجين الدجاج', orders: 89, restaurant: 'مطعم الأصالة' },
    { name: 'برجر كلاسيك', orders: 76, restaurant: 'برجر ستيشن' },
    { name: 'كسكس باللحم', orders: 65, restaurant: 'مطعم الأصالة' },
    { name: 'شباكية', orders: 54, restaurant: 'حلويات الدار البيضاء' },
    { name: 'كباب مشوي', orders: 43, restaurant: 'مشاوي مراكش' }
  ];

  // Most active drivers
  const topDrivers = [
    { name: 'يوسف الحسني', deliveries: 15, rating: 4.9, earnings: '890 درهم' },
    { name: 'محمد الإدريسي', deliveries: 12, rating: 4.8, earnings: '720 درهم' },
    { name: 'أحمد المرابط', deliveries: 10, rating: 4.7, earnings: '650 درهم' },
    { name: 'عبد الرحمن بنعلي', deliveries: 8, rating: 4.6, earnings: '540 درهم' },
    { name: 'سعيد الكرامي', deliveries: 7, rating: 4.5, earnings: '480 درهم' }
  ];

  // Recent reviews
  const recentReviews = [
    { customer: 'فاطمة الزهراء', rating: 5, comment: 'خدمة ممتازة وتوصيل سريع', time: 'منذ 5 دقائق' },
    { customer: 'أحمد محمد', rating: 4, comment: 'طعام لذيذ ولكن التوصيل تأخر قليلاً', time: 'منذ 15 دقيقة' },
    { customer: 'سارة الحسني', rating: 5, comment: 'أفضل تطبيق توصيل في المغرب', time: 'منذ 30 دقيقة' },
    { customer: 'محمد العلوي', rating: 4, comment: 'جودة عالية وأسعار معقولة', time: 'منذ ساعة' }
  ];

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 2000);
  };

  const getDriverStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-500';
      case 'busy': return 'bg-orange-500';
      case 'offline': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'preparing': return 'bg-yellow-500';
      case 'pickup': return 'bg-blue-500';
      case 'delivery': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white rtl-font">
            نظرة عامة
          </h1>
          <p className="text-gray-600 dark:text-gray-400 rtl-font">
            مؤشرات الأداء الرئيسية ومتابعة العمليات في الوقت الفعلي
          </p>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <select
            value={timeFilter}
            onChange={(e) => setTimeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="year">هذا العام</option>
          </select>
          
          <button 
            onClick={handleRefresh}
            disabled={isLoading}
            className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>تحديث</span>
          </button>
          
          <button className="bg-primary text-white px-6 py-2 rounded-xl font-semibold hover:bg-primary/90 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
            <Download className="w-4 h-4" />
            <span>تصدير التقرير</span>
          </button>
        </div>
      </div>

      {/* KPIs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {kpis.map((kpi, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${kpi.bgColor} dark:bg-gray-700`}>
                <div className={kpi.color}>
                  {kpi.icon}
                </div>
              </div>
              <div className={`flex items-center space-x-1 rtl:space-x-reverse text-sm font-medium ${
                kpi.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
              }`}>
                {kpi.changeType === 'increase' ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                <span>{kpi.change}</span>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-1">
              {kpi.value}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
              {kpi.title}
            </p>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid lg:grid-cols-3 gap-6">
        {/* Interactive Map */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-800 dark:text-white rtl-font">
              الخريطة التفاعلية
            </h3>
            <div className="flex space-x-2 rtl:space-x-reverse">
              <button className="px-3 py-1 rounded-lg text-sm bg-primary text-white">
                السائقين
              </button>
              <button className="px-3 py-1 rounded-lg text-sm bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                الطلبات
              </button>
              <button className="px-3 py-1 rounded-lg text-sm bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                المطاعم
              </button>
            </div>
          </div>
          
          {/* Map Placeholder */}
          <div className="h-96 bg-gray-100 dark:bg-gray-700 rounded-xl relative overflow-hidden">
            {/* Morocco Map Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-green-50 dark:from-gray-600 dark:to-gray-700">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="w-16 h-16 text-primary mx-auto mb-4" />
                  <h4 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2 rtl-font">
                    خريطة المغرب التفاعلية
                  </h4>
                  <p className="text-gray-500 dark:text-gray-400 rtl-font">
                    عرض مواقع السائقين والطلبات في الوقت الفعلي
                  </p>
                </div>
              </div>
              
              {/* Mock Driver Locations */}
              {drivers.map((driver, index) => (
                <div
                  key={driver.id}
                  className="absolute w-4 h-4 rounded-full border-2 border-white shadow-lg cursor-pointer hover:scale-125 transition-transform"
                  style={{
                    left: `${20 + index * 15}%`,
                    top: `${30 + index * 10}%`,
                  }}
                  title={`${driver.name} - ${driver.status}`}
                >
                  <div className={`w-full h-full rounded-full ${getDriverStatusColor(driver.status)}`}></div>
                </div>
              ))}
              
              {/* Mock Order Routes */}
              {activeOrders.map((order, index) => (
                <div
                  key={order.id}
                  className="absolute w-3 h-3 rounded-full border border-white shadow-md"
                  style={{
                    left: `${40 + index * 20}%`,
                    top: `${50 + index * 8}%`,
                  }}
                  title={`طلب ${order.id} - ${order.status}`}
                >
                  <div className={`w-full h-full rounded-full ${getOrderStatusColor(order.status)}`}></div>
                </div>
              ))}
            </div>
            
            {/* Map Legend */}
            <div className="absolute bottom-4 left-4 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-lg">
              <div className="space-y-2 text-xs">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300 rtl-font">سائق متاح</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300 rtl-font">سائق مشغول</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300 rtl-font">طلب جاري</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="space-y-6">
          {/* Live Activity */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
                النشاط المباشر
              </h3>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-600 font-medium">مباشر</span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <ShoppingBag className="w-5 h-5 text-green-600" />
                  <span className="text-sm font-medium text-gray-800 dark:text-white rtl-font">طلبات جديدة</span>
                </div>
                <span className="text-lg font-bold text-green-600">23</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Car className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-gray-800 dark:text-white rtl-font">سائقين متاحين</span>
                </div>
                <span className="text-lg font-bold text-blue-600">47</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Clock className="w-5 h-5 text-orange-600" />
                  <span className="text-sm font-medium text-gray-800 dark:text-white rtl-font">متوسط الانتظار</span>
                </div>
                <span className="text-lg font-bold text-orange-600">12 د</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 rtl-font">
              إجراءات سريعة
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              <button className="flex flex-col items-center p-4 bg-primary/10 rounded-xl hover:bg-primary/20 transition-colors">
                <Users className="w-6 h-6 text-primary mb-2" />
                <span className="text-sm font-medium text-primary rtl-font">إدارة المستخدمين</span>
              </button>
              
              <button className="flex flex-col items-center p-4 bg-green-100 dark:bg-green-900/20 rounded-xl hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors">
                <ChefHat className="w-6 h-6 text-green-600 mb-2" />
                <span className="text-sm font-medium text-green-600 rtl-font">المطاعم</span>
              </button>
              
              <button className="flex flex-col items-center p-4 bg-blue-100 dark:bg-blue-900/20 rounded-xl hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors">
                <Car className="w-6 h-6 text-blue-600 mb-2" />
                <span className="text-sm font-medium text-blue-600 rtl-font">السائقين</span>
              </button>
              
              <button className="flex flex-col items-center p-4 bg-purple-100 dark:bg-purple-900/20 rounded-xl hover:bg-purple-200 dark:hover:bg-purple-900/40 transition-colors">
                <BarChart3 className="w-6 h-6 text-purple-600 mb-2" />
                <span className="text-sm font-medium text-purple-600 rtl-font">التقارير</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Stats Grid */}
      <div className="grid lg:grid-cols-4 gap-6">
        {/* Top Restaurants */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
              أفضل المطاعم
            </h3>
            <Award className="w-5 h-5 text-yellow-500" />
          </div>
          
          <div className="space-y-4">
            {topRestaurants.map((restaurant, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <span className="text-sm font-bold text-primary">#{index + 1}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 dark:text-white text-sm rtl-font">
                      {restaurant.name}
                    </h4>
                    <div className="flex items-center space-x-1 rtl:space-x-reverse">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-gray-500">{restaurant.rating}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-bold text-gray-800 dark:text-white">
                    {restaurant.orders}
                  </div>
                  <div className="text-xs text-gray-500 rtl-font">طلب</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Dishes */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
              أكثر الأطباق طلباً
            </h3>
            <Target className="w-5 h-5 text-orange-500" />
          </div>
          
          <div className="space-y-4">
            {topDishes.map((dish, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                    <span className="text-sm font-bold text-orange-600">#{index + 1}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 dark:text-white text-sm rtl-font">
                      {dish.name}
                    </h4>
                    <p className="text-xs text-gray-500 rtl-font">{dish.restaurant}</p>
                  </div>
                </div>
                <div className="text-sm font-bold text-gray-800 dark:text-white">
                  {dish.orders}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Drivers */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
              السائقين الأكثر نشاطاً
            </h3>
            <Zap className="w-5 h-5 text-blue-500" />
          </div>
          
          <div className="space-y-4">
            {topDrivers.map((driver, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 dark:text-white text-sm rtl-font">
                      {driver.name}
                    </h4>
                    <div className="flex items-center space-x-1 rtl:space-x-reverse">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-gray-500">{driver.rating}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-bold text-gray-800 dark:text-white">
                    {driver.deliveries}
                  </div>
                  <div className="text-xs text-gray-500 rtl-font">توصيلة</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Reviews */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white rtl-font">
              التقييمات الحديثة
            </h3>
            <ThumbsUp className="w-5 h-5 text-green-500" />
          </div>
          
          <div className="space-y-4">
            {recentReviews.map((review, index) => (
              <div key={index} className="border-b border-gray-100 dark:border-gray-700 pb-3 last:border-b-0">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-800 dark:text-white text-sm rtl-font">
                    {review.customer}
                  </h4>
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    {[...Array(review.rating)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-1 rtl-font">
                  "{review.comment}"
                </p>
                <p className="text-xs text-gray-500">{review.time}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;