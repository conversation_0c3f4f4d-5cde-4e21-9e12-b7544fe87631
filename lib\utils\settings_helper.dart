import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/settings/settings_screen_en.dart';
import '../providers/language_provider.dart';

/// Helper class for settings screen navigation and language handling
class SettingsHelper {
  
  /// Navigate to appropriate settings screen based on language
  static void navigateToSettings(BuildContext context, {String? language}) {
    final settingsScreen = getSettingsScreen(language);

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => settingsScreen,
      ),
    );
  }

  /// Navigate to settings using current language from LanguageProvider
  static void navigateToSettingsWithProvider(BuildContext context) {
    final languageProvider = context.read<LanguageProvider>();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => languageProvider.isEnglish
            ? const SettingsScreenEN()
            : const SettingsScreen(),
      ),
    );
  }
  
  /// Get appropriate settings screen widget based on language
  static Widget getSettingsScreen(String? language) {
    // Default to Arabic if language is null or not specified
    if (language == null) {
      return const SettingsScreen(); // Arabic version
    }
    
    // Check language preference
    switch (language.toLowerCase()) {
      case 'english':
      case 'en':
        return const SettingsScreenEN();
      case 'arabic':
      case 'ar':
      case 'العربية':
        return const SettingsScreen();
      default:
        // Default to Arabic for unknown languages
        return const SettingsScreen();
    }
  }
  
  /// Get settings screen based on locale
  static Widget getSettingsScreenByLocale(Locale? locale) {
    if (locale == null) {
      return const SettingsScreen(); // Default to Arabic
    }
    
    switch (locale.languageCode) {
      case 'en':
        return const SettingsScreenEN();
      case 'ar':
        return const SettingsScreen();
      default:
        return const SettingsScreen(); // Default to Arabic
    }
  }
  
  /// Navigate to settings with locale detection
  static void navigateToSettingsWithLocale(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final settingsScreen = getSettingsScreenByLocale(locale);
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => settingsScreen,
      ),
    );
  }
  
  /// Get supported languages list
  static List<String> getSupportedLanguages() {
    return ['العربية', 'English'];
  }
  
  /// Get language display name
  static String getLanguageDisplayName(String languageCode) {
    switch (languageCode.toLowerCase()) {
      case 'ar':
      case 'arabic':
        return 'العربية';
      case 'en':
      case 'english':
        return 'English';
      default:
        return 'العربية'; // Default
    }
  }
  
  /// Check if language is RTL
  static bool isRTL(String? language) {
    if (language == null) return true; // Default to RTL (Arabic)
    
    switch (language.toLowerCase()) {
      case 'arabic':
      case 'ar':
      case 'العربية':
        return true;
      case 'english':
      case 'en':
        return false;
      default:
        return true; // Default to RTL
    }
  }
  
  /// Get text direction based on language
  static TextDirection getTextDirection(String? language) {
    return isRTL(language) ? TextDirection.rtl : TextDirection.ltr;
  }
}

/// Extension for easy settings navigation
extension SettingsNavigation on BuildContext {
  /// Navigate to settings screen with automatic language detection
  void navigateToSettings({String? language}) {
    SettingsHelper.navigateToSettings(this, language: language);
  }
  
  /// Navigate to settings screen with locale detection
  void navigateToSettingsAuto() {
    SettingsHelper.navigateToSettingsWithLocale(this);
  }
}

/// Example usage:
/// 
/// ```dart
/// // Method 1: Direct navigation with language
/// SettingsHelper.navigateToSettings(context, language: 'English');
/// 
/// // Method 2: Using extension
/// context.navigateToSettings(language: 'العربية');
/// 
/// // Method 3: Auto-detect from locale
/// context.navigateToSettingsAuto();
/// 
/// // Method 4: Get widget directly
/// Widget settingsScreen = SettingsHelper.getSettingsScreen('English');
/// ```
