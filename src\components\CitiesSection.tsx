import React, { useState } from 'react';
import { MapPin, Users, Clock, Star, ChevronRight, Building, Truck, Award } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const CitiesSection = () => {
  const [selectedCity, setSelectedCity] = useState(0);
  const { t, isRTL } = useLanguage();

  const cities = [
    {
      id: 1,
      name: "الدار البيضاء",
      nameEn: "Casablanca",
      image: "https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 850,
      deliveryTime: "15-25 دقيقة",
      activeUsers: "500K+",
      rating: 4.9,
      isLaunched: true,
      launchDate: "2021",
      districts: ["عين الشق", "المعاريف", "الحي المحمدي", "سيدي البرنوصي", "عين السبع"],
      specialties: ["المأكولات المغربية", "الوجبات السريعة", "المأكولات الآسيوية", "الحلويات"]
    },
    {
      id: 2,
      name: "الرباط",
      nameEn: "Rabat",
      image: "https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 420,
      deliveryTime: "20-30 دقيقة",
      activeUsers: "200K+",
      rating: 4.8,
      isLaunched: true,
      launchDate: "2021",
      districts: ["أكدال", "حسان", "يعقوب المنصور", "الرياض", "السويسي"],
      specialties: ["المطبخ المغربي", "المأكولات الدولية", "المقاهي", "الحلويات التقليدية"]
    },
    {
      id: 3,
      name: "مراكش",
      nameEn: "Marrakech",
      image: "https://images.pexels.com/photos/1581384/pexels-photo-1581384.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 380,
      deliveryTime: "20-35 دقيقة",
      activeUsers: "180K+",
      rating: 4.7,
      isLaunched: true,
      launchDate: "2022",
      districts: ["جليز", "المدينة القديمة", "الحي الصناعي", "سيدي يوسف بن علي", "داوديات"],
      specialties: ["الطاجين التقليدي", "الكسكس", "الحلويات المراكشية", "المشاوي"]
    },
    {
      id: 4,
      name: "فاس",
      nameEn: "Fez",
      image: "https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 290,
      deliveryTime: "25-35 دقيقة",
      activeUsers: "120K+",
      rating: 4.8,
      isLaunched: true,
      launchDate: "2022",
      districts: ["فاس الجديد", "المدينة القديمة", "الأطلس", "بنسودة", "زواغة"],
      specialties: ["المطبخ الفاسي", "الحلويات التقليدية", "المعجنات", "الشاي المغربي"]
    },
    {
      id: 5,
      name: "طنجة",
      nameEn: "Tangier",
      image: "https://images.pexels.com/photos/1435904/pexels-photo-1435904.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 320,
      deliveryTime: "20-30 دقيقة",
      activeUsers: "150K+",
      rating: 4.6,
      isLaunched: true,
      launchDate: "2022",
      districts: ["المدينة القديمة", "الحي الإداري", "بني مكادة", "الشرف", "مغوغة"],
      specialties: ["المأكولات البحرية", "المطبخ الأندلسي", "الوجبات الدولية", "المقاهي الشعبية"]
    },
    {
      id: 6,
      name: "أكادير",
      nameEn: "Agadir",
      image: "https://images.pexels.com/photos/1058277/pexels-photo-1058277.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 250,
      deliveryTime: "25-35 دقيقة",
      activeUsers: "100K+",
      rating: 4.7,
      isLaunched: true,
      launchDate: "2023",
      districts: ["الحي الصناعي", "تالبرجت", "الداخلة", "القصبة", "فونتي"],
      specialties: ["المأكولات البحرية", "الأطباق الأمازيغية", "الفواكه البحرية", "المشاوي"]
    },
    {
      id: 7,
      name: "وجدة",
      nameEn: "Oujda",
      image: "https://images.pexels.com/photos/1109197/pexels-photo-1109197.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 180,
      deliveryTime: "25-40 دقيقة",
      activeUsers: "80K+",
      rating: 4.5,
      isLaunched: true,
      launchDate: "2023",
      districts: ["المدينة الجديدة", "الحي الجامعي", "السلام", "الوحدة", "لازاريت"],
      specialties: ["المطبخ الشرقي المغربي", "الحلويات التقليدية", "المعجنات", "الشاي والقهوة"]
    },
    {
      id: 8,
      name: "تطوان",
      nameEn: "Tetouan",
      image: "https://images.pexels.com/photos/1438672/pexels-photo-1438672.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 150,
      deliveryTime: "30-40 دقيقة",
      activeUsers: "60K+",
      rating: 4.6,
      isLaunched: true,
      launchDate: "2023",
      districts: ["المدينة العتيقة", "المرتيل", "سانية الرمل", "الملاح", "الحي الإداري"],
      specialties: ["المطبخ التطواني", "الحلويات الأندلسية", "المأكولات البحرية", "الشاي المنعنع"]
    },
    {
      id: 9,
      name: "مكناس",
      nameEn: "Meknes",
      image: "https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 200,
      deliveryTime: "25-35 دقيقة",
      activeUsers: "90K+",
      rating: 4.7,
      isLaunched: true,
      launchDate: "2023",
      districts: ["الحملة", "المرينيين", "الرياض", "توالة", "سيدي بابا"],
      specialties: ["الكسكس المكناسي", "الطاجين", "الحلويات المحلية", "زيت الزيتون"]
    },
    {
      id: 10,
      name: "الجديدة",
      nameEn: "El Jadida",
      image: "https://images.pexels.com/photos/1055272/pexels-photo-1055272.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 120,
      deliveryTime: "30-45 دقيقة",
      activeUsers: "50K+",
      rating: 4.5,
      isLaunched: true,
      launchDate: "2024",
      districts: ["المدينة البرتغالية", "الحي الجديد", "سيدي بوزيد", "الهوارة", "الصخيرات"],
      specialties: ["المأكولات البحرية الطازجة", "السردين المشوي", "الحلويات البحرية", "الأسماك المقلية"]
    },
    {
      id: 11,
      name: "خريبكة",
      nameEn: "Khouribga",
      image: "https://images.pexels.com/photos/1581384/pexels-photo-1581384.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 100,
      deliveryTime: "30-45 دقيقة",
      activeUsers: "40K+",
      rating: 4.4,
      isLaunched: true,
      launchDate: "2024",
      districts: ["المدينة الجديدة", "الحي الصناعي", "ولاد عزوز", "بوجنيبة", "الحي الإداري"],
      specialties: ["المطبخ المغربي التقليدي", "اللحوم المشوية", "الحلويات المحلية", "الخبز التقليدي"]
    },
    {
      id: 12,
      name: "بني ملال",
      nameEn: "Beni Mellal",
      image: "https://images.pexels.com/photos/1109197/pexels-photo-1109197.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      restaurants: 90,
      deliveryTime: "35-45 دقيقة",
      activeUsers: "35K+",
      rating: 4.3,
      isLaunched: true,
      launchDate: "2024",
      districts: ["المدينة الجديدة", "الحي الإداري", "القصيبة", "سيدي جابر", "تادلة"],
      specialties: ["الفواكه الطازجة", "العسل الطبيعي", "الحلويات التقليدية", "منتجات الألبان"]
    }
  ];

  const comingSoonCities = [
    { name: "الناظور", nameEn: "Nador", launchDate: "قريباً 2024" },
    { name: "الحسيمة", nameEn: "Al Hoceima", launchDate: "قريباً 2024" },
    { name: "ورزازات", nameEn: "Ouarzazate", launchDate: "2025" },
    { name: "الراشيدية", nameEn: "Errachidia", launchDate: "2025" }
  ];

  const currentCity = cities[selectedCity];

  return (
    <section className="py-24 bg-gradient-to-br from-background via-white to-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-blue-400/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <MapPin className="w-4 h-4" />
            <span className="rtl-font">{t('cities.badge')}</span>
          </div>
          <h2 className="text-5xl font-bold text-accent mb-6 rtl-font">
            {t('cities.title')}
            <br />
            <span className="text-primary">{t('cities.subtitle')}</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed rtl-font">
            {t('cities.description')}
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-all duration-300">
            <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Building className="w-6 h-6 text-primary" />
            </div>
            <div className="text-2xl font-bold text-primary mb-1">12</div>
            <div className="text-sm text-gray-600 rtl-font">{t('cities.available_cities')}</div>
          </div>
          
          <div className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-all duration-300">
            <div className="w-12 h-12 bg-orange-500/10 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Users className="w-6 h-6 text-orange-500" />
            </div>
            <div className="text-2xl font-bold text-primary mb-1">1.5M+</div>
            <div className="text-sm text-gray-600 rtl-font">{t('cities.active_users')}</div>
          </div>
          
          <div className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-all duration-300">
            <div className="w-12 h-12 bg-blue-500/10 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Truck className="w-6 h-6 text-blue-500" />
            </div>
            <div className="text-2xl font-bold text-primary mb-1">3000+</div>
            <div className="text-sm text-gray-600 rtl-font">{t('cities.partner_restaurants')}</div>
          </div>
          
          <div className="bg-white rounded-2xl p-6 shadow-lg text-center hover:shadow-xl transition-all duration-300">
            <div className="w-12 h-12 bg-purple-500/10 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Award className="w-6 h-6 text-purple-500" />
            </div>
            <div className="text-2xl font-bold text-primary mb-1">4.7</div>
            <div className="text-sm text-gray-600 rtl-font">{t('cities.average_rating')}</div>
          </div>
        </div>

        {/* Cities Grid */}
        <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-6 mb-16">
          {cities.map((city, index) => (
            <div 
              key={city.id}
              onClick={() => setSelectedCity(index)}
              className={`group cursor-pointer bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 ${
                selectedCity === index ? 'ring-2 ring-primary scale-105' : ''
              }`}
            >
              <div className="relative overflow-hidden">
                <img 
                  src={city.image} 
                  alt={city.name}
                  className="w-full h-40 object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                {/* Status Badge */}
                <div className="absolute top-3 right-3">
                  <div className="bg-primary text-white px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1 rtl:space-x-reverse">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    <span>{t('cities.available')}</span>
                  </div>
                </div>
                
                {/* Launch Year */}
                <div className="absolute bottom-3 left-3">
                  <div className="bg-black/70 text-white px-2 py-1 rounded-lg text-xs">
                    {t('cities.since')} {city.launchDate}
                  </div>
                </div>
              </div>
              
              <div className="p-4">
                <h3 className="text-lg font-bold text-accent mb-2 rtl-font group-hover:text-primary transition-colors">
                  {city.name}
                </h3>
                
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center justify-between">
                    <span className="rtl-font">{t('cities.restaurants')}</span>
                    <span className="font-semibold text-primary">{city.restaurants}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="rtl-font">{t('cities.delivery')}</span>
                    <span className="font-semibold">{city.deliveryTime}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="rtl-font">{t('cities.users')}</span>
                    <span className="font-semibold text-primary">{city.activeUsers}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="rtl-font">{t('cities.rating')}</span>
                    <div className="flex items-center space-x-1 rtl:space-x-reverse">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="font-semibold">{city.rating}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Selected City Details */}
        <div className="bg-white rounded-3xl shadow-2xl p-8 mb-16">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* City Image */}
            <div className="relative">
              <img 
                src={currentCity.image} 
                alt={currentCity.name}
                className="w-full h-80 object-cover rounded-2xl shadow-xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-2xl"></div>
              <div className="absolute bottom-6 left-6 text-white">
                <h3 className="text-3xl font-bold rtl-font">{currentCity.name}</h3>
                <p className="text-lg opacity-90">متاح منذ {currentCity.launchDate}</p>
              </div>
            </div>
            
            {/* City Info */}
            <div>
              <h3 className="text-3xl font-bold text-accent mb-6 rtl-font">
                {t('cities.discover')} {currentCity.name}
              </h3>
              
              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="bg-primary/5 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-primary mb-1">{currentCity.restaurants}</div>
                  <div className="text-sm text-gray-600 rtl-font">{t('hero.restaurants_count')}</div>
                </div>
                <div className="bg-orange-500/5 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-primary mb-1">{currentCity.activeUsers}</div>
                  <div className="text-sm text-gray-600 rtl-font">{t('cities.users')}</div>
                </div>
                <div className="bg-blue-500/5 rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-primary mb-1">{currentCity.deliveryTime}</div>
                  <div className="text-sm text-gray-600 rtl-font">{t('cities.delivery')}</div>
                </div>
                <div className="bg-purple-500/5 rounded-xl p-4 text-center">
                  <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse">
                    <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    <div className="text-2xl font-bold text-primary">{currentCity.rating}</div>
                  </div>
                  <div className="text-sm text-gray-600 rtl-font">{t('cities.rating')}</div>
                </div>
              </div>
              
              {/* Districts */}
              <div className="mb-6">
                <h4 className="text-lg font-bold text-accent mb-3 rtl-font">{t('cities.available_areas')}</h4>
                <div className="flex flex-wrap gap-2">
                  {currentCity.districts.map((district, index) => (
                    <span key={index} className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm rtl-font">
                      {district}
                    </span>
                  ))}
                </div>
              </div>
              
              {/* Specialties */}
              <div className="mb-8">
                <h4 className="text-lg font-bold text-accent mb-3 rtl-font">{t('cities.local_specialties')}</h4>
                <div className="flex flex-wrap gap-2">
                  {currentCity.specialties.map((specialty, index) => (
                    <span key={index} className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium rtl-font">
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>
              
              <button 
                onClick={() => {
                  const restaurantsSection = document.getElementById('restaurants');
                  if (restaurantsSection) {
                    restaurantsSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="bg-gradient-to-r from-primary to-primary/90 text-white px-8 py-3 rounded-2xl font-bold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center space-x-2 rtl:space-x-reverse"
              >
                <span>{t('cities.order_from')} {currentCity.name}</span>
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Coming Soon Cities */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-accent mb-8 rtl-font">{t('cities.coming_soon')}</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {comingSoonCities.map((city, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 text-center border-2 border-dashed border-gray-300 hover:border-primary/50 transition-all duration-300">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="w-8 h-8 text-gray-400" />
                </div>
                <h4 className="text-lg font-bold text-gray-600 mb-2 rtl-font">{city.name}</h4>
                <p className="text-sm text-gray-500 rtl-font">{city.launchDate}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-primary via-primary/95 to-primary/90 rounded-3xl p-8 text-white text-center">
          <h3 className="text-2xl font-bold mb-4 rtl-font">{t('cities.city_not_found')}</h3>
          <p className="text-lg mb-6 opacity-90 rtl-font">{t('cities.request_expansion')}</p>
          <button 
            onClick={() => alert('سيتم فتح نموذج طلب التوسع لمدينتك...')}
            className="bg-white text-primary px-8 py-3 rounded-2xl font-bold hover:bg-gray-100 hover:scale-105 transition-all duration-300"
          >
            {t('cities.request_expansion_button')}
          </button>
        </div>
      </div>
    </section>
  );
};

export default CitiesSection;