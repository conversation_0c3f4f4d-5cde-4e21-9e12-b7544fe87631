import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/order_item_widget.dart';

class OrderHistoryScreen extends StatefulWidget {
  const OrderHistoryScreen({super.key});

  @override
  State<OrderHistoryScreen> createState() => _OrderHistoryScreenState();
}

class _OrderHistoryScreenState extends State<OrderHistoryScreen> {
  String searchQuery = '';
  
  final List<OrderItem> orders = [
    OrderItem(
      id: '1',
      menuName: 'Less Sugar',
      date: 'Apr 22, 2023, 8:00 AM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$14.81',
      status: OrderStatus.completed,
      cardNumber: '4255 0017 7617 8545',
      image: 'assets/images/less_sugar.png',
    ),
    OrderItem(
      id: '2',
      menuName: 'Paket Hemat',
      date: 'Apr 15, 2023, 2:21 PM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$11.70',
      status: OrderStatus.completed,
      cardNumber: '5644 2424 6947 5432',
      image: 'assets/images/paket_hemat.png',
    ),
    OrderItem(
      id: '3',
      menuName: 'Chocolate',
      date: 'Apr 22, 2023, 10:11 AM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$11.70',
      status: OrderStatus.cancelled,
      cardNumber: '6542 0786 4431 6636',
      image: 'assets/images/chocolate.png',
    ),
    OrderItem(
      id: '4',
      menuName: 'Coca cola',
      date: 'Jun 15, 2023, 2:00 PM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$6.48',
      status: OrderStatus.delivering,
      cardNumber: '5322 2245 6775 6718',
      image: 'assets/images/coca_cola.png',
    ),
    OrderItem(
      id: '5',
      menuName: 'Chicken',
      date: 'Wednesday, 5:55 PM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$17.84',
      status: OrderStatus.completed,
      cardNumber: '6675 0594 9943 1007',
      image: 'assets/images/chicken.png',
    ),
    OrderItem(
      id: '6',
      menuName: 'Ice Cream',
      date: 'Apr 19, 2023, 2:21 PM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$5.22',
      status: OrderStatus.delivering,
      cardNumber: '6551 8754 3322 2335',
      image: 'assets/images/ice_cream.png',
    ),
    OrderItem(
      id: '7',
      menuName: 'Boba',
      date: 'Apr 16, 2023, 2:21 PM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$8.99',
      status: OrderStatus.completed,
      cardNumber: '0669 8007 6099 1101',
      image: 'assets/images/boba.png',
    ),
    OrderItem(
      id: '8',
      menuName: 'Beef',
      date: 'May 20, 2023, 11:00 AM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$14.81',
      status: OrderStatus.cancelled,
      cardNumber: '5663 3322 7836 9365',
      image: 'assets/images/beef.png',
    ),
    OrderItem(
      id: '9',
      menuName: 'Fried Fries',
      date: 'Aug 12, 2023, 10:15 AM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$14.81',
      status: OrderStatus.delivering,
      cardNumber: '9536 5563 5923 7732',
      image: 'assets/images/fried_fries.png',
    ),
    OrderItem(
      id: '10',
      menuName: 'No Sugar',
      date: 'Apr 16, 2023, 2:21 PM',
      address: 'Elm Street, 21 Yogyakarta\n2.97 Km',
      total: '\$6.48',
      status: OrderStatus.delivering,
      cardNumber: '6888 5433 4431 1199',
      image: 'assets/images/no_sugar.png',
    ),
  ];

  List<OrderItem> get filteredOrders {
    if (searchQuery.isEmpty) return orders;
    return orders.where((order) =>
        order.menuName.toLowerCase().contains(searchQuery.toLowerCase()) ||
        order.address.toLowerCase().contains(searchQuery.toLowerCase()) ||
        order.cardNumber.contains(searchQuery)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.shadow,
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // Table Header
                          _buildTableHeader(),
                          
                          // Orders List
                          Expanded(
                            child: ListView.builder(
                              itemCount: filteredOrders.length,
                              itemBuilder: (context, index) {
                                return OrderItemWidget(
                                  order: filteredOrders[index],
                                  onTap: () => _showOrderDetails(filteredOrders[index]),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Text(
                'Order history',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 8),
              const Text('😊', style: TextStyle(fontSize: 20)),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Notifications
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.notifications_outlined,
                    color: AppColors.textSecondary,
                    size: 20,
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // User Profile
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primary,
                child: Text(
                  'JS',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Jhon Smith',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          Checkbox(
            value: false,
            onChanged: (value) {},
            activeColor: AppColors.primary,
          ),
          
          const SizedBox(width: 16),
          
          // Headers
          Expanded(
            flex: 2,
            child: Text(
              'Menu',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 2,
            child: Text(
              'Date',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 2,
            child: Text(
              'Address',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 1,
            child: Text(
              'Total',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 1,
            child: Text(
              'Status',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 2,
            child: Text(
              'Card Number',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          
          Expanded(
            flex: 1,
            child: Text(
              'Action',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showOrderDetails(OrderItem order) {
    // Show order details dialog or navigate to details screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Order Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Menu: ${order.menuName}'),
            Text('Date: ${order.date}'),
            Text('Total: ${order.total}'),
            Text('Status: ${order.status.name}'),
            Text('Card: ${order.cardNumber}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}

enum OrderStatus {
  completed,
  cancelled,
  delivering,
}

class OrderItem {
  final String id;
  final String menuName;
  final String date;
  final String address;
  final String total;
  final OrderStatus status;
  final String cardNumber;
  final String image;

  OrderItem({
    required this.id,
    required this.menuName,
    required this.date,
    required this.address,
    required this.total,
    required this.status,
    required this.cardNumber,
    required this.image,
  });
}
