import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardOverview from '../components/DashboardOverview';
import RoleManagement from '../components/RoleManagement';
import UserManagement from '../components/UserManagement';
import OrdersManagement from '../components/OrdersManagement';
import RestaurantsManagement from '../components/RestaurantsManagement';
import DriversManagement from '../components/DriversManagement';
import AnalyticsReports from '../components/AnalyticsReports';
import SupportManagement from '../components/SupportManagement';
import SystemSettings from '../components/SystemSettings';
import RealTimeMap from '../components/RealTimeMap';
import PromotionsManagement from '../components/PromotionsManagement';
import FinancialReports from '../components/FinancialReports';
import ContentManagement from '../components/ContentManagement';
import AuditLog from '../components/AuditLog';
import PaymentManagement from '../components/PaymentManagement';
import NotificationCenter from '../components/NotificationCenter';
import DeliveryManagement from '../components/DeliveryManagement';
import QualityControl from '../components/QualityControl';
import InventoryManagement from '../components/InventoryManagement';
import SecurityManagement from '../components/SecurityManagement';
import { 
  BarChart3, 
  Users, 
  ShoppingBag, 
  Car, 
  MessageCircle, 
  Settings, 
  LogOut, 
  Bell, 
  Search,
  TrendingUp,
  DollarSign,
  Clock,
  Star,
  Shield,
  UserPlus,
  Eye,
  MoreVertical,
  Filter,
  Download,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Moon,
  Sun,
  Globe,
  ChevronDown,
  ChevronUp,
  MapPin,
  Route,
  Target,
  Award,
  TrendingDown,
  Navigation,
  Zap,
  Map,
  Gift,
  FileText,
  Megaphone,
  Image,
  Video,
  Percent,
  Calendar,
  Tag,
  Copy,
  Share2,
  Upload,
  Save,
  X,
  Smartphone,
  Monitor,
  Building,
  Wallet,
  CreditCard,
  Calculator,
  PieChart,
  LineChart,
  BarChart,
  Activity,
  Bell,
  Truck,
  Package,
  Shield,
  Lock,
  Key,
  Wallet,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  Award,
  Zap
} from 'lucide-react';

const DashboardPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [notifications, setNotifications] = useState(3);
  const [driversDropdownOpen, setDriversDropdownOpen] = useState(false);

  // Mock user data
  const currentUser = {
    name: 'مدير النظام',
    email: '<EMAIL>',
    role: 'Super Admin',
    avatar: 'https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop'
  };

  const handleLogout = () => {
    localStorage.removeItem('admin-token');
    localStorage.removeItem('admin-user');
    navigate('/admin/login');
  };

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    if (!isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // Stats data
  const stats = [
    {
      title: 'إجمالي الطلبات',
      value: '12,847',
      change: '+12.5%',
      icon: <ShoppingBag className="w-8 h-8" />,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'المطاعم النشطة',
      value: '486',
      change: '****%',
      icon: <Users className="w-8 h-8" />,
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'السائقين المتاحين',
      value: '234',
      change: '+15.3%',
      icon: <Car className="w-8 h-8" />,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'الإيرادات اليومية',
      value: '45,280 درهم',
      change: '+23.1%',
      icon: <DollarSign className="w-8 h-8" />,
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  // Recent activities
  const recentActivities = [
    {
      id: 1,
      type: 'order',
      message: 'طلب جديد من مطعم الأصالة',
      time: 'منذ 5 دقائق',
      status: 'success'
    },
    {
      id: 2,
      type: 'driver',
      message: 'سائق جديد انضم للمنصة',
      time: 'منذ 15 دقيقة',
      status: 'info'
    },
    {
      id: 3,
      type: 'restaurant',
      message: 'مطعم جديد في انتظار الموافقة',
      time: 'منذ 30 دقيقة',
      status: 'warning'
    },
    {
      id: 4,
      type: 'support',
      message: 'شكوى جديدة من عميل',
      time: 'منذ ساعة',
      status: 'error'
    }
  ];

  // Navigation items
  const navigationItems = [
    { id: 'overview', label: 'نظرة عامة', icon: <BarChart3 className="w-5 h-5" /> },
    { id: 'realtime-map', label: 'الخريطة التفاعلية', icon: <Map className="w-5 h-5" /> },
    { id: 'analytics', label: 'التحليلات والتقارير', icon: <TrendingUp className="w-5 h-5" /> },
    { id: 'financial-reports', label: 'التقارير المالية', icon: <Calculator className="w-5 h-5" /> },
    { id: 'payment-management', label: 'إدارة المدفوعات', icon: <Wallet className="w-5 h-5" /> },
    { id: 'users', label: 'إدارة المستخدمين', icon: <Users className="w-5 h-5" /> },
    { id: 'roles', label: 'إدارة الأدوار', icon: <Shield className="w-5 h-5" /> },
    { id: 'security', label: 'الأمان والصلاحيات', icon: <Lock className="w-5 h-5" /> },
    { id: 'orders', label: 'الطلبات', icon: <ShoppingBag className="w-5 h-5" /> },
    { id: 'restaurants', label: 'المطاعم', icon: <Building className="w-5 h-5" /> },
    { id: 'drivers', label: 'السائقين', icon: <Car className="w-5 h-5" /> },
    { id: 'delivery', label: 'إدارة التوصيل', icon: <Truck className="w-5 h-5" /> },
    { id: 'inventory', label: 'المخزون والموردين', icon: <Package className="w-5 h-5" /> },
    { id: 'promotions', label: 'العروض والخصومات', icon: <Gift className="w-5 h-5" /> },
    { id: 'content', label: 'إدارة المحتوى', icon: <FileText className="w-5 h-5" /> },
    { id: 'notifications', label: 'مركز الإشعارات', icon: <Bell className="w-5 h-5" /> },
    { id: 'quality', label: 'مراقبة الجودة', icon: <Award className="w-5 h-5" /> },
    { id: 'support', label: 'الدعم الفني', icon: <MessageCircle className="w-5 h-5" /> },
    { id: 'audit-log', label: 'سجل العمليات', icon: <Activity className="w-5 h-5" /> },
    { id: 'settings', label: 'الإعدادات', icon: <Settings className="w-5 h-5" /> }
  ];

  const handleDriversClick = () => {
    setActiveTab('drivers');
    setDriversDropdownOpen(!driversDropdownOpen);
  };
  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      {/* Sidebar */}
      <div className={`fixed right-0 top-0 h-full w-64 transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      } border-l shadow-lg z-50`}>
        
        {/* Logo */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center">
              <span className="text-white font-bold text-2xl">و</span>
            </div>
            <div>
              <h1 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-accent'}`}>
                وصلتي
              </h1>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                لوحة التحكم
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="p-4">
          <div className="space-y-2">
            {navigationItems.map((item) => (
              <div key={item.id}>
                <button
                  onClick={() => item.id === 'drivers' ? handleDriversClick() : setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 rounded-xl transition-all duration-300 ${
                    activeTab === item.id
                      ? 'bg-primary text-white shadow-lg'
                      : isDarkMode
                        ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {item.icon}
                  <span className="font-medium rtl-font">{item.label}</span>
                  {item.id === 'drivers' && (
                    <div className="mr-auto">
                      {driversDropdownOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                    </div>
                  )}
                </button>
                
                {/* Drivers Dropdown Menu - يظهر مباشرة أسفل زر السائقين */}
                {item.id === 'drivers' && driversDropdownOpen && (
                  <div className="mt-2 mr-6 overflow-hidden transition-all duration-300 max-h-[500px]">
                    <div className={`rounded-2xl border-2 shadow-lg ${
                      isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-primary/10'
                    }`}>
                      <div className="p-4 space-y-3">
                        {/* نظرة عامة على الأداء */}
                        <button 
                          onClick={() => alert('سيتم فتح نظرة عامة على الأداء...')}
                          className={`w-full flex items-center space-x-3 rtl:space-x-reverse p-3 rounded-xl transition-all duration-300 transform hover:scale-105 ${
                            isDarkMode 
                              ? 'hover:bg-gray-600 text-gray-300 hover:text-white border border-gray-600' 
                              : 'bg-blue-50/50 text-gray-700 hover:text-primary border border-blue-100'
                          } hover:shadow-xl`}
                        >
                          <div className="w-9 h-9 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center shadow-inner">
                            <BarChart3 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div className="flex-1 text-right">
                            <h4 className="font-bold text-base rtl-font">نظرة عامة على الأداء</h4>
                            <p className="text-xs opacity-80 rtl-font mt-1">عدد الطلبات • الأرباح • التقييم • المسافة</p>
                          </div>
                        </button>

                        {/* الخريطة والطلبات */}
                        <button 
                          onClick={() => alert('سيتم فتح الخريطة والطلبات...')}
                          className={`w-full flex items-center space-x-3 rtl:space-x-reverse p-3 rounded-xl transition-all duration-300 transform hover:scale-105 ${
                            isDarkMode 
                              ? 'hover:bg-gray-600 text-gray-300 hover:text-white border border-gray-600' 
                              : 'bg-green-50/50 text-gray-700 hover:text-primary border border-green-100'
                          } hover:shadow-xl`}
                        >
                          <div className="w-9 h-9 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center shadow-inner">
                            <MapPin className="w-5 h-5 text-green-600 dark:text-green-400" />
                          </div>
                          <div className="flex-1 text-right">
                            <h4 className="font-bold text-base rtl-font">الخريطة والطلبات</h4>
                            <p className="text-xs opacity-80 rtl-font mt-1">الطلبات القريبة • المسارات • قبول/رفض</p>
                          </div>
                        </button>

                        {/* الأرباح والمدفوعات */}
                        <button 
                          onClick={() => alert('سيتم فتح الأرباح والمدفوعات...')}
                          className={`w-full flex items-center space-x-3 rtl:space-x-reverse p-3 rounded-xl transition-all duration-300 transform hover:scale-105 ${
                            isDarkMode 
                              ? 'hover:bg-gray-600 text-gray-300 hover:text-white border border-gray-600' 
                              : 'bg-yellow-50/50 text-gray-700 hover:text-primary border border-yellow-100'
                          } hover:shadow-xl`}
                        >
                          <div className="w-9 h-9 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl flex items-center justify-center shadow-inner">
                            <DollarSign className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                          </div>
                          <div className="flex-1 text-right">
                            <h4 className="font-bold text-base rtl-font">الأرباح والمدفوعات</h4>
                            <p className="text-xs opacity-80 rtl-font mt-1">الأرباح اليومية • المدفوعات • المكافآت</p>
                          </div>
                        </button>

                        {/* تحسين الأداء */}
                        <button 
                          onClick={() => alert('سيتم فتح تحسين الأداء...')}
                          className={`w-full flex items-center space-x-3 rtl:space-x-reverse p-3 rounded-xl transition-all duration-300 transform hover:scale-105 ${
                            isDarkMode 
                              ? 'hover:bg-gray-600 text-gray-300 hover:text-white border border-gray-600' 
                              : 'bg-purple-50/50 text-gray-700 hover:text-primary border border-purple-100'
                          } hover:shadow-xl`}
                        >
                          <div className="w-9 h-9 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center shadow-inner">
                            <TrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                          </div>
                          <div className="flex-1 text-right">
                            <h4 className="font-bold text-base rtl-font">تحسين الأداء</h4>
                            <p className="text-xs opacity-80 rtl-font mt-1">نصائح الأرباح • أوقات الذروة • التحسينات</p>
                          </div>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </nav>

        {/* User Profile */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
            <img 
              src={currentUser.avatar} 
              alt={currentUser.name}
              className="w-10 h-10 rounded-full object-cover"
            />
            <div className="flex-1">
              <h4 className={`font-semibold text-sm ${isDarkMode ? 'text-white' : 'text-gray-800'} rtl-font`}>
                {currentUser.name}
              </h4>
              <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {currentUser.role}
              </p>
            </div>
          </div>
          
          <button
            onClick={handleLogout}
            className={`w-full flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-lg transition-colors ${
              isDarkMode 
                ? 'text-gray-300 hover:bg-gray-700 hover:text-white' 
                : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <LogOut className="w-4 h-4" />
            <span className="text-sm font-medium">تسجيل الخروج</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="mr-64">
        {/* Header */}
        <header className={`sticky top-0 z-40 transition-colors duration-300 ${
          isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } border-b shadow-sm`}>
          <div className="flex items-center justify-between px-6 py-4">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className={`absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <input
                  type="text"
                  placeholder="البحث..."
                  className={`w-full pr-10 pl-4 py-2 rounded-xl border transition-colors ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                      : 'bg-gray-50 border-gray-200 text-gray-800 placeholder-gray-500'
                  } focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary rtl-font`}
                />
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className={`w-10 h-10 rounded-xl flex items-center justify-center transition-colors ${
                  isDarkMode 
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
              </button>

              {/* Notifications */}
              <button className={`relative w-10 h-10 rounded-xl flex items-center justify-center transition-colors ${
                isDarkMode 
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}>
                <Bell className="w-5 h-5" />
                {notifications > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {notifications}
                  </span>
                )}
              </button>

              {/* Language */}
              <button className={`w-10 h-10 rounded-xl flex items-center justify-center transition-colors ${
                isDarkMode 
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}>
                <Globe className="w-5 h-5" />
              </button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="p-6">
          {activeTab === 'overview' && (
            <DashboardOverview />
          )}
          
          {activeTab === 'analytics' && (
            <AnalyticsReports />
          )}

          {activeTab === 'users' && (
            <UserManagement />
          )}

          {activeTab === 'roles' && (
            <RoleManagement />
          )}

          {activeTab === 'orders' && (
            <OrdersManagement />
          )}

          {activeTab === 'restaurants' && (
            <RestaurantsManagement />
          )}

          {activeTab === 'drivers' && (
            <DriversManagement />
          )}

          {activeTab === 'support' && (
            <SupportManagement />
          )}

          {activeTab === 'settings' && (
            <SystemSettings />
          )}

          {activeTab === 'realtime-map' && (
            <RealTimeMap />
          )}

          {activeTab === 'financial-reports' && (
            <FinancialReports />
          )}

          {activeTab === 'promotions' && (
            <PromotionsManagement />
          )}

          {activeTab === 'content' && (
            <ContentManagement />
          )}

          {activeTab === 'audit-log' && (
            <AuditLog />
          )}

          {activeTab === 'payment-management' && (
            <PaymentManagement />
          )}

          {activeTab === 'notifications' && (
            <NotificationCenter />
          )}

          {activeTab === 'delivery' && (
            <DeliveryManagement />
          )}

          {activeTab === 'quality' && (
            <QualityControl />
          )}

          {activeTab === 'inventory' && (
            <InventoryManagement />
          )}

          {activeTab === 'security' && (
            <SecurityManagement />
          )}

          {/* Other tabs will show placeholder content for now */}
          {!['overview', 'realtime-map', 'analytics', 'financial-reports', 'payment-management', 'users', 'roles', 'security', 'orders', 'restaurants', 'drivers', 'delivery', 'inventory', 'promotions', 'content', 'notifications', 'quality', 'support', 'audit-log', 'settings'].includes(activeTab) && (
            <div className={`rounded-2xl p-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg text-center`}>
              <div className={`w-16 h-16 mx-auto mb-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                {navigationItems.find(item => item.id === activeTab)?.icon}
              </div>
              <h3 className={`text-xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-800'} rtl-font`}>
                {navigationItems.find(item => item.id === activeTab)?.label}
              </h3>
              <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} rtl-font`}>
                سيتم تطوير هذه الصفحة قريباً
              </p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default DashboardPage;