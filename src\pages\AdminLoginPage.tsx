import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Mail, Lock, Moon, Sun, Globe, ArrowRight, Shield, AlertCircle, CheckCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

const AdminLoginPage = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [loginError, setLoginError] = useState('');
  
  const navigate = useNavigate();
  const { language, setLanguage, t, isRTL } = useLanguage();

  useEffect(() => {
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('admin-theme');
    if (savedTheme === 'dark') {
      setIsDarkMode(true);
      document.documentElement.classList.add('dark');
    }
  }, []);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    if (!isDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('admin-theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('admin-theme', 'light');
    }
  };

  const calculatePasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    return strength;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear errors when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    // Calculate password strength
    if (field === 'password') {
      setPasswordStrength(calculatePasswordStrength(value));
    }
    
    // Clear login error
    if (loginError) {
      setLoginError('');
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }
    
    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة';
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsLoading(true);
    setLoginError('');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock authentication - in real app, this would be an API call
      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {
        // Store auth token
        localStorage.setItem('admin-token', 'mock-jwt-token');
        localStorage.setItem('admin-user', JSON.stringify({
          email: formData.email,
          name: 'مدير النظام',
          role: 'admin'
        }));
        
        // Redirect to dashboard
        navigate('/dashboard');
      } else {
        setLoginError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
      }
    } catch (error) {
      setLoginError('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit(e as any);
    }
  };

  const handleGoogleSignIn = () => {
    alert('تسجيل الدخول عبر Google قريباً...');
  };

  const handleForgotPassword = () => {
    alert('سيتم إرسال رابط إعادة تعيين كلمة المرور...');
  };

  const getPasswordStrengthColor = () => {
    if (passwordStrength < 25) return 'bg-red-500';
    if (passwordStrength < 50) return 'bg-orange-500';
    if (passwordStrength < 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getPasswordStrengthText = () => {
    if (passwordStrength < 25) return 'ضعيفة';
    if (passwordStrength < 50) return 'متوسطة';
    if (passwordStrength < 75) return 'جيدة';
    return 'قوية';
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gradient-to-br from-accent via-accent/95 to-accent/90' 
        : 'bg-gradient-to-br from-background via-white to-background'
    }`}>
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className={`absolute top-0 left-0 w-96 h-96 rounded-full blur-3xl ${
          isDarkMode ? 'bg-primary/10' : 'bg-primary/5'
        }`}></div>
        <div className={`absolute bottom-0 right-0 w-72 h-72 rounded-full blur-3xl ${
          isDarkMode ? 'bg-orange-400/10' : 'bg-orange-400/5'
        }`}></div>
      </div>

      {/* Header Controls */}
      <div className="absolute top-6 left-6 right-6 flex justify-between items-center z-10">
        {/* Theme Toggle */}
        <button
          onClick={toggleTheme}
          className={`w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-300 hover:scale-110 ${
            isDarkMode 
              ? 'bg-white/10 text-white hover:bg-white/20' 
              : 'bg-white/80 text-gray-700 hover:bg-white shadow-lg'
          }`}
        >
          {isDarkMode ? <Sun className="w-6 h-6" /> : <Moon className="w-6 h-6" />}
        </button>

        {/* Language Switcher */}
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          {['ar', 'en', 'fr'].map((lang) => (
            <button
              key={lang}
              onClick={() => setLanguage(lang as any)}
              className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                language === lang
                  ? 'bg-primary text-white shadow-lg scale-105'
                  : isDarkMode
                    ? 'bg-white/10 text-white hover:bg-white/20'
                    : 'bg-white/80 text-gray-700 hover:bg-white shadow-md'
              }`}
            >
              {lang.toUpperCase()}
            </button>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className={`w-full max-w-md transition-all duration-300 ${
          isDarkMode 
            ? 'bg-white/10 backdrop-blur-lg border border-white/20' 
            : 'bg-white shadow-2xl'
        } rounded-3xl overflow-hidden`}>
          
          {/* Header */}
          <div className="p-8 text-center">
            {/* Logo */}
            <div className="flex items-center justify-center space-x-3 rtl:space-x-reverse mb-6">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary/80 rounded-3xl flex items-center justify-center shadow-xl">
                  <span className="text-white font-bold text-3xl">و</span>
                </div>
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-orange-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-accent'}`}>
                  وصلتي
                </h1>
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                  لوحة التحكم
                </p>
              </div>
            </div>

            {/* Welcome Message */}
            <div className="mb-8">
              <h2 className={`text-2xl font-bold mb-2 rtl-font ${isDarkMode ? 'text-white' : 'text-accent'}`}>
                مرحباً بعودتك
              </h2>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} rtl-font`}>
                سجل دخولك للوصول إلى لوحة التحكم
              </p>
            </div>
          </div>

          {/* Form */}
          <div className="px-8 pb-8">
            {/* Login Error */}
            {loginError && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-2xl flex items-center space-x-3 rtl:space-x-reverse">
                <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
                <p className="text-red-700 text-sm rtl-font">{loginError}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div>
                <div className="relative">
                  <Mail className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-500'
                  }`} />
                  <input
                    type="email"
                    placeholder="البريد الإلكتروني"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    onKeyPress={handleKeyPress}
                    className={`w-full pr-12 pl-4 py-4 rounded-2xl border-2 transition-all duration-300 rtl-font ${
                      errors.email
                        ? 'border-red-500 focus:border-red-500'
                        : isDarkMode
                          ? 'border-white/20 bg-white/10 text-white placeholder-gray-400 focus:border-primary'
                          : 'border-gray-200 bg-white text-gray-800 placeholder-gray-500 focus:border-primary'
                    } focus:outline-none focus:ring-4 focus:ring-primary/20`}
                  />
                </div>
                {errors.email && (
                  <p className="mt-2 text-red-500 text-sm flex items-center space-x-2 rtl:space-x-reverse">
                    <AlertCircle className="w-4 h-4" />
                    <span>{errors.email}</span>
                  </p>
                )}
              </div>

              {/* Password Field */}
              <div>
                <div className="relative">
                  <Lock className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-500'
                  }`} />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="كلمة المرور"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    onKeyPress={handleKeyPress}
                    className={`w-full pr-12 pl-12 py-4 rounded-2xl border-2 transition-all duration-300 rtl-font ${
                      errors.password
                        ? 'border-red-500 focus:border-red-500'
                        : isDarkMode
                          ? 'border-white/20 bg-white/10 text-white placeholder-gray-400 focus:border-primary'
                          : 'border-gray-200 bg-white text-gray-800 placeholder-gray-500 focus:border-primary'
                    } focus:outline-none focus:ring-4 focus:ring-primary/20`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className={`absolute left-4 top-1/2 transform -translate-y-1/2 ${
                      isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'
                    } transition-colors`}
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                
                {/* Password Strength */}
                {formData.password && (
                  <div className="mt-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'} rtl-font`}>
                        قوة كلمة المرور:
                      </span>
                      <span className={`text-sm font-medium ${
                        passwordStrength >= 75 ? 'text-green-500' :
                        passwordStrength >= 50 ? 'text-yellow-500' :
                        passwordStrength >= 25 ? 'text-orange-500' : 'text-red-500'
                      }`}>
                        {getPasswordStrengthText()}
                      </span>
                    </div>
                    <div className={`w-full h-2 rounded-full ${isDarkMode ? 'bg-white/20' : 'bg-gray-200'}`}>
                      <div 
                        className={`h-full rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}
                        style={{ width: `${passwordStrength}%` }}
                      ></div>
                    </div>
                  </div>
                )}
                
                {errors.password && (
                  <p className="mt-2 text-red-500 text-sm flex items-center space-x-2 rtl:space-x-reverse">
                    <AlertCircle className="w-4 h-4" />
                    <span>{errors.password}</span>
                  </p>
                )}
              </div>

              {/* Forgot Password */}
              <div className="text-left rtl:text-right">
                <button
                  type="button"
                  onClick={handleForgotPassword}
                  className={`text-sm font-medium transition-colors ${
                    isDarkMode 
                      ? 'text-primary hover:text-primary/80' 
                      : 'text-primary hover:text-primary/80'
                  }`}
                >
                  نسيت كلمة المرور؟
                </button>
              </div>

              {/* Login Button */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-primary to-primary/90 text-white py-4 rounded-2xl font-bold text-lg hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isLoading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>جاري تسجيل الدخول...</span>
                  </>
                ) : (
                  <>
                    <span>تسجيل الدخول</span>
                    <ArrowRight className="w-5 h-5" />
                  </>
                )}
              </button>

              {/* Divider */}
              <div className="relative my-6">
                <div className={`absolute inset-0 flex items-center`}>
                  <div className={`w-full border-t ${isDarkMode ? 'border-white/20' : 'border-gray-200'}`} />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className={`px-2 ${isDarkMode ? 'bg-white/10 text-gray-300' : 'bg-white text-gray-500'} rtl-font`}>
                    أو
                  </span>
                </div>
              </div>

              {/* Google Sign In */}
              <button
                type="button"
                onClick={handleGoogleSignIn}
                className={`w-full py-4 rounded-2xl font-semibold transition-all duration-300 hover:scale-105 flex items-center justify-center space-x-3 rtl:space-x-reverse ${
                  isDarkMode
                    ? 'bg-white/10 text-white border-2 border-white/20 hover:bg-white/20'
                    : 'bg-gray-50 text-gray-700 border-2 border-gray-200 hover:bg-gray-100'
                }`}
              >
                <Globe className="w-5 h-5" />
                <span>تسجيل الدخول عبر Google</span>
              </button>
            </form>

            {/* Demo Credentials */}
            <div className={`mt-8 p-4 rounded-2xl ${
              isDarkMode ? 'bg-white/5 border border-white/10' : 'bg-gray-50 border border-gray-200'
            }`}>
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                <Shield className="w-4 h-4 text-primary" />
                <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-700'} rtl-font`}>
                  بيانات تجريبية:
                </span>
              </div>
              <div className={`text-xs space-y-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                <p>البريد: <EMAIL></p>
                <p>كلمة المرور: admin123</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} rtl-font`}>
          &copy; 2024 وصلتي. جميع الحقوق محفوظة.
        </p>
      </div>
    </div>
  );
};

export default AdminLoginPage;