import React, { useState } from 'react';
import { 
  MessageCircle, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Reply, 
  Archive, 
  Clock, 
  User, 
  Mail, 
  Phone,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Plus,
  RefreshCw,
  Star,
  Tag,
  Calendar,
  Send,
  Paperclip,
  MoreVertical
} from 'lucide-react';

interface SupportTicket {
  id: string;
  ticketNumber: string;
  subject: string;
  message: string;
  customer: {
    name: string;
    email: string;
    phone: string;
    avatar: string;
    type: 'customer' | 'restaurant' | 'driver';
  };
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'order' | 'payment' | 'delivery' | 'account' | 'technical' | 'other';
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  responses: {
    id: string;
    message: string;
    sender: 'customer' | 'support';
    senderName: string;
    timestamp: string;
    attachments?: string[];
  }[];
  rating?: number;
  tags: string[];
}

const SupportManagement = () => {
  const [tickets, setTickets] = useState<SupportTicket[]>([
    {
      id: '1',
      ticketNumber: 'SUP-2024-001',
      subject: 'مشكلة في الدفع',
      message: 'لا أستطيع إكمال عملية الدفع باستخدام بطاقتي الائتمانية',
      customer: {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '+212 6XX-XXXXXX',
        avatar: 'https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
        type: 'customer'
      },
      status: 'open',
      priority: 'high',
      category: 'payment',
      createdAt: '2024-01-20 14:30',
      updatedAt: '2024-01-20 14:30',
      responses: [
        {
          id: '1',
          message: 'لا أستطيع إكمال عملية الدفع باستخدام بطاقتي الائتمانية. تظهر رسالة خطأ في كل مرة أحاول فيها الدفع.',
          sender: 'customer',
          senderName: 'أحمد محمد',
          timestamp: '2024-01-20 14:30'
        }
      ],
      tags: ['دفع', 'بطاقة ائتمان']
    },
    {
      id: '2',
      ticketNumber: 'SUP-2024-002',
      subject: 'تأخير في التوصيل',
      message: 'طلبي متأخر أكثر من ساعة عن الوقت المحدد',
      customer: {
        name: 'فاطمة الزهراء',
        email: '<EMAIL>',
        phone: '+212 6XX-XXXXXX',
        avatar: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
        type: 'customer'
      },
      status: 'in_progress',
      priority: 'medium',
      category: 'delivery',
      assignedTo: 'سارة الدعم',
      createdAt: '2024-01-20 12:15',
      updatedAt: '2024-01-20 13:45',
      responses: [
        {
          id: '1',
          message: 'طلبي رقم ORD-2024-123 متأخر أكثر من ساعة عن الوقت المحدد. أين السائق؟',
          sender: 'customer',
          senderName: 'فاطمة الزهراء',
          timestamp: '2024-01-20 12:15'
        },
        {
          id: '2',
          message: 'نعتذر عن التأخير. تم التواصل مع السائق وسيصل طلبك خلال 15 دقيقة. سنقوم بتعويضك بخصم على طلبك القادم.',
          sender: 'support',
          senderName: 'سارة الدعم',
          timestamp: '2024-01-20 13:45'
        }
      ],
      tags: ['توصيل', 'تأخير'],
      rating: 4
    },
    {
      id: '3',
      ticketNumber: 'SUP-2024-003',
      subject: 'مشكلة في تسجيل الدخول',
      message: 'لا أستطيع تسجيل الدخول إلى حسابي',
      customer: {
        name: 'مطعم الأصالة',
        email: '<EMAIL>',
        phone: '+212 5XX-XXXXXX',
        avatar: 'https://images.pexels.com/photos/1581384/pexels-photo-1581384.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
        type: 'restaurant'
      },
      status: 'resolved',
      priority: 'low',
      category: 'account',
      assignedTo: 'محمد الدعم',
      createdAt: '2024-01-19 16:20',
      updatedAt: '2024-01-20 09:30',
      responses: [
        {
          id: '1',
          message: 'لا أستطيع تسجيل الدخول إلى لوحة تحكم المطعم. تظهر رسالة "كلمة مرور خاطئة"',
          sender: 'customer',
          senderName: 'مطعم الأصالة',
          timestamp: '2024-01-19 16:20'
        },
        {
          id: '2',
          message: 'تم إعادة تعيين كلمة المرور وإرسال رابط جديد إلى بريدكم الإلكتروني. يرجى التحقق من صندوق الوارد.',
          sender: 'support',
          senderName: 'محمد الدعم',
          timestamp: '2024-01-20 09:30'
        }
      ],
      tags: ['حساب', 'كلمة مرور'],
      rating: 5
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);

  const statuses = [
    { id: 'all', name: 'جميع الحالات', color: 'text-gray-600' },
    { id: 'open', name: 'مفتوح', color: 'text-blue-600' },
    { id: 'in_progress', name: 'قيد المعالجة', color: 'text-yellow-600' },
    { id: 'resolved', name: 'تم الحل', color: 'text-green-600' },
    { id: 'closed', name: 'مغلق', color: 'text-gray-600' }
  ];

  const priorities = [
    { id: 'all', name: 'جميع الأولويات' },
    { id: 'low', name: 'منخفضة' },
    { id: 'medium', name: 'متوسطة' },
    { id: 'high', name: 'عالية' },
    { id: 'urgent', name: 'عاجلة' }
  ];

  const categories = [
    { id: 'all', name: 'جميع الفئات' },
    { id: 'order', name: 'الطلبات' },
    { id: 'payment', name: 'الدفع' },
    { id: 'delivery', name: 'التوصيل' },
    { id: 'account', name: 'الحساب' },
    { id: 'technical', name: 'تقني' },
    { id: 'other', name: 'أخرى' }
  ];

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.ticketNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.customer.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || ticket.status === selectedStatus;
    const matchesPriority = selectedPriority === 'all' || ticket.priority === selectedPriority;
    const matchesCategory = selectedCategory === 'all' || ticket.category === selectedCategory;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case 'medium': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    const statusObj = statuses.find(s => s.id === status);
    return statusObj ? statusObj.name : status;
  };

  const getPriorityText = (priority: string) => {
    const priorityObj = priorities.find(p => p.id === priority);
    return priorityObj ? priorityObj.name : priority;
  };

  const getCategoryText = (category: string) => {
    const categoryObj = categories.find(c => c.id === category);
    return categoryObj ? categoryObj.name : category;
  };

  const getCustomerTypeIcon = (type: string) => {
    switch (type) {
      case 'customer': return <User className="w-4 h-4" />;
      case 'restaurant': return <MessageCircle className="w-4 h-4" />;
      case 'driver': return <MessageCircle className="w-4 h-4" />;
      default: return <User className="w-4 h-4" />;
    }
  };

  const getCustomerTypeText = (type: string) => {
    switch (type) {
      case 'customer': return 'عميل';
      case 'restaurant': return 'مطعم';
      case 'driver': return 'سائق';
      default: return type;
    }
  };

  const handleViewTicket = (ticket: SupportTicket) => {
    setSelectedTicket(ticket);
    setIsTicketModalOpen(true);
  };

  const handleUpdateTicketStatus = (ticketId: string, newStatus: string) => {
    setTickets(prev => prev.map(ticket => 
      ticket.id === ticketId ? { ...ticket, status: newStatus as any, updatedAt: new Date().toISOString() } : ticket
    ));
  };

  const handleAssignTicket = (ticketId: string, assignee: string) => {
    setTickets(prev => prev.map(ticket => 
      ticket.id === ticketId ? { ...ticket, assignedTo: assignee, updatedAt: new Date().toISOString() } : ticket
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white rtl-font">
            إدارة الدعم الفني
          </h1>
          <p className="text-gray-600 dark:text-gray-400 rtl-font">
            إدارة ومتابعة جميع تذاكر الدعم والاستفسارات
          </p>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
            <Download className="w-4 h-4" />
            <span>تصدير</span>
          </button>
          <button className="bg-primary text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary/90 transition-colors flex items-center space-x-2 rtl:space-x-reverse">
            <Plus className="w-5 h-5" />
            <span>تذكرة جديدة</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">إجمالي التذاكر</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">{tickets.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
              <MessageCircle className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">مفتوحة</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {tickets.filter(t => t.status === 'open').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">قيد المعالجة</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {tickets.filter(t => t.status === 'in_progress').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 rtl-font">تم الحل</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {tickets.filter(t => t.status === 'resolved').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="البحث عن تذكرة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
            />
          </div>

          {/* Status Filter */}
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {statuses.map(status => (
              <option key={status.id} value={status.id}>{status.name}</option>
            ))}
          </select>

          {/* Priority Filter */}
          <select
            value={selectedPriority}
            onChange={(e) => setSelectedPriority(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {priorities.map(priority => (
              <option key={priority.id} value={priority.id}>{priority.name}</option>
            ))}
          </select>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
          >
            {categories.map(category => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>

          {/* Refresh Button */}
          <button className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse">
            <RefreshCw className="w-4 h-4" />
            <span>تحديث</span>
          </button>
        </div>
      </div>

      {/* Tickets Table */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  رقم التذكرة
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  العميل
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  الموضوع
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  الفئة
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  الأولوية
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  الحالة
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  المسؤول
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300 rtl-font">
                  التاريخ
                </th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700 dark:text-gray-300">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
              {filteredTickets.map((ticket) => (
                <tr key={ticket.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="px-6 py-4">
                    <div className="font-semibold text-gray-800 dark:text-white">
                      {ticket.ticketNumber}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <img 
                        src={ticket.customer.avatar} 
                        alt={ticket.customer.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <div>
                        <h4 className="font-semibold text-gray-800 dark:text-white rtl-font">
                          {ticket.customer.name}
                        </h4>
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          {getCustomerTypeIcon(ticket.customer.type)}
                          <span className="text-xs text-gray-600 dark:text-gray-400 rtl-font">
                            {getCustomerTypeText(ticket.customer.type)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="font-medium text-gray-800 dark:text-white rtl-font">
                      {ticket.subject}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 rtl-font truncate max-w-xs">
                      {ticket.message}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                      {getCategoryText(ticket.category)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                      {getPriorityText(ticket.priority)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                      {getStatusText(ticket.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400 rtl-font">
                      {ticket.assignedTo || 'غير مخصص'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {ticket.createdAt}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button 
                        onClick={() => handleViewTicket(ticket)}
                        className="w-8 h-8 flex items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors"
                        title="عرض التفاصيل"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      {ticket.status !== 'closed' && (
                        <select
                          value={ticket.status}
                          onChange={(e) => handleUpdateTicketStatus(ticket.id, e.target.value)}
                          className="text-xs border border-gray-200 dark:border-gray-600 rounded-lg px-2 py-1 bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
                        >
                          {statuses.filter(s => s.id !== 'all').map(status => (
                            <option key={status.id} value={status.id}>{status.name}</option>
                          ))}
                        </select>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredTickets.length === 0 && (
          <div className="text-center py-12">
            <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2 rtl-font">
              لا توجد تذاكر
            </h3>
            <p className="text-gray-500 dark:text-gray-500 rtl-font">
              لم يتم العثور على تذاكر تطابق معايير البحث
            </p>
          </div>
        )}
      </div>

      {/* Ticket Details Modal */}
      {isTicketModalOpen && selectedTicket && (
        <TicketDetailsModal 
          ticket={selectedTicket}
          onClose={() => {
            setIsTicketModalOpen(false);
            setSelectedTicket(null);
          }}
          onUpdateStatus={(status) => handleUpdateTicketStatus(selectedTicket.id, status)}
        />
      )}
    </div>
  );
};

// Ticket Details Modal Component
const TicketDetailsModal = ({ ticket, onClose, onUpdateStatus }: {
  ticket: SupportTicket;
  onClose: () => void;
  onUpdateStatus: (status: string) => void;
}) => {
  const [replyMessage, setReplyMessage] = useState('');

  const handleSendReply = () => {
    if (replyMessage.trim()) {
      // Add reply logic here
      console.log('Sending reply:', replyMessage);
      setReplyMessage('');
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />
      
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-4xl bg-white dark:bg-gray-800 rounded-3xl shadow-2xl">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white rtl-font">
                {ticket.subject}
              </h3>
              <button
                onClick={onClose}
                className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <XCircle className="w-5 h-5 text-gray-500" />
              </button>
            </div>
            <div className="flex items-center space-x-4 rtl:space-x-reverse mt-4">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {ticket.ticketNumber}
              </span>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${ticket.status === 'open' ? 'bg-blue-100 text-blue-800' : ticket.status === 'resolved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                {ticket.status === 'open' ? 'مفتوح' : ticket.status === 'resolved' ? 'تم الحل' : 'قيد المعالجة'}
              </span>
            </div>
          </div>
          
          <div className="p-6 max-h-96 overflow-y-auto">
            {/* Customer Info */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
              <img 
                src={ticket.customer.avatar} 
                alt={ticket.customer.name}
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <h4 className="font-semibold text-gray-800 dark:text-white rtl-font">
                  {ticket.customer.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {ticket.customer.email}
                </p>
              </div>
            </div>

            {/* Conversation */}
            <div className="space-y-4 mb-6">
              {ticket.responses.map((response) => (
                <div key={response.id} className={`flex ${response.sender === 'customer' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${
                    response.sender === 'customer' 
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white'
                  }`}>
                    <p className="text-sm rtl-font">{response.message}</p>
                    <p className="text-xs mt-2 opacity-70">
                      {response.senderName} - {response.timestamp}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Reply Section */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <div className="flex space-x-4 rtl:space-x-reverse">
                <textarea
                  value={replyMessage}
                  onChange={(e) => setReplyMessage(e.target.value)}
                  placeholder="اكتب ردك هنا..."
                  rows={3}
                  className="flex-1 p-4 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary bg-white dark:bg-gray-700 text-gray-800 dark:text-white rtl-font"
                />
                <button
                  onClick={handleSendReply}
                  className="bg-primary text-white px-6 py-3 rounded-xl hover:bg-primary/90 transition-colors flex items-center space-x-2 rtl:space-x-reverse"
                >
                  <Send className="w-4 h-4" />
                  <span>إرسال</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupportManagement;