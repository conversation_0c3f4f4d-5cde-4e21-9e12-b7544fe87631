import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/driver_themes.dart';
import '../providers/auth_provider.dart';
import '../screens/auth/otp_verification_screen.dart';

void main() {
  runApp(const SimpleLoginTestApp());
}

class SimpleLoginTestApp extends StatelessWidget {
  const SimpleLoginTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'Simple Login Test',
        debugShowCheckedModeBanner: false,
        theme: DriverThemes.lightTheme,
        home: const SimpleLoginScreen(),
      ),
    );
  }
}

class SimpleLoginScreen extends StatefulWidget {
  const SimpleLoginScreen({super.key});

  @override
  State<SimpleLoginScreen> createState() => _SimpleLoginScreenState();
}

class _SimpleLoginScreenState extends State<SimpleLoginScreen> {
  final _phoneController = TextEditingController(text: '**********');
  final _passwordController = TextEditingController(text: '123456');

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Simple Login Test'),
        backgroundColor: const Color(0xFF11B96F),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            return Column(
              children: [
                const Text(
                  'Test Login',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                
                const SizedBox(height: 20),
                
                TextField(
                  controller: _phoneController,
                  decoration: const InputDecoration(
                    labelText: 'Phone (**********)',
                    border: OutlineInputBorder(),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                TextField(
                  controller: _passwordController,
                  decoration: const InputDecoration(
                    labelText: 'Password (123456)',
                    border: OutlineInputBorder(),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: authProvider.isLoading ? null : _testLogin,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF11B96F),
                      foregroundColor: Colors.white,
                    ),
                    child: authProvider.isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text('Test Login'),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                if (authProvider.errorMessage != null)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Error: ${authProvider.errorMessage}',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                
                if (authProvider.lastPhone != null)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Login Success! Phone: ${authProvider.lastPhone}',
                          style: const TextStyle(color: Colors.green),
                        ),
                        const SizedBox(height: 10),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => OtpVerificationScreen(
                                  phoneNumber: authProvider.lastPhone!,
                                ),
                              ),
                            );
                          },
                          child: const Text('Go to OTP'),
                        ),
                      ],
                    ),
                  ),
                
                const SizedBox(height: 20),
                
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Debug Info:', style: TextStyle(fontWeight: FontWeight.bold)),
                      Text('Loading: ${authProvider.isLoading}'),
                      Text('Logged In: ${authProvider.isLoggedIn}'),
                      Text('Last Phone: ${authProvider.lastPhone ?? "None"}'),
                      Text('Error: ${authProvider.errorMessage ?? "None"}'),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _testLogin() async {
    print('=== STARTING LOGIN TEST ===');
    print('Phone: ${_phoneController.text}');
    print('Password: ${_passwordController.text}');
    
    final authProvider = context.read<AuthProvider>();
    
    try {
      final success = await authProvider.login(
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
      );
      
      print('=== LOGIN RESULT ===');
      print('Success: $success');
      print('Error: ${authProvider.errorMessage}');
      print('Last Phone: ${authProvider.lastPhone}');
      print('==================');
      
      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Login successful! You can now go to OTP'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      print('=== LOGIN EXCEPTION ===');
      print('Exception: $e');
      print('======================');
    }
  }
}
