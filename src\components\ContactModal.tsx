import React, { useState } from 'react';
import { MessageCircle, Phone, Mail, Send, User, MessageSquare } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface ContactModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ContactModal: React.FC<ContactModalProps> = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Contact form submitted:', formData);
    alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
    onClose();
    setFormData({ name: '', email: '', phone: '', subject: '', message: '' });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const contactMethods = [
    {
      icon: <Phone className="w-6 h-6" />,
      title: 'اتصل بنا',
      value: '+212 5XX-XXXXXX',
      action: () => window.open('tel:+2125XXXXXXX')
    },
    {
      icon: <Mail className="w-6 h-6" />,
      title: 'راسلنا',
      value: '<EMAIL>',
      action: () => window.open('mailto:<EMAIL>')
    },
    {
      icon: <MessageCircle className="w-6 h-6" />,
      title: 'دردشة مباشرة',
      value: 'متاح 24/7',
      action: () => alert('سيتم فتح الدردشة المباشرة...')
    }
  ];

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      />
      
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-2xl bg-white rounded-3xl shadow-2xl">
          <div className="p-8">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-accent mb-2 rtl-font">تواصل معنا</h2>
              <p className="text-gray-600 rtl-font">نحن هنا لمساعدتك في أي وقت</p>
            </div>

            <div className="text-center mb-6">
              <button
                onClick={() => {
                  onClose();
                  navigate('/contact');
                }}
                className="bg-primary text-white px-6 py-3 rounded-2xl font-bold hover:bg-primary/90 hover:scale-105 transition-all duration-300"
              >
                انتقل إلى صفحة التواصل الكاملة
              </button>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {/* Contact Methods */}
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-accent mb-4 rtl-font">طرق التواصل</h3>
                {contactMethods.map((method, index) => (
                  <button
                    key={index}
                    onClick={method.action}
                    className="w-full flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-colors text-right"
                  >
                    <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center text-primary">
                      {method.icon}
                    </div>
                    <div>
                      <h4 className="font-semibold text-accent rtl-font">{method.title}</h4>
                      <p className="text-sm text-gray-600">{method.value}</p>
                    </div>
                  </button>
                ))}
              </div>

              {/* Contact Form */}
              <div>
                <h3 className="text-lg font-bold text-accent mb-4 rtl-font">أرسل رسالة</h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="relative">
                    <User className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="الاسم الكامل"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full pr-12 pl-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors rtl-font"
                      required
                    />
                  </div>

                  <div className="relative">
                    <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="email"
                      placeholder="البريد الإلكتروني"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full pr-12 pl-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors rtl-font"
                      required
                    />
                  </div>

                  <div className="relative">
                    <Phone className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="tel"
                      placeholder="رقم الهاتف"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full pr-12 pl-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors rtl-font"
                    />
                  </div>

                  <div className="relative">
                    <MessageSquare className="absolute right-3 top-4 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="موضوع الرسالة"
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      className="w-full pr-12 pl-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors rtl-font"
                      required
                    />
                  </div>

                  <div className="relative">
                    <textarea
                      placeholder="اكتب رسالتك هنا..."
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      rows={4}
                      className="w-full p-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors resize-none rtl-font"
                      required
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-primary to-primary/90 text-white py-3 rounded-xl font-bold hover:shadow-lg hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse"
                  >
                    <Send className="w-5 h-5" />
                    <span>إرسال الرسالة</span>
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactModal;