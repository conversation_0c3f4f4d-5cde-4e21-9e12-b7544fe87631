import 'package:flutter/material.dart';
import '../models/delivery_order.dart';
import '../models/order_status.dart';

/// Widget for tracking order progress and status
class OrderTrackingWidget extends StatelessWidget {
  final DeliveryOrder order;
  final VoidCallback onActionPressed;
  final VoidCallback? onCancelPressed;

  const OrderTrackingWidget({
    super.key,
    required this.order,
    required this.onActionPressed,
    this.onCancelPressed,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Order header
          _buildOrderHeader(isDark),
          
          const SizedBox(height: 16),
          
          // Current status
          _buildCurrentStatus(isDark),
          
          const SizedBox(height: 20),
          
          // Order details
          _buildOrderDetails(isDark),
          
          const SizedBox(height: 20),
          
          // Action buttons
          _buildActionButtons(isDark),
        ],
      ),
    );
  }

  Widget _buildOrderHeader(bool isDark) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: order.status.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            order.status.icon,
            color: order.status.color,
            size: 24,
          ),
        ),
        
        const SizedBox(width: 12),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Order #${order.id.substring(order.id.length - 6)}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              
              const SizedBox(height: 4),
              
              Text(
                order.restaurantName,
                style: TextStyle(
                  fontSize: 14,
                  color: isDark ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
                ),
              ),
            ],
          ),
        ),
        
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: order.status.color,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            order.status.displayText,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentStatus(bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: order.status.color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: order.status.color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            order.status.icon,
            color: order.status.color,
            size: 20,
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.status.displayText,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: order.status.color,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                Text(
                  order.status.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails(bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Details',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Customer info
        _buildDetailRow(
          icon: Icons.person,
          label: 'Customer',
          value: order.customerName,
          isDark: isDark,
        ),
        
        const SizedBox(height: 8),
        
        // Address
        _buildDetailRow(
          icon: Icons.location_on,
          label: 'Address',
          value: order.customerAddress,
          isDark: isDark,
        ),
        
        const SizedBox(height: 8),
        
        // Items
        _buildDetailRow(
          icon: Icons.shopping_bag,
          label: 'Items',
          value: order.orderSummary,
          isDark: isDark,
        ),
        
        const SizedBox(height: 8),
        
        // Total
        _buildDetailRow(
          icon: Icons.payments,
          label: 'Total',
          value: order.formattedTotal,
          isDark: isDark,
        ),
      ],
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
    required bool isDark,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: isDark ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
        ),
        
        const SizedBox(width: 8),
        
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: isDark ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
            ),
          ),
        ),
        
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(bool isDark) {
    return Row(
      children: [
        // Cancel button (if allowed)
        if (order.status.canCancel && onCancelPressed != null)
          Expanded(
            child: OutlinedButton(
              onPressed: onCancelPressed,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 14),
                side: BorderSide(
                  color: isDark ? const Color(0xFF757575) : const Color(0xFFBDBDBD),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isDark ? const Color(0xFF757575) : const Color(0xFF424242),
                ),
              ),
            ),
          ),
        
        if (order.status.canCancel && onCancelPressed != null)
          const SizedBox(width: 12),
        
        // Main action button
        Expanded(
          flex: order.status.canCancel ? 1 : 2,
          child: ElevatedButton(
            onPressed: order.status == OrderStatus.delivered ? null : onActionPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: order.status.color,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 14),
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              order.status.actionButtonText,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
