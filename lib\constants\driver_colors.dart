import 'package:flutter/material.dart';

/// نظام الألوان المخصص لتطبيق السائق
/// يدعم الوضع الفاتح والمظلم مع ألوان محسنة للاستخدام أثناء القيادة
class DriverColors {
  // ========== الألوان الأساسية ==========
  
  /// الأخضر الرئيسي - لون العلامة التجارية
  static const Color primaryGreen = Color(0xFF00C853);
  static const Color primaryGreenDark = Color(0xFF00A843);
  static const Color primaryGreenLight = Color(0xFF5DFC8D);
  
  /// الأزرق للمعلومات والملاحة
  static const Color infoBlue = Color(0xFF2196F3);
  static const Color infoBlueDark = Color(0xFF1976D2);
  static const Color infoBlueLight = Color(0xFF64B5F6);
  
  /// البرتقالي للتحذيرات والانتظار
  static const Color warningOrange = Color(0xFFFF9800);
  static const Color warningOrangeDark = Color(0xFFE65100);
  static const Color warningOrangeLight = Color(0xFFFFCC02);
  
  /// الأحمر للأخطاء والإلغاء
  static const Color errorRed = Color(0xFFE53935);
  static const Color errorRedDark = Color(0xFFC62828);
  static const Color errorRedLight = Color(0xFFEF5350);
  
  /// الرمادي للنصوص الثانوية
  static const Color neutralGray = Color(0xFF757575);
  static const Color neutralGrayDark = Color(0xFF424242);
  static const Color neutralGrayLight = Color(0xFFBDBDBD);

  // ========== الوضع الفاتح (Light Mode) ==========
  
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    
    // الألوان الأساسية
    primary: primaryGreen,
    onPrimary: Colors.white,
    primaryContainer: Color(0xFFE8F5E8),
    onPrimaryContainer: Color(0xFF003300),
    
    // الألوان الثانوية
    secondary: infoBlue,
    onSecondary: Colors.white,
    secondaryContainer: Color(0xFFE3F2FD),
    onSecondaryContainer: Color(0xFF0D47A1),
    
    // الألوان الثالثية
    tertiary: warningOrange,
    onTertiary: Colors.white,
    tertiaryContainer: Color(0xFFFFF3E0),
    onTertiaryContainer: Color(0xFFE65100),
    
    // الخطأ
    error: errorRed,
    onError: Colors.white,
    errorContainer: Color(0xFFFFEBEE),
    onErrorContainer: Color(0xFFB71C1C),
    
    // الخلفيات
    surface: Colors.white,
    onSurface: Color(0xFF1C1C1C),
    surfaceContainerHighest: Color(0xFFF5F5F5),
    
    // الخطوط العريضة
    outline: Color(0xFFE0E0E0),
    outlineVariant: Color(0xFFF5F5F5),
    
    // الظلال
    shadow: Color(0x1F000000),
    scrim: Color(0x66000000),
    onSurfaceVariant: Color(0xFF424242),
    
    // الألوان العكسية
    inverseSurface: Color(0xFF2C2C2C),
    onInverseSurface: Color(0xFFF0F0F0),
    inversePrimary: primaryGreenLight,
  );

  // ========== الوضع المظلم (Dark Mode) ==========
  
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    
    // الألوان الأساسية
    primary: primaryGreenLight,
    onPrimary: Color(0xFF003300),
    primaryContainer: Color(0xFF00A843),
    onPrimaryContainer: Color(0xFFE8F5E8),
    
    // الألوان الثانوية
    secondary: infoBlueLight,
    onSecondary: Color(0xFF0D47A1),
    secondaryContainer: Color(0xFF1976D2),
    onSecondaryContainer: Color(0xFFE3F2FD),
    
    // الألوان الثالثية
    tertiary: warningOrangeLight,
    onTertiary: Color(0xFFE65100),
    tertiaryContainer: warningOrangeDark,
    onTertiaryContainer: Color(0xFFFFF3E0),
    
    // الخطأ
    error: errorRedLight,
    onError: Color(0xFFB71C1C),
    errorContainer: errorRedDark,
    onErrorContainer: Color(0xFFFFEBEE),
    
    // الخلفيات
    surface: Color(0xFF121212),
    onSurface: Color(0xFFE0E0E0),
    surfaceContainerHighest: Color(0xFF2C2C2C),
    
    // الخطوط العريضة
    outline: Color(0xFF424242),
    outlineVariant: Color(0xFF2C2C2C),
    
    // الظلال
    shadow: Color(0x3F000000),
    scrim: Color(0x80000000),
    onSurfaceVariant: Color(0xFFBDBDBD),
    
    // الألوان العكسية
    inverseSurface: Color(0xFFE0E0E0),
    onInverseSurface: Color(0xFF1C1C1C),
    inversePrimary: primaryGreen,
  );

  // ========== ألوان مخصصة للسائق ==========
  
  /// ألوان حالة الطلبات
  static const Color orderPending = warningOrange;
  static const Color orderAccepted = primaryGreen;
  static const Color orderInProgress = infoBlue;
  static const Color orderCompleted = Color(0xFF4CAF50);
  static const Color orderCancelled = errorRed;
  
  /// ألوان حالة السائق
  static const Color driverOnline = primaryGreen;
  static const Color driverOffline = neutralGray;
  static const Color driverBusy = warningOrange;
  
  /// ألوان الأرباح
  static const Color earningsPositive = Color(0xFF4CAF50);
  static const Color earningsNeutral = neutralGray;
  static const Color earningsHighlight = Color(0xFFFFD700);
  
  /// ألوان الخريطة والملاحة
  static const Color mapRoute = infoBlue;
  static const Color mapDestination = errorRed;
  static const Color mapCurrentLocation = primaryGreen;
  
  /// ألوان التقييم
  static const Color ratingExcellent = Color(0xFF4CAF50);
  static const Color ratingGood = Color(0xFF8BC34A);
  static const Color ratingAverage = warningOrange;
  static const Color ratingPoor = errorRed;

  // ========== ألوان الوضع الليلي للقيادة ==========
  
  /// ألوان محسنة للقيادة الليلية - تقلل إجهاد العين
  static const Color nightModeBackground = Color(0xFF0D1117);
  static const Color nightModeSurface = Color(0xFF161B22);
  static const Color nightModeCard = Color(0xFF21262D);
  static const Color nightModeText = Color(0xFFF0F6FC);
  static const Color nightModeTextSecondary = Color(0xFF8B949E);
  static const Color nightModeAccent = Color(0xFF58A6FF);
  
  /// ألوان التباين العالي للأمان
  static const Color highContrastGreen = Color(0xFF00FF41);
  static const Color highContrastRed = Color(0xFFFF4444);
  static const Color highContrastYellow = Color(0xFFFFFF00);
  static const Color highContrastWhite = Color(0xFFFFFFFF);
  static const Color highContrastBlack = Color(0xFF000000);

  // ========== دوال مساعدة ==========
  
  /// الحصول على لون حالة الطلب
  static Color getOrderStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'في الانتظار':
        return orderPending;
      case 'accepted':
      case 'مقبول':
        return orderAccepted;
      case 'preparing':
      case 'قيد التحضير':
      case 'ready':
      case 'جاهز':
      case 'picked_up':
      case 'تم الاستلام':
      case 'on_the_way':
      case 'في الطريق':
        return orderInProgress;
      case 'delivered':
      case 'تم التسليم':
        return orderCompleted;
      case 'cancelled':
      case 'ملغي':
        return orderCancelled;
      default:
        return neutralGray;
    }
  }
  
  /// الحصول على لون حالة السائق
  static Color getDriverStatusColor(bool isOnline, bool isBusy) {
    if (!isOnline) return driverOffline;
    if (isBusy) return driverBusy;
    return driverOnline;
  }
  
  /// الحصول على لون التقييم
  static Color getRatingColor(double rating) {
    if (rating >= 4.5) return ratingExcellent;
    if (rating >= 4.0) return ratingGood;
    if (rating >= 3.0) return ratingAverage;
    return ratingPoor;
  }
  
  /// الحصول على لون مع شفافية
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }
  
  /// الحصول على لون متباين للنص
  static Color getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
