import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';

/// Contact Us screen with multiple contact methods
class ContactUsScreen extends StatefulWidget {
  const ContactUsScreen({super.key});

  @override
  State<ContactUsScreen> createState() => _ContactUsScreenState();
}

class _ContactUsScreenState extends State<ContactUsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _messageController = TextEditingController();
  String _selectedSubject = 'general';
  bool _isSubmitting = false;

  final List<ContactSubject> _subjects = [
    ContactSubject(
      id: 'general',
      nameEn: 'General Inquiry',
      nameAr: 'استفسار عام',
    ),
    ContactSubject(
      id: 'technical',
      nameEn: 'Technical Support',
      nameAr: 'الدعم التقني',
    ),
    ContactSubject(
      id: 'payment',
      nameEn: 'Payment Issue',
      nameAr: 'مشكلة في الدفع',
    ),
    ContactSubject(
      id: 'account',
      nameEn: 'Account Problem',
      nameAr: 'مشكلة في الحساب',
    ),
    ContactSubject(
      id: 'feedback',
      nameEn: 'Feedback',
      nameAr: 'ملاحظات',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _messageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Contact Us', 'اتصل بنا'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Header
                _buildHeader(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Quick Contact Methods
                _buildQuickContactMethods(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Contact Form
                _buildContactForm(isDark, languageProvider),
                
                const SizedBox(height: 20),
                
                // Office Information
                _buildOfficeInfo(isDark, languageProvider),
                
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF11B96F),
            const Color(0xFF11B96F).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF11B96F).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.contact_support,
            size: 60,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('Get in Touch', 'تواصل معنا'),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'We\'re here to help you with any questions or concerns',
              'نحن هنا لمساعدتك في أي أسئلة أو مخاوف'
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickContactMethods(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageProvider.getText('Quick Contact', 'تواصل سريع'),
            style: TextStyle(
              fontSize: DriverTypography.titleMedium,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF11B96F),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildContactMethod(
                  icon: Icons.phone,
                  title: languageProvider.getText('Call', 'اتصال'),
                  subtitle: '+966 11 123 4567',
                  color: Colors.green,
                  onTap: () => _makePhoneCall('+966111234567'),
                  isDark: isDark,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildContactMethod(
                  icon: Icons.email,
                  title: languageProvider.getText('Email', 'بريد إلكتروني'),
                  subtitle: '<EMAIL>',
                  color: Colors.blue,
                  onTap: () => _sendEmail('<EMAIL>'),
                  isDark: isDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildContactMethod(
                  icon: Icons.chat,
                  title: languageProvider.getText('WhatsApp', 'واتساب'),
                  subtitle: '+966 50 123 4567',
                  color: Colors.green[600]!,
                  onTap: () => _openWhatsApp('+966501234567'),
                  isDark: isDark,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildContactMethod(
                  icon: Icons.telegram,
                  title: languageProvider.getText('Telegram', 'تليجرام'),
                  subtitle: '@wasslti_support',
                  color: Colors.blue[600]!,
                  onTap: () => _openTelegram('@wasslti_support'),
                  isDark: isDark,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContactMethod({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: isDark ? Colors.grey[400] : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactForm(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              languageProvider.getText('Send us a Message', 'أرسل لنا رسالة'),
              style: TextStyle(
                fontSize: DriverTypography.titleMedium,
                fontWeight: FontWeight.bold,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 20),
            
            // Name Field
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: languageProvider.getText('Full Name', 'الاسم الكامل'),
                prefixIcon: const Icon(Icons.person, color: Color(0xFF11B96F)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return languageProvider.getText('Please enter your name', 'يرجى إدخال اسمك');
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // Email Field
            TextFormField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                labelText: languageProvider.getText('Email', 'البريد الإلكتروني'),
                prefixIcon: const Icon(Icons.email, color: Color(0xFF11B96F)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return languageProvider.getText('Please enter your email', 'يرجى إدخال بريدك الإلكتروني');
                }
                if (!value.contains('@')) {
                  return languageProvider.getText('Please enter a valid email', 'يرجى إدخال بريد إلكتروني صحيح');
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // Phone Field
            TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                labelText: languageProvider.getText('Phone Number', 'رقم الهاتف'),
                prefixIcon: const Icon(Icons.phone, color: Color(0xFF11B96F)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Subject Dropdown
            DropdownButtonFormField<String>(
              value: _selectedSubject,
              decoration: InputDecoration(
                labelText: languageProvider.getText('Subject', 'الموضوع'),
                prefixIcon: const Icon(Icons.subject, color: Color(0xFF11B96F)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
                ),
              ),
              items: _subjects.map((subject) {
                return DropdownMenuItem(
                  value: subject.id,
                  child: Text(
                    languageProvider.isArabic ? subject.nameAr : subject.nameEn,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedSubject = value!;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Message Field
            TextFormField(
              controller: _messageController,
              maxLines: 5,
              decoration: InputDecoration(
                labelText: languageProvider.getText('Message', 'الرسالة'),
                prefixIcon: const Icon(Icons.message, color: Color(0xFF11B96F)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF11B96F), width: 2),
                ),
                alignLabelWithHint: true,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return languageProvider.getText('Please enter your message', 'يرجى إدخال رسالتك');
                }
                return null;
              },
            ),
            
            const SizedBox(height: 20),
            
            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitForm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF11B96F),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        languageProvider.getText('Send Message', 'إرسال الرسالة'),
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfficeInfo(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            languageProvider.getText('Office Information', 'معلومات المكتب'),
            style: TextStyle(
              fontSize: DriverTypography.titleMedium,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          _buildInfoRow(
            icon: Icons.location_on,
            title: languageProvider.getText('Address', 'العنوان'),
            content: languageProvider.getText(
              'King Fahd Road, Riyadh 12345, Saudi Arabia',
              'طريق الملك فهد، الرياض 12345، المملكة العربية السعودية'
            ),
            isDark: isDark,
          ),

          const SizedBox(height: 12),

          _buildInfoRow(
            icon: Icons.access_time,
            title: languageProvider.getText('Working Hours', 'ساعات العمل'),
            content: languageProvider.getText(
              'Sunday - Thursday: 9:00 AM - 6:00 PM',
              'الأحد - الخميس: 9:00 ص - 6:00 م'
            ),
            isDark: isDark,
          ),

          const SizedBox(height: 12),

          _buildInfoRow(
            icon: Icons.support,
            title: languageProvider.getText('Support Hours', 'ساعات الدعم'),
            content: languageProvider.getText(
              '24/7 Emergency Support Available',
              'دعم الطوارئ متاح 24/7'
            ),
            isDark: isDark,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String content,
    required bool isDark,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: const Color(0xFF11B96F),
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                content,
                style: TextStyle(
                  fontSize: 13,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    }
  }

  Future<void> _sendEmail(String email) async {
    final Uri launchUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=Support Request&body=Hello Wasslti Support Team,',
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    }
  }

  Future<void> _openWhatsApp(String phoneNumber) async {
    final Uri launchUri = Uri.parse('https://wa.me/$phoneNumber');
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    }
  }

  Future<void> _openTelegram(String username) async {
    final Uri launchUri = Uri.parse('https://t.me/$username');
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.read<LanguageProvider>().getText(
                'Message sent successfully! We will get back to you soon.',
                'تم إرسال الرسالة بنجاح! سنتواصل معك قريباً.'
              )
            ),
            backgroundColor: const Color(0xFF11B96F),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );

        // Clear form
        _nameController.clear();
        _emailController.clear();
        _phoneController.clear();
        _messageController.clear();
        setState(() {
          _selectedSubject = 'general';
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.read<LanguageProvider>().getText(
                'Failed to send message. Please try again.',
                'فشل في إرسال الرسالة. يرجى المحاولة مرة أخرى.'
              )
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}

class ContactSubject {
  final String id;
  final String nameEn;
  final String nameAr;

  ContactSubject({
    required this.id,
    required this.nameEn,
    required this.nameAr,
  });
}
