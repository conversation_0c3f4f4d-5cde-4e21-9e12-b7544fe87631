import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';

class FeedbackListWidget extends StatelessWidget {
  const FeedbackListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Feedback',
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              <PERSON>(
                children: [
                  _buildFilterButton('Contact', true),
                  const SizedBox(width: 8),
                  _buildFilterButton('Low Rating', false),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Feedback Grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 4,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.8,
            children: [
              _buildFeedbackCard(
                name: 'Thomas Smith',
                date: 'User since 2023',
                avatar: 'TS',
                rating: 4.5,
                comment: 'Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt.',
                foodType: 'Burger',
                foodRating: 4.5,
                color: Colors.green.shade100,
              ),
              _buildFeedbackCard(
                name: 'Eleanor Pena',
                date: 'User since 2023',
                avatar: 'EP',
                rating: 4.0,
                comment: 'Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt.',
                foodType: 'Burger',
                foodRating: 4.0,
                color: Colors.blue.shade100,
              ),
              _buildFeedbackCard(
                name: 'Albert Flores',
                date: 'User since 2023',
                avatar: 'AF',
                rating: 3.5,
                comment: 'Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt.',
                foodType: 'Burger',
                foodRating: 3.5,
                color: Colors.orange.shade100,
              ),
              _buildFeedbackCard(
                name: 'Kristin Watson',
                date: 'User since 2023',
                avatar: 'KW',
                rating: 5.0,
                comment: 'Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt.',
                foodType: 'Burger',
                foodRating: 5.0,
                color: Colors.purple.shade100,
              ),
              _buildFeedbackCard(
                name: 'Jerome Bell',
                date: 'User since 2023',
                avatar: 'JB',
                rating: 4.2,
                comment: 'Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt.',
                foodType: 'Burger',
                foodRating: 4.2,
                color: Colors.teal.shade100,
              ),
              _buildFeedbackCard(
                name: 'Cody Fisher',
                date: 'User since 2023',
                avatar: 'CF',
                rating: 3.8,
                comment: 'Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt.',
                foodType: 'Burger',
                foodRating: 3.8,
                color: Colors.indigo.shade100,
              ),
              _buildFeedbackCard(
                name: 'Theresa Webb',
                date: 'User since 2023',
                avatar: 'TW',
                rating: 4.7,
                comment: 'Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt.',
                foodType: 'Burger',
                foodRating: 4.7,
                color: Colors.pink.shade100,
              ),
              _buildFeedbackCard(
                name: 'Jane Cooper',
                date: 'User since 2023',
                avatar: 'JC',
                rating: 4.1,
                comment: 'Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt.',
                foodType: 'Burger',
                foodRating: 4.1,
                color: Colors.amber.shade100,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton(String text, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary : AppColors.white,
        border: Border.all(
          color: isSelected ? AppColors.primary : AppColors.inputBorder,
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        text,
        style: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: isSelected ? AppColors.white : AppColors.textSecondary,
        ),
      ),
    );
  }

  Widget _buildFeedbackCard({
    required String name,
    required String date,
    required String avatar,
    required double rating,
    required String comment,
    required String foodType,
    required double foodRating,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Info
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.white,
                child: Text(
                  avatar,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      date,
                      style: GoogleFonts.inter(
                        fontSize: 10,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Comment
          Text(
            comment,
            style: GoogleFonts.inter(
              fontSize: 10,
              color: AppColors.textSecondary,
              height: 1.4,
            ),
            maxLines: 4,
            overflow: TextOverflow.ellipsis,
          ),
          
          const Spacer(),
          
          // Food Rating
          Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  Icons.fastfood,
                  color: AppColors.white,
                  size: 12,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      foodType,
                      style: GoogleFonts.inter(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Row(
                      children: [
                        ...List.generate(5, (index) {
                          return Icon(
                            Icons.star,
                            size: 8,
                            color: index < foodRating.floor()
                                ? Colors.amber
                                : AppColors.inputBorder,
                          );
                        }),
                        const SizedBox(width: 4),
                        Text(
                          foodRating.toString(),
                          style: GoogleFonts.inter(
                            fontSize: 8,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
