import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../dashboard/presentation/widgets/sidebar_widget.dart';
import '../widgets/order_card_widget.dart';
import '../widgets/order_tracking_widget.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  String searchQuery = '';
  String selectedStatus = 'All';
  Order? selectedOrder;

  final List<String> statusFilters = ['All', 'Pending', 'Preparing', 'Ready', 'Delivered'];

  final List<Order> orders = [
    Order(
      id: '#1',
      customerName: 'Jhon Smith',
      items: [
        OrderItem(name: 'Sliced lamb', price: 5.59, image: 'assets/images/lamb.png'),
        OrderItem(name: 'Mongolian beef', price: 5.59, image: 'assets/images/beef.png'),
      ],
      status: OrderStatus.pending,
      deliveryTime: '10 Min',
      distance: '2.5 Km',
      total: 11.18,
      rating: 5.0,
    ),
    Order(
      id: '#2',
      customerName: 'Jane Doe',
      items: [
        OrderItem(name: 'Sliced lamb', price: 5.59, image: 'assets/images/lamb.png'),
        OrderItem(name: 'Mongolian beef', price: 5.59, image: 'assets/images/beef.png'),
      ],
      status: OrderStatus.preparing,
      deliveryTime: '10 Min',
      distance: '2.5 Km',
      total: 11.18,
      rating: 5.0,
    ),
    Order(
      id: '#3',
      customerName: 'Mike Johnson',
      items: [
        OrderItem(name: 'Mongolian beef', price: 5.59, image: 'assets/images/beef.png'),
      ],
      status: OrderStatus.rejected,
      deliveryTime: '10 Min',
      distance: '2.5 Km',
      total: 11.18,
      rating: 5.0,
    ),
    Order(
      id: '#4',
      customerName: 'Sarah Wilson',
      items: [
        OrderItem(name: 'Sliced lamb', price: 5.59, image: 'assets/images/lamb.png'),
        OrderItem(name: 'Mongolian beef', price: 5.59, image: 'assets/images/beef.png'),
      ],
      status: OrderStatus.ready,
      deliveryTime: '10 Min',
      distance: '2.5 Km',
      total: 11.18,
      rating: 5.0,
    ),
    Order(
      id: '#5',
      customerName: 'Tom Brown',
      items: [
        OrderItem(name: 'Sliced lamb', price: 5.59, image: 'assets/images/lamb.png'),
        OrderItem(name: 'Mongolian beef', price: 5.59, image: 'assets/images/beef.png'),
      ],
      status: OrderStatus.preparing,
      deliveryTime: '10 Min',
      distance: '2.5 Km',
      total: 11.18,
      rating: 5.0,
    ),
    Order(
      id: '#6',
      customerName: 'Lisa Davis',
      items: [
        OrderItem(name: 'Sliced lamb', price: 5.59, image: 'assets/images/lamb.png'),
      ],
      status: OrderStatus.ready,
      deliveryTime: '10 Min',
      distance: '2.5 Km',
      total: 11.18,
      rating: 5.0,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      body: Row(
        children: [
          // Sidebar
          const SidebarWidget(),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Content
                Expanded(
                  child: Row(
                    children: [
                      // Orders List
                      Expanded(
                        flex: 2,
                        child: Container(
                          color: AppColors.backgroundLight,
                          child: Column(
                            children: [
                              // Status Filters
                              _buildStatusFilters(),
                              
                              // Orders Grid
                              Expanded(
                                child: _buildOrdersGrid(),
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      // Order Tracking Panel
                      if (selectedOrder != null)
                        Container(
                          width: 350,
                          decoration: const BoxDecoration(
                            color: AppColors.white,
                            border: Border(
                              left: BorderSide(
                                color: AppColors.inputBorder,
                                width: 1,
                              ),
                            ),
                          ),
                          child: OrderTrackingWidget(
                            order: selectedOrder!,
                            onClose: () {
                              setState(() {
                                selectedOrder = null;
                              });
                            },
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Row(
            children: [
              Text(
                'Food order',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(width: 8),
              const Text('😊', style: TextStyle(fontSize: 20)),
            ],
          ),
          
          const Spacer(),
          
          // Search Bar
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: GoogleFonts.inter(
                  color: AppColors.textHint,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textHint,
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Notifications
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.notifications_outlined,
                    color: AppColors.textSecondary,
                    size: 20,
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // User Profile
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primary,
                child: Text(
                  'JS',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Jhon Smith',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'User',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: statusFilters.map((status) {
          final isSelected = selectedStatus == status;
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(
                status,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isSelected ? AppColors.white : AppColors.textSecondary,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  selectedStatus = status;
                });
              },
              backgroundColor: AppColors.white,
              selectedColor: AppColors.primary,
              checkmarkColor: AppColors.white,
              side: BorderSide(
                color: isSelected ? AppColors.primary : AppColors.inputBorder,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildOrdersGrid() {
    final filteredOrders = _filterOrders();
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: filteredOrders.length,
        itemBuilder: (context, index) {
          final order = filteredOrders[index];
          return OrderCardWidget(
            order: order,
            onTap: () {
              setState(() {
                selectedOrder = order;
              });
            },
          );
        },
      ),
    );
  }

  List<Order> _filterOrders() {
    List<Order> filtered = orders;

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((order) =>
          order.customerName.toLowerCase().contains(searchQuery.toLowerCase()) ||
          order.id.toLowerCase().contains(searchQuery.toLowerCase())).toList();
    }

    // Apply status filter
    if (selectedStatus != 'All') {
      final status = _getOrderStatusFromString(selectedStatus);
      filtered = filtered.where((order) => order.status == status).toList();
    }

    return filtered;
  }

  OrderStatus _getOrderStatusFromString(String status) {
    switch (status) {
      case 'Pending':
        return OrderStatus.pending;
      case 'Preparing':
        return OrderStatus.preparing;
      case 'Ready':
        return OrderStatus.ready;
      case 'Delivered':
        return OrderStatus.delivered;
      default:
        return OrderStatus.pending;
    }
  }
}

enum OrderStatus { pending, preparing, ready, delivered, rejected }

class Order {
  final String id;
  final String customerName;
  final List<OrderItem> items;
  final OrderStatus status;
  final String deliveryTime;
  final String distance;
  final double total;
  final double rating;

  Order({
    required this.id,
    required this.customerName,
    required this.items,
    required this.status,
    required this.deliveryTime,
    required this.distance,
    required this.total,
    required this.rating,
  });
}

class OrderItem {
  final String name;
  final double price;
  final String image;

  OrderItem({
    required this.name,
    required this.price,
    required this.image,
  });
}
