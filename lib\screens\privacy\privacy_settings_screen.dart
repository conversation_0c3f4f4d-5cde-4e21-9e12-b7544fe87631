import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/driver_typography.dart';
import '../../providers/language_provider.dart';
import '../legal/privacy_policy_screen.dart';

/// Advanced Privacy Settings screen
class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  // Privacy settings
  bool _locationTracking = true;
  bool _dataCollection = true;
  bool _personalizedAds = false;
  bool _analyticsSharing = true;
  bool _crashReporting = true;
  bool _performanceData = true;
  bool _marketingEmails = false;
  bool _pushNotifications = true;
  bool _smsNotifications = false;
  bool _profileVisibility = true;
  bool _activityStatus = true;
  bool _shareWithPartners = false;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final languageProvider = context.watch<LanguageProvider>();

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
        appBar: AppBar(
          backgroundColor: isDark ? const Color(0xFF0F231A) : const Color(0xFFF5F5F5),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              languageProvider.isArabic ? Icons.arrow_forward : Icons.arrow_back,
              color: isDark ? Colors.white : Colors.black87,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            languageProvider.getText('Privacy Settings', 'إعدادات الخصوصية'),
            style: TextStyle(
              fontSize: DriverTypography.titleLarge,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.info_outline, color: Colors.blue),
              onPressed: () => Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
              ),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header
              _buildHeader(isDark, languageProvider),
              
              const SizedBox(height: 20),
              
              // Location & Tracking
              _buildSection(
                title: languageProvider.getText('Location & Tracking', 'الموقع والتتبع'),
                icon: Icons.location_on_outlined,
                color: Colors.red,
                children: [
                  _buildSettingTile(
                    title: languageProvider.getText('Location Tracking', 'تتبع الموقع'),
                    subtitle: languageProvider.getText(
                      'Allow app to track your location for trip navigation',
                      'السماح للتطبيق بتتبع موقعك للتنقل في الرحلات'
                    ),
                    value: _locationTracking,
                    onChanged: (value) => setState(() => _locationTracking = value),
                    isDark: isDark,
                    isRequired: true,
                  ),
                ],
                isDark: isDark,
              ),
              
              const SizedBox(height: 16),
              
              // Data Collection
              _buildSection(
                title: languageProvider.getText('Data Collection', 'جمع البيانات'),
                icon: Icons.data_usage_outlined,
                color: Colors.orange,
                children: [
                  _buildSettingTile(
                    title: languageProvider.getText('Usage Data Collection', 'جمع بيانات الاستخدام'),
                    subtitle: languageProvider.getText(
                      'Help improve the app by sharing usage data',
                      'ساعد في تحسين التطبيق بمشاركة بيانات الاستخدام'
                    ),
                    value: _dataCollection,
                    onChanged: (value) => setState(() => _dataCollection = value),
                    isDark: isDark,
                  ),
                  _buildSettingTile(
                    title: languageProvider.getText('Analytics Sharing', 'مشاركة التحليلات'),
                    subtitle: languageProvider.getText(
                      'Share anonymous analytics to improve services',
                      'مشاركة التحليلات المجهولة لتحسين الخدمات'
                    ),
                    value: _analyticsSharing,
                    onChanged: (value) => setState(() => _analyticsSharing = value),
                    isDark: isDark,
                  ),
                  _buildSettingTile(
                    title: languageProvider.getText('Crash Reporting', 'تقارير الأعطال'),
                    subtitle: languageProvider.getText(
                      'Automatically send crash reports to help fix bugs',
                      'إرسال تقارير الأعطال تلقائياً لمساعدة في إصلاح الأخطاء'
                    ),
                    value: _crashReporting,
                    onChanged: (value) => setState(() => _crashReporting = value),
                    isDark: isDark,
                  ),
                  _buildSettingTile(
                    title: languageProvider.getText('Performance Data', 'بيانات الأداء'),
                    subtitle: languageProvider.getText(
                      'Share app performance data for optimization',
                      'مشاركة بيانات أداء التطبيق للتحسين'
                    ),
                    value: _performanceData,
                    onChanged: (value) => setState(() => _performanceData = value),
                    isDark: isDark,
                  ),
                ],
                isDark: isDark,
              ),
              
              const SizedBox(height: 16),
              
              // Marketing & Communications
              _buildSection(
                title: languageProvider.getText('Marketing & Communications', 'التسويق والاتصالات'),
                icon: Icons.campaign_outlined,
                color: Colors.purple,
                children: [
                  _buildSettingTile(
                    title: languageProvider.getText('Personalized Ads', 'إعلانات مخصصة'),
                    subtitle: languageProvider.getText(
                      'Show ads based on your interests and activity',
                      'عرض إعلانات بناءً على اهتماماتك ونشاطك'
                    ),
                    value: _personalizedAds,
                    onChanged: (value) => setState(() => _personalizedAds = value),
                    isDark: isDark,
                  ),
                  _buildSettingTile(
                    title: languageProvider.getText('Marketing Emails', 'رسائل تسويقية'),
                    subtitle: languageProvider.getText(
                      'Receive promotional emails and offers',
                      'استقبال رسائل ترويجية وعروض'
                    ),
                    value: _marketingEmails,
                    onChanged: (value) => setState(() => _marketingEmails = value),
                    isDark: isDark,
                  ),
                  _buildSettingTile(
                    title: languageProvider.getText('Push Notifications', 'الإشعارات'),
                    subtitle: languageProvider.getText(
                      'Receive push notifications for updates',
                      'استقبال إشعارات للتحديثات'
                    ),
                    value: _pushNotifications,
                    onChanged: (value) => setState(() => _pushNotifications = value),
                    isDark: isDark,
                  ),
                  _buildSettingTile(
                    title: languageProvider.getText('SMS Notifications', 'رسائل نصية'),
                    subtitle: languageProvider.getText(
                      'Receive SMS notifications for important updates',
                      'استقبال رسائل نصية للتحديثات المهمة'
                    ),
                    value: _smsNotifications,
                    onChanged: (value) => setState(() => _smsNotifications = value),
                    isDark: isDark,
                  ),
                ],
                isDark: isDark,
              ),
              
              const SizedBox(height: 16),
              
              // Profile & Visibility
              _buildSection(
                title: languageProvider.getText('Profile & Visibility', 'الملف الشخصي والظهور'),
                icon: Icons.visibility_outlined,
                color: const Color(0xFF11B96F),
                children: [
                  _buildSettingTile(
                    title: languageProvider.getText('Profile Visibility', 'ظهور الملف الشخصي'),
                    subtitle: languageProvider.getText(
                      'Allow customers to see your profile information',
                      'السماح للعملاء برؤية معلومات ملفك الشخصي'
                    ),
                    value: _profileVisibility,
                    onChanged: (value) => setState(() => _profileVisibility = value),
                    isDark: isDark,
                    isRequired: true,
                  ),
                  _buildSettingTile(
                    title: languageProvider.getText('Activity Status', 'حالة النشاط'),
                    subtitle: languageProvider.getText(
                      'Show when you are online and available',
                      'إظهار متى تكون متصلاً ومتاحاً'
                    ),
                    value: _activityStatus,
                    onChanged: (value) => setState(() => _activityStatus = value),
                    isDark: isDark,
                  ),
                ],
                isDark: isDark,
              ),
              
              const SizedBox(height: 16),
              
              // Third-Party Sharing
              _buildSection(
                title: languageProvider.getText('Third-Party Sharing', 'المشاركة مع أطراف ثالثة'),
                icon: Icons.share_outlined,
                color: Colors.blue,
                children: [
                  _buildSettingTile(
                    title: languageProvider.getText('Share with Partners', 'مشاركة مع الشركاء'),
                    subtitle: languageProvider.getText(
                      'Share data with trusted business partners',
                      'مشاركة البيانات مع شركاء الأعمال الموثوقين'
                    ),
                    value: _shareWithPartners,
                    onChanged: (value) => setState(() => _shareWithPartners = value),
                    isDark: isDark,
                  ),
                ],
                isDark: isDark,
              ),
              
              const SizedBox(height: 20),
              
              // Privacy Actions
              _buildPrivacyActions(isDark, languageProvider),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDark, LanguageProvider languageProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue,
            Colors.blue.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.privacy_tip,
            size: 60,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            languageProvider.getText('Your Privacy Matters', 'خصوصيتك مهمة'),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            languageProvider.getText(
              'Control how your data is collected and used',
              'تحكم في كيفية جمع واستخدام بياناتك'
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
    required bool isDark,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required bool isDark,
    bool isRequired = false,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    if (isRequired) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'Required',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Switch(
            value: value,
            onChanged: isRequired ? null : onChanged,
            activeColor: const Color(0xFF11B96F),
            inactiveThumbColor: isDark ? Colors.grey[600] : Colors.grey[400],
            inactiveTrackColor: isDark ? Colors.grey[800] : Colors.grey[300],
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacyActions(bool isDark, LanguageProvider languageProvider) {
    return Column(
      children: [
        // Data Export
        _buildActionCard(
          icon: Icons.download_outlined,
          title: languageProvider.getText('Export My Data', 'تصدير بياناتي'),
          subtitle: languageProvider.getText(
            'Download a copy of your personal data',
            'تحميل نسخة من بياناتك الشخصية'
          ),
          color: const Color(0xFF11B96F),
          onTap: () => _handleDataExport(),
          isDark: isDark,
        ),

        const SizedBox(height: 12),

        // Delete Account
        _buildActionCard(
          icon: Icons.delete_outline,
          title: languageProvider.getText('Delete My Account', 'حذف حسابي'),
          subtitle: languageProvider.getText(
            'Permanently delete your account and data',
            'حذف حسابك وبياناتك نهائياً'
          ),
          color: Colors.red,
          onTap: () => _handleDeleteAccount(),
          isDark: isDark,
        ),

        const SizedBox(height: 12),

        // Privacy Policy
        _buildActionCard(
          icon: Icons.policy_outlined,
          title: languageProvider.getText('Privacy Policy', 'سياسة الخصوصية'),
          subtitle: languageProvider.getText(
            'Read our complete privacy policy',
            'اقرأ سياسة الخصوصية الكاملة'
          ),
          color: Colors.blue,
          onTap: () => Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
          ),
          isDark: isDark,
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF1B3B2E) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _handleDataExport() {
    final languageProvider = context.read<LanguageProvider>();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(languageProvider.getText('Export Data', 'تصدير البيانات')),
        content: Text(
          languageProvider.getText(
            'We will prepare your data export and send it to your registered email address within 24 hours.',
            'سنقوم بإعداد تصدير بياناتك وإرسالها إلى عنوان بريدك الإلكتروني المسجل خلال 24 ساعة.'
          )
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageProvider.getText('Cancel', 'إلغاء')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    languageProvider.getText(
                      'Data export request submitted',
                      'تم تقديم طلب تصدير البيانات'
                    )
                  ),
                  backgroundColor: const Color(0xFF11B96F),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: const Color(0xFF11B96F)),
            child: Text(languageProvider.getText('Export', 'تصدير')),
          ),
        ],
      ),
    );
  }

  void _handleDeleteAccount() {
    final languageProvider = context.read<LanguageProvider>();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          languageProvider.getText('Delete Account', 'حذف الحساب'),
          style: const TextStyle(color: Colors.red),
        ),
        content: Text(
          languageProvider.getText(
            'This action cannot be undone. All your data will be permanently deleted.',
            'هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بياناتك نهائياً.'
          )
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(languageProvider.getText('Cancel', 'إلغاء')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    languageProvider.getText(
                      'Account deletion request submitted',
                      'تم تقديم طلب حذف الحساب'
                    )
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(languageProvider.getText('Delete', 'حذف')),
          ),
        ],
      ),
    );
  }
}
